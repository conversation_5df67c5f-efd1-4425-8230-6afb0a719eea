{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "allowJs": false,
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "typeRoots": [
      "./node_modules/@types",
      "./types"
    ],
    "paths": {
      "@/*": ["./src/*"]
    },
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "types/**/*.d.ts", "build.ts"],

  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    },
    "files": true,
    "typeCheck": false
  }
}