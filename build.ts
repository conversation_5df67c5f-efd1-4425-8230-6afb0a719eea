/**
 * 因为vite目前不支持iife的打包方式下，多个文件的问题，因此需要自己写一个build.ts文件来实现
 */

import { UserConfig, defineConfig } from "vite";
import path from 'path';
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import { build } from "vite"
import fs from 'fs'
import JSZip from 'jszip'
import manifest from './src/manifest.json'

const distPath = path.resolve(__dirname, './dist')

function processArgs() {
  const args = process.argv
  const result: { [key: string]: any } = {}
  // 删除前两个参数，第一个是node，第二个是build.ts
  args.splice(0, 2)
  args.forEach((arg: string) => {
    const arr = arg.split('=')
    if (arr.length === 2) {
      result[arr[0]] = arr[1]
    }
  })
  return result
}

const params = processArgs()
const mode = params['--mode']

function getConfig(lib: any, mode: string): UserConfig {
  return {
    mode: mode == 'dev' ? 'development' : 'production',
    root: path.resolve(__dirname, './src'),
    build: {
      outDir: distPath,
      cssCodeSplit: false,
      minify: mode == 'dev' ? false : true,
      rollupOptions: {
        input: lib,
        output: {
          entryFileNames: '[name].js',
          assetFileNames: `assets/[name].[ext]`,
          format: "iife"
        }
      }
    },
    resolve: {
      alias: { '@': path.resolve(__dirname, './src') }
    },
    plugins: [
      vue(),
      Components({ resolvers: [AntDesignVueResolver({})] }),
    ]
  }
}

function emptyDistDir() {
  const dir = path.resolve(__dirname, './dist')
  if (fs.existsSync(dir)) {
    fs.rmdirSync(dir, { recursive: true })
  }
  fs.mkdirSync(dir)
}

function copyFile(src: string, dest: string) {
  fs.copyFileSync(
    path.resolve(__dirname, src),
    path.resolve(__dirname, dest)
  )
}

function copyDir(src: string, dest: string) {
  fs.cpSync(
    path.resolve(__dirname, src),
    path.resolve(__dirname, dest),
    { recursive: true }
  )
}

async function buildProject(mode: string) {
  emptyDistDir()
  copyFile('./src/manifest.json', './dist/manifest.json')
  copyDir('./src/assets', './dist/assets')
  copyDir('./src/popup', './dist/popup')

  const libs = [
    { 'doc_end': path.resolve(__dirname, './src/content/resume.ts') },
    { 'doc_start': path.resolve(__dirname, './src/content/communication.ts') },
    { 'background': path.resolve(__dirname, './src/background/index.ts') },
    { 'inject': path.resolve(__dirname, './src/content/inject.ts') },
    { 'message_inject': path.resolve(__dirname, './src/content/message/index.ts') },
  ]

  libs.forEach(async (lib) => {
    await build(getConfig(lib, mode))
  })
}


function watchFileChange() {
  const watchList = ['./src', './build.ts']
  watchList.forEach((item) => {
    fs.watch('./src', { recursive: true }, (eventType, filename) => {
      buildProject(mode)
    })
  })
}

// zip a folder using jszip, params is folder path string.
function zipFolder(folder:string, outDir:string) {
  const items = fs.readdirSync(folder, {recursive: true}) as string[]
  const zip = new JSZip()
  items.forEach((item) => {
    const fileStat = fs.statSync(path.resolve(folder, item))
    if (fileStat.isDirectory()) return
    const filePath = path.resolve(folder, item)
    const fileContent = fs.readFileSync(filePath)
    zip.file(item, fileContent)
  })
  zip.generateAsync({type: 'nodebuffer'}).then((content) => {
    fs.writeFileSync(path.resolve(outDir, `${manifest.name}-${manifest.version}.zip`), content)
  })
} 


if (mode === 'build') {
  buildProject(mode)
} else if (mode === 'dev') {
  buildProject(mode)
  watchFileChange()
} else if (mode == 'package') {
  zipFolder(distPath, path.resolve(__dirname))
}
