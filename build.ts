/**
 * 因为vite目前不支持iife的打包方式下，多个文件的问题，因此需要自己写一个build.ts文件来实现
 */

import { UserConfig, defineConfig } from "vite";
import path from 'path';
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import { build } from "vite"
import fs from 'fs'
import JSZip from 'jszip'
import manifest from './src/manifest.json'
import crx3 = require('crx3')
import { createHash, generateKeyPairSync } from 'crypto'

const distPath = path.resolve(__dirname, './dist')
const keysPath = path.resolve(__dirname, './keys')
const outputPath = path.resolve(__dirname, './output')

function processArgs() {
  const args = process.argv
  const result: { [key: string]: any } = {}
  // 删除前两个参数，第一个是node，第二个是build.ts
  args.splice(0, 2)
  args.forEach((arg: string) => {
    const arr = arg.split('=')
    if (arr.length === 2) {
      result[arr[0]] = arr[1]
    }
  })
  return result
}

const params = processArgs()
const mode = params['--mode']

function getConfig(lib: any, mode: string): UserConfig {
  return {
    mode: mode == 'dev' ? 'development' : 'production',
    root: path.resolve(__dirname, './src'),
    build: {
      outDir: distPath,
      cssCodeSplit: false,
      minify: mode == 'dev' ? false : true,
      rollupOptions: {
        input: lib,
        output: {
          entryFileNames: '[name].js',
          assetFileNames: `assets/[name].[ext]`,
          format: "iife"
        }
      }
    },
    resolve: {
      alias: { '@': path.resolve(__dirname, './src') }
    },
    plugins: [
      vue(),
      Components({ resolvers: [AntDesignVueResolver({})] }),
    ]
  }
}

function emptyDistDir() {
  const dir = path.resolve(__dirname, './dist')
  if (fs.existsSync(dir)) {
    fs.rmdirSync(dir, { recursive: true })
  }
  fs.mkdirSync(dir)
}

function copyFile(src: string, dest: string) {
  fs.copyFileSync(
    path.resolve(__dirname, src),
    path.resolve(__dirname, dest)
  )
}

function copyDir(src: string, dest: string) {
  fs.cpSync(
    path.resolve(__dirname, src),
    path.resolve(__dirname, dest),
    { recursive: true }
  )
}

async function buildProject(mode: string) {
  emptyDistDir()
  copyFile('./src/manifest.json', './dist/manifest.json')
  copyDir('./src/assets', './dist/assets')
  copyDir('./src/popup', './dist/popup')

  const libs = [
    { 'doc_end': path.resolve(__dirname, './src/content/resume.ts') },
    { 'doc_start': path.resolve(__dirname, './src/content/communication.ts') },
    { 'background': path.resolve(__dirname, './src/background/index.ts') },
    { 'inject': path.resolve(__dirname, './src/content/inject.ts') },
    { 'message_inject': path.resolve(__dirname, './src/content/message/index.ts') },
  ]

  // 使用Promise.all来等待所有构建完成
  await Promise.all(libs.map(async (lib) => {
    await build(getConfig(lib, mode))
  }))

  console.log('✅ 项目构建完成')
}


function watchFileChange() {
  const watchList = ['./src', './build.ts']
  watchList.forEach((item) => {
    fs.watch('./src', { recursive: true }, (eventType, filename) => {
      buildProject(mode)
    })
  })
}

// zip a folder using jszip, params is folder path string.
function zipFolder(folder:string, outDir:string) {
  const items = fs.readdirSync(folder, {recursive: true}) as string[]
  const zip = new JSZip()
  items.forEach((item) => {
    const fileStat = fs.statSync(path.resolve(folder, item))
    if (fileStat.isDirectory()) return
    const filePath = path.resolve(folder, item)
    const fileContent = fs.readFileSync(filePath)
    zip.file(item, fileContent)
  })
  zip.generateAsync({type: 'nodebuffer'}).then((content) => {
    fs.writeFileSync(path.resolve(outDir, `${manifest.name}-${manifest.version}.zip`), content)
  })
}

// 确保目录存在
function ensureDir(dirPath: string) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
  }
}

// 生成RSA密钥对
function generateKeyPair() {
  const keyPairPath = path.resolve(keysPath, 'key.pem')
  const publicKeyPath = path.resolve(keysPath, 'key.pub')

  ensureDir(keysPath)

  if (fs.existsSync(keyPairPath)) {
    console.log('密钥已存在，跳过生成')
    return {
      privateKey: fs.readFileSync(keyPairPath, 'utf8'),
      publicKey: fs.readFileSync(publicKeyPath, 'utf8')
    }
  }

  console.log('生成新的RSA密钥对...')
  const { privateKey, publicKey } = generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  })

  fs.writeFileSync(keyPairPath, privateKey)
  fs.writeFileSync(publicKeyPath, publicKey)

  console.log('密钥对已生成并保存到 keys/ 目录')
  return { privateKey, publicKey }
}

// 获取目录下所有文件
function getAllFiles(dirPath: string, arrayOfFiles: string[] = []): string[] {
  const files = fs.readdirSync(dirPath)

  files.forEach((file) => {
    const fullPath = path.join(dirPath, file)
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles)
    } else {
      arrayOfFiles.push(fullPath)
    }
  })

  return arrayOfFiles
}

// 生成.crx文件
async function generateCrx() {
  console.log('开始生成.crx文件...')

  ensureDir(outputPath)

  // 生成或读取密钥
  const { privateKey } = generateKeyPair()

  const crxPath = path.resolve(outputPath, `${manifest.name}-${manifest.version}.crx`)
  const zipPath = path.resolve(outputPath, `${manifest.name}-${manifest.version}.zip`)

  try {
    // 获取dist目录下的所有文件
    const files = getAllFiles(distPath)

    // 使用crx3生成.crx文件
    await crx3(files, {
      keyPath: path.resolve(keysPath, 'key.pem'),
      crxPath: crxPath,
      zipPath: zipPath
    })

    console.log(`✅ .crx文件已生成: ${crxPath}`)
    console.log(`✅ .zip文件已生成: ${zipPath}`)

    return crxPath
  } catch (error) {
    console.error('生成.crx文件时出错:', error)
    throw error
  }
}


async function main() {
  if (mode === 'build') {
    await buildProject(mode)
  } else if (mode === 'dev') {
    await buildProject(mode)
    watchFileChange()
  } else if (mode === 'package') {
    zipFolder(distPath, path.resolve(__dirname))
  } else if (mode === 'crx') {
    // 先构建项目，然后生成.crx文件
    await buildProject('build')
    await generateCrx()
  } else if (mode === 'build-crx') {
    // 构建并生成.crx文件的组合命令
    await buildProject('build')
    await generateCrx()
  } else {
    console.log('可用的模式:')
    console.log('  --mode=build      构建项目')
    console.log('  --mode=dev        开发模式（构建+监听文件变化）')
    console.log('  --mode=package    打包为zip文件')
    console.log('  --mode=crx        生成.crx文件')
    console.log('  --mode=build-crx  构建并生成.crx文件')
  }
}

// 执行主函数
main().catch(console.error)
