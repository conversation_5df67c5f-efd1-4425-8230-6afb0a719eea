# 小鹿助手 Chrome扩展

招聘数据融合管理插件，一键节省50%人才筛选时间！

## 快速开始

### 开发环境

```bash
# 安装依赖
npm install

# 开发模式（构建+监听文件变化）
npm run dev

# 构建项目
npm run build
```

### 打包发布

```bash
# 一键构建并生成CRX文件
npm run build-crx
```

生成的文件：
- `output/小鹿助手-1.0.1.crx` - Chrome扩展安装文件
- `output/小鹿助手-1.0.1.zip` - Chrome Web Store上传文件

### 安装扩展

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的`dist`目录

## 更多信息

详细的打包和使用说明请参考：[CRX打包使用指南.md](./CRX打包使用指南.md)

## 加油干