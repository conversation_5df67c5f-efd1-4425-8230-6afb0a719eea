{"name": "chrome-plugin", "version": "1.0.0", "description": "{**When you're done, you can delete the content in this README and update the file with details for others getting started with your repository**}", "main": "index.js", "scripts": {"build": "ts-node build.ts --mode=build", "dev": "ts-node build.ts --mode=dev", "package": "ts-node build.ts --mode=package"}, "author": "", "license": "ISC", "dependencies": {"@types/chrome": "^0.0.253", "@vitejs/plugin-vue": "^4.5.1", "@vue/compiler-sfc": "^3.3.11", "ant-design-vue": "^4.0.7", "axios": "^1.6.2", "crx3": "^1.1.3", "dayjs": "^1.11.10", "jszip": "^3.10.1", "less": "^4.2.0", "pinia": "^2.1.7", "pug": "^3.0.2", "puppeteer": "^21.6.1", "rollup-plugin-vue": "^6.0.0", "sass": "^1.69.5", "ts-node": "^10.9.2", "unplugin-vue-components": "^0.26.0", "uuid": "^9.0.1", "vite": "^5.0.5", "vite-plugin-static-copy": "^0.17.0", "vue": "^3.3.10"}, "devDependencies": {"@types/node": "^20.10.3", "@types/uuid": "^9.0.7", "@types/vue": "^2.0.0", "@vue/runtime-core": "^3.5.13"}, "engines": {"node": ">=18.17.0"}}