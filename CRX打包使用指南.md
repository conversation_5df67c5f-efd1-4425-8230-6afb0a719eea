# Chrome扩展.crx文件打包使用指南

## 项目概述

本项目已成功实现Chrome扩展的.crx文件打包功能，支持自动生成签名密钥、构建项目并打包为可安装的.crx文件。

## 功能特性

- ✅ 自动构建Chrome扩展项目
- ✅ 自动生成RSA密钥对用于签名
- ✅ 生成.crx文件（用于直接安装）
- ✅ 生成.zip文件（用于Chrome Web Store上传）
- ✅ 密钥管理和重用
- ✅ 完整的错误处理和日志输出

## 目录结构

```
itp-plugin/
├── src/                    # 源代码目录
├── dist/                   # 构建输出目录
├── keys/                   # 密钥存储目录
│   ├── key.pem            # 私钥文件
│   └── key.pub            # 公钥文件
├── output/                 # 打包输出目录
│   ├── 小鹿助手-1.0.1.crx  # CRX文件
│   └── 小鹿助手-1.0.1.zip  # ZIP文件
├── types/                  # TypeScript类型声明
├── build.ts               # 构建脚本
└── package.json           # 项目配置
```

## 可用命令

### 1. 构建项目
```bash
npm run build
```
仅构建项目，生成dist目录下的文件。

### 2. 开发模式
```bash
npm run dev
```
构建项目并监听文件变化，自动重新构建。

### 3. 生成ZIP包
```bash
npm run package
```
将dist目录打包为ZIP文件。

### 4. 生成CRX文件
```bash
npm run crx
```
仅生成.crx文件（需要先运行build）。

### 5. 构建并生成CRX文件（推荐）
```bash
npm run build-crx
```
一键完成构建和CRX文件生成。

### 6. 清理文件
```bash
npm run clean
```
清理dist、output、keys目录。

## 详细使用步骤

### 第一次使用

1. **安装依赖**
   ```bash
   npm install
   ```

2. **构建并生成CRX文件**
   ```bash
   npm run build-crx
   ```

3. **查看生成的文件**
   - CRX文件：`output/小鹿助手-1.0.1.crx`
   - ZIP文件：`output/小鹿助手-1.0.1.zip`
   - 私钥文件：`keys/key.pem`

### 后续使用

如果已经生成过密钥，后续打包会自动重用现有密钥：

```bash
npm run build-crx
```

## 安装CRX文件到Chrome浏览器

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的`dist`目录

### 方法二：CRX文件安装

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 将生成的`.crx`文件拖拽到扩展程序页面
5. 确认安装

**注意**：由于Chrome安全策略，直接安装.crx文件可能会被阻止。推荐使用方法一进行开发测试。

## 发布到Chrome Web Store

1. 使用生成的`.zip`文件上传到Chrome Web Store
2. 文件路径：`output/小鹿助手-1.0.1.zip`
3. 按照Chrome Web Store的发布流程进行操作

## 密钥管理

### 密钥文件位置
- 私钥：`keys/key.pem`
- 公钥：`keys/key.pub`

### 重要提醒
- **请妥善保管私钥文件**，丢失后无法更新已发布的扩展
- 建议将`keys/`目录添加到`.gitignore`中，避免泄露私钥
- 如需重新生成密钥，删除`keys`目录后重新运行打包命令

## 故障排除

### 常见问题

1. **构建失败**
   - 检查Node.js版本（需要>=18.17.0）
   - 运行`npm install`确保依赖安装完整

2. **CRX文件无法安装**
   - 使用开发者模式安装dist目录
   - 检查manifest.json文件格式是否正确

3. **权限错误**
   - 确保有写入keys和output目录的权限
   - 在Windows上可能需要以管理员身份运行

### 日志输出

打包过程中会显示详细的日志信息：
- ✅ 项目构建完成
- ✅ .crx文件已生成: [路径]
- ✅ .zip文件已生成: [路径]

## 技术实现

- **构建工具**：Vite + TypeScript
- **CRX生成**：crx3库
- **密钥生成**：Node.js crypto模块
- **文件压缩**：JSZip库

## 版本信息

- Chrome扩展版本：1.0.1
- Manifest版本：3
- 支持的Chrome版本：64.0.3242及以上
