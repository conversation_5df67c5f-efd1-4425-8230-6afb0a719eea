# 聊天功能实现与开发指南

## 目录
1. [概述](#概述)
2. [架构设计](#架构设计)
3. [核心组件](#核心组件)
4. [现有实现](#现有实现)
5. [开发新网站支持](#开发新网站支持)
6. [维护指南](#维护指南)
7. [最佳实践](#最佳实践)
8. [故障排除](#故障排除)

## 概述

本聊天功能系统是一个基于 Vue.js 和 TypeScript 的浏览器扩展聊天监控系统，支持多个招聘网站的自动化消息处理。系统采用模块化设计，易于扩展和维护。

### 主要功能
- 多网站支持（猎聘、LinkedIn、BOSS直聘）
- 自动消息监控
- 智能回复（基于 Coze AI）
- 防检测机制
- 动态配置管理

### 技术栈
- **前端框架**: Vue.js 3 + TypeScript
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式**: Sass/Pug
- **扩展API**: Chrome Extensions Manifest V3

## 架构设计

### 整体架构

```
├── src/content/message/
│   ├── message.vue           # 主组件
│   ├── index.ts             # 入口文件
│   ├── types/               # 类型定义
│   │   └── message-handler.ts
│   ├── handlers/            # 网站处理器
│   │   ├── base-message-handler.ts
│   │   ├── liepin-handler.ts
│   │   ├── linkedin-handler.ts
│   │   └── zhipin-handler.ts
│   └── utils/              # 工具函数
│       ├── handler-registry.ts
│       └── randomization.ts
```

### 设计模式

1. **策略模式**: 不同网站使用不同的处理策略
2. **工厂模式**: 通过注册表创建对应的处理器
3. **观察者模式**: 监控DOM变化和消息更新
4. **模板方法模式**: 基础处理器定义通用流程

## 核心组件

### 1. MessageHandler 接口

```typescript
export interface MessageHandler {
  config: MessageHandlerConfig
  isValidPage: () => boolean
  checkForNewMessages: () => Promise<UnreadMessageInfo[]>
  handleUnreadMessage: (messageInfo: UnreadMessageInfo) => Promise<void>
  startMonitoring: () => void
  stopMonitoring: () => void
}
```

**核心方法说明**:
- `isValidPage()`: 验证当前页面是否支持
- `checkForNewMessages()`: 检查未读消息
- `handleUnreadMessage()`: 处理单条未读消息
- `startMonitoring()` / `stopMonitoring()`: 控制监控状态

### 2. 基础处理器 (BaseMessageHandler)

提供通用的监控逻辑和生命周期管理：

```typescript
export function createBaseMessageHandler(
  config: MessageHandlerConfig,
  handlerFunctions: HandlerFunctions
): MessageHandler
```

**特性**:
- 定时检查机制
- 动态间隔调节
- 错误处理
- 生命周期管理

### 3. 处理器注册表 (HandlerRegistry)

管理所有网站处理器的注册和创建：

```typescript
// 注册新处理器
registerHandler('website-name', createHandlerFunction)

// 自动检测并创建处理器
const handler = createHandlerForCurrentWebsite(configData)
```

### 4. 防检测机制 (Randomization)

```typescript
// 预设间隔配置
export const IntervalPresets = {
  conservative: () => number,  // 45-90秒，适用高安全性网站
  moderate: () => number,      // 25-45秒，常规网站
  aggressive: () => number     // 10-20秒，测试环境
}
```

## 现有实现

### 1. 猎聘 (Liepin)
- **URL模式**: `liepin.com/im/`
- **特点**: DOM结构相对稳定
- **实现重点**: 聊天列表解析、消息提取

### 2. LinkedIn
- **URL模式**: `linkedin.com/messaging/`
- **特点**: 复杂的React结构，频繁更新
- **实现重点**: JSON数据提取、CID获取

### 3. BOSS直聘 (Zhipin)
- **URL模式**: `zhipin.com/web/geek/chat`
- **特点**: SPA架构，动态加载
- **实现重点**: 实时消息监控

## 开发新网站支持

### 步骤1: 创建处理器文件

```typescript
// src/content/message/handlers/newsite-handler.ts
import type { MessageHandler, UnreadMessageInfo } from "../types/message-handler"
import { createBaseMessageHandler } from "./base-message-handler"
import { IntervalPresets, addRandomDelay } from "../utils/randomization"

// 全局配置存储
let cozeConfig: any = null

// 1. 创建网站配置
function createNewSiteConfig() {
  return {
    websiteName: "NewSite",
    urlPattern: "newsite.com/messages/",
    checkInterval: 30000, // 会被 getNextInterval 覆盖
  }
}

// 2. 设置和获取配置
function setCozeConfig(config: any): void {
  cozeConfig = config
}

function getCozeConfig(): any {
  return cozeConfig
}

// 3. 实现核心功能函数
async function checkForNewMessages(): Promise<UnreadMessageInfo[]> {
  const unreadMessages: UnreadMessageInfo[] = []
  
  // TODO: 实现具体的消息检查逻辑
  // 例如：查找未读消息元素
  const unreadElements = document.querySelectorAll('.unread-message')
  
  unreadElements.forEach((element, index) => {
    if (element instanceof HTMLElement) {
      unreadMessages.push({
        count: 1,
        contactElement: element,
        index: index
      })
    }
  })
  
  return unreadMessages
}

async function handleUnreadMessage(messageInfo: UnreadMessageInfo): Promise<void> {
  console.log(`处理第 ${messageInfo.index + 1} 条未读消息`)
  
  try {
    // 添加人性化延迟
    await addRandomDelay()
    
    // 点击联系人
    messageInfo.contactElement.click()
    
    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 提取对话信息
    await extractConversation(messageInfo.index + 1)
    
  } catch (error) {
    console.error(`处理消息失败:`, error)
  }
}

// 4. 实现对话提取逻辑
async function extractConversation(contactNumber: number): Promise<void> {
  console.log(`Contact ${contactNumber}: 开始提取对话...`)
  
  const conversation: Array<{
    from: string
    sendTime: string
    message: string
  }> = []
  
  // TODO: 根据网站结构实现消息提取
  // 示例：
  const messageElements = document.querySelectorAll('.message-item')
  
  messageElements.forEach((element, index) => {
    const messageText = element.querySelector('.message-text')?.textContent?.trim() || ''
    const timestamp = element.querySelector('.timestamp')?.textContent?.trim() || `message-${index}`
    const isFromUser = element.classList.contains('incoming') // 根据实际情况调整
    
    conversation.push({
      from: isFromUser ? 'user' : 'assistant',
      sendTime: timestamp,
      message: messageText
    })
  })
  
  // 提取最新用户消息
  const latestUserMessage = extractLatestUserMessages(conversation)
  
  if (latestUserMessage && cozeConfig) {
    console.log(`Contact ${contactNumber}: 发现新消息，准备回复`)
    await sendCozeReply(latestUserMessage, contactNumber)
  }
}

// 5. 实现AI回复功能
async function sendCozeReply(userMessage: string, contactNumber: number): Promise<void> {
  // TODO: 调用AI API获取回复
  // TODO: 在聊天界面发送回复
}

// 6. 创建处理器
export function createNewSiteHandler(configData?: any): MessageHandler {
  if (configData) {
    setCozeConfig(configData)
  }

  const config = createNewSiteConfig()
  
  const handlerFunctions = {
    checkForNewMessages,
    handleUnreadMessage,
    getNextInterval: IntervalPresets.moderate, // 选择合适的间隔预设
  }

  return createBaseMessageHandler(config, handlerFunctions)
}
```

### 步骤2: 注册处理器

```typescript
// src/content/message/utils/handler-registry.ts

import { createNewSiteHandler } from '../handlers/newsite-handler'

// 1. 添加到工厂函数映射
const handlerFactories = new Map<string, (configData?: any) => MessageHandler>([
  ['liepin', createLiepinHandler],
  ['linkedin', createLinkedInHandler],
  ['zhipin', createZhipinHandler],
  ['newsite', createNewSiteHandler], // 新增
])

// 2. 添加网站检测逻辑
function detectCurrentWebsite(): string | null {
  const currentUrl = window.location.href
  
  // 现有检测逻辑...
  
  if (currentUrl.includes('newsite.com/messages/')) {
    return 'newsite'
  }
  
  return null
}
```

### 步骤3: 开发调试

```typescript
// 开发时可以使用更频繁的检查间隔
const handlerFunctions = {
  checkForNewMessages,
  handleUnreadMessage,
  getNextInterval: IntervalPresets.aggressive, // 调试用
}
```

## 维护指南

### 日常维护

1. **监控日志**
   ```typescript
   // 在处理器中添加详细日志
   console.log(`${config.websiteName}: 检查消息`, new Date().toISOString())
   ```

2. **错误监控**
   ```typescript
   try {
     await handleUnreadMessage(messageInfo)
   } catch (error) {
     console.error(`${websiteName} 处理失败:`, error)
     // 可以添加错误上报逻辑
   }
   ```

3. **性能监控**
   ```typescript
   const startTime = performance.now()
   await checkForNewMessages()
   const endTime = performance.now()
   console.log(`检查耗时: ${endTime - startTime}ms`)
   ```

### 版本更新维护

1. **网站结构变化检测**
   - 定期检查目标网站DOM结构
   - 更新选择器和提取逻辑
   - 测试现有功能是否正常

2. **配置更新**
   ```typescript
   // 支持动态配置更新
   function updateConfig(newConfig: any) {
     cozeConfig = { ...cozeConfig, ...newConfig }
   }
   ```

3. **兼容性处理**
   ```typescript
   // 添加降级处理
   function tryExtractMessage(element: Element): string {
     // 尝试多种选择器
     const selectors = ['.message-text', '.msg-content', '.chat-message']
     
     for (const selector of selectors) {
       const textElement = element.querySelector(selector)
       if (textElement?.textContent?.trim()) {
         return textElement.textContent.trim()
       }
     }
     
     return ''
   }
   ```

### 故障处理流程

1. **收集错误信息**
   - 控制台错误日志
   - 网络请求状态
   - DOM结构变化

2. **定位问题**
   - 检查选择器是否失效
   - 验证API接口变化
   - 确认页面加载时机

3. **修复验证**
   - 本地测试修复效果
   - 多个账号验证
   - 不同浏览器测试

## 最佳实践

### 1. 防检测策略

```typescript
// 使用随机间隔
getNextInterval: IntervalPresets.moderate

// 添加人性化延迟
await addRandomDelay({
  minDelay: 1000,
  maxDelay: 3000
})

// 模拟人类行为
await simulateScrolling()
await simulateMouseMovement()
```

### 2. 错误处理

```typescript
async function safeExecute<T>(
  operation: () => Promise<T>,
  fallback: T,
  context: string
): Promise<T> {
  try {
    return await operation()
  } catch (error) {
    console.error(`${context} 执行失败:`, error)
    return fallback
  }
}

// 使用示例
const messages = await safeExecute(
  () => checkForNewMessages(),
  [],
  '检查新消息'
)
```

### 3. 配置管理

```typescript
interface SiteConfig {
  enabled: boolean
  checkInterval: number
  autoReply: boolean
  customSelectors?: {
    messageList?: string
    unreadIndicator?: string
    messageInput?: string
  }
}

// 支持站点特定配置
const siteConfigs: Record<string, SiteConfig> = {
  'liepin': {
    enabled: true,
    checkInterval: 30000,
    autoReply: true
  },
  'linkedin': {
    enabled: true,
    checkInterval: 45000,
    autoReply: false,
    customSelectors: {
      messageList: '.msg-conversations-container'
    }
  }
}
```

### 4. 测试策略

```typescript
// 开发环境测试工具
if (process.env.NODE_ENV === 'development') {
  // 暴露调试接口
  (window as any).messageHandlerDebug = {
    forceCheck: () => checkForNewMessages(),
    testReply: (message: string) => sendCozeReply(message, 999),
    getCurrentConfig: () => cozeConfig
  }
}
```

## 故障排除

### 常见问题

1. **消息检测失败**
   - 检查URL模式匹配
   - 验证DOM选择器
   - 确认页面加载完成

2. **自动回复不工作**
   - 检查Coze配置
   - 验证API连接
   - 确认消息提取正确

3. **监控停止**
   - 检查错误日志
   - 重启监控进程
   - 验证页面有效性

### 调试技巧

```typescript
// 添加调试开关
const DEBUG_MODE = true

function debugLog(message: string, data?: any) {
  if (DEBUG_MODE) {
    console.log(`[DEBUG] ${message}`, data)
  }
}

// 使用调试日志
debugLog('开始检查消息', { url: window.location.href })
debugLog('找到未读消息', { count: unreadMessages.length })
```

### 监控面板

```typescript
// 创建状态监控面板
function createDebugPanel() {
  const panel = document.createElement('div')
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: white;
    border: 1px solid #ccc;
    padding: 10px;
    z-index: 10000;
  `
  
  panel.innerHTML = `
    <h4>消息监控状态</h4>
    <div id="status">等待中...</div>
    <div id="last-check">未检查</div>
    <div id="message-count">消息数: 0</div>
  `
  
  document.body.appendChild(panel)
  return panel
}
```

## 总结

本聊天功能系统通过模块化设计实现了多网站支持，具有良好的扩展性和维护性。开发新网站支持时，只需要：

1. 创建对应的处理器文件
2. 实现必需的接口方法
3. 在注册表中注册处理器
4. 测试和调试功能

系统的防检测机制和错误处理确保了稳定运行，详细的日志和调试工具便于维护和故障排除。遵循本指南的最佳实践，可以快速开发出高质量的网站支持。