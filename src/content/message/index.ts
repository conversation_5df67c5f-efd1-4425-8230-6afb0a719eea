import { createApp } from 'vue'
import main from './message.vue'
import { createPinia } from 'pinia'
import draggable from '@/utils/draggable.directive'
import { useTracker } from '@/utils/track'

(async () => {
  const APP_ID = `app-${Date.now()}`
  const container = document.createElement('div')
  container.id = APP_ID
  document.body.append(container)
  const app = createApp(main)
  app.directive('dragable', draggable)
  app.use(createPinia())
  app.mount(`#${APP_ID}`)

  const tracker = await useTracker()
  // 异常捕获
  app.config.errorHandler = (err: any) => {
    console.log(err)
    tracker.exception('app_exception', err)
  }
})()
