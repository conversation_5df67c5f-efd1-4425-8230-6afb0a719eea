// Randomization utilities for anti-bot detection

export interface IntervalConfig {
  minInterval: number
  maxInterval: number
  extendedBreakChance: number
  extendedBreakMultiplier: number
}

export interface DelayConfig {
  minDelay: number
  maxDelay: number
}

// Default configurations
export const DEFAULT_INTERVAL_CONFIG: IntervalConfig = {
  minInterval: 25000, // 25 seconds
  maxInterval: 45000, // 45 seconds
  extendedBreakChance: 0.1, // 10% chance
  extendedBreakMultiplier: 2 // 2x the interval
}

export const DEFAULT_DELAY_CONFIG: DelayConfig = {
  minDelay: 500,  // 0.5 seconds
  maxDelay: 2000  // 2 seconds
}

/**
 * Generate a random number between min and max (inclusive)
 */
function getRandomBetween(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * Generate random interval with occasional extended breaks to simulate human behavior
 */
export function getRandomInterval(config: IntervalConfig = DEFAULT_INTERVAL_CONFIG): number {
  const randomInterval = getRandomBetween(config.minInterval, config.maxInterval)
  
  // Occasionally use longer intervals to simulate human breaks
  if (Math.random() < config.extendedBreakChance) {
    const longInterval = randomInterval * config.extendedBreakMultiplier
    console.log(`Using extended interval: ${longInterval}ms (human break simulation)`)
    return longInterval
  }
  
  return randomInterval
}

/**
 * Create a random delay to simulate human thinking/reaction time
 */
export function getRandomDelay(config: DelayConfig = DEFAULT_DELAY_CONFIG): number {
  return getRandomBetween(config.minDelay, config.maxDelay)
}

/**
 * Add a random delay with Promise-based timing
 */
export async function addRandomDelay(config: DelayConfig = DEFAULT_DELAY_CONFIG): Promise<void> {
  const delay = getRandomDelay(config)
  console.log(`Adding human-like delay: ${delay}ms`)
  return new Promise(resolve => setTimeout(resolve, delay))
}

/**
 * Generate random intervals for different scenarios
 */
export const IntervalPresets = {
  // Conservative intervals for high-security sites
  conservative: (): number => getRandomInterval({
    minInterval: 45000, // 45 seconds
    maxInterval: 90000, // 90 seconds
    extendedBreakChance: 0.15, // 15% chance
    extendedBreakMultiplier: 2.5
  }),
  
  // Moderate intervals for normal sites
  moderate: (): number => getRandomInterval(DEFAULT_INTERVAL_CONFIG),
  
  // Aggressive intervals for testing/development
  aggressive: (): number => getRandomInterval({
    minInterval: 10000, // 10 seconds
    maxInterval: 20000, // 20 seconds
    extendedBreakChance: 0.05, // 5% chance
    extendedBreakMultiplier: 1.5
  })
}

/**
 * Generate random delay presets for different actions
 */
export const DelayPresets = {
  // Quick actions (button clicks)
  quick: (): number => getRandomDelay({
    minDelay: 200,
    maxDelay: 800
  }),
  
  // Normal actions (form interactions)
  normal: (): number => getRandomDelay(DEFAULT_DELAY_CONFIG),
  
  // Thoughtful actions (reading content)
  thoughtful: (): number => getRandomDelay({
    minDelay: 2000,
    maxDelay: 5000
  })
} 