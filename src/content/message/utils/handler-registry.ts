import type { <PERSON><PERSON><PERSON><PERSON> } from '../types/message-handler'
import { create<PERSON><PERSON>pinHandler } from '../handlers/liepin-handler'
import { createLinkedInHandler } from '../handlers/linkedin-handler'
import { createZhipinHandler } from '../handlers/zhipin-handler'

// Registry of available handlers
const handlerFactories = new Map<string, (configData?: any) => MessageHandler>([
  ['liepin', create<PERSON>iepinHandler],
  ['linkedin', createLinkedInHandler],
  ['zhipin', createZhipinHandler]
  // Add more handlers here: ['website-name', createHandlerFunction]
])

function detectCurrentWebsite(): string | null {
  const currentUrl = window.location.href
  
  if (currentUrl.includes('liepin.com/im/')) {
    return 'liepin'
  }
  
  if (currentUrl.includes('linkedin.com/messaging/')) {
    return 'linkedin'
  }
  
  if (currentUrl.includes('zhipin.com/web/geek/chat')) {
    return 'zhipin'
  }
  
  // Add more website detection logic here
  // if (currentUrl.includes('other-site.com')) {
  //   return 'other-site'
  // }
  
  return null
}

export function createHandlerForCurrentWebsite(configData?: any): MessageHandler | null {
  const websiteName = detectCurrentWebsite()
  
  if (!websiteName) {
    console.log('No supported website detected')
    return null
  }
  
  const handlerFactory = handlerFactories.get(websiteName)
  
  if (!handlerFactory) {
    console.log(`No handler available for website: ${websiteName}`)
    return null
  }
  
  console.log(`Creating handler for website: ${websiteName}`)
  if (configData) {
    console.log('Passing config data to handler:', configData)
  }
  return handlerFactory(configData)
}

export function getAllSupportedWebsites(): string[] {
  return Array.from(handlerFactories.keys())
}

export function registerHandler(websiteName: string, handlerFactory: (configData?: any) => MessageHandler): void {
  handlerFactories.set(websiteName, handlerFactory)
  console.log(`Registered handler for website: ${websiteName}`)
} 