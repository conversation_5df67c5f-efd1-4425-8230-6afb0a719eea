<template lang="pug">

</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue'
import type { MessageHandler } from './types/message-handler'
import { createHandlerForCurrentWebsite } from './utils/handler-registry'
import request from '@/utils/plugin-request'
import config from '@/config'

// Reactive references
const currentHandler = ref<MessageHandler | null>(null)
const isMonitoring = ref(false)
const cozeConfig = ref<any>(null)

// Initialize message handler for current website
function initializeMessageHandler(configData?: any): void {
  console.log('Initializing message handler...', window.location.href)
  
  const handler = createHandlerForCurrentWebsite(configData)
  
  if (!handler) {
    console.log('No handler available for current website')
    return
  }
  
  if (!handler.isValidPage()) {
    console.log('Current page is not valid for the handler')
    return
  }
  
  currentHandler.value = handler
  startMonitoring()
}

function startMonitoring(): void {
  if (!currentHandler.value || isMonitoring.value) {
    return
  }

  console.log('Starting message monitoring...')
  currentHandler.value.startMonitoring()
  isMonitoring.value = true
}

function stopMonitoring(): void {
  if (!currentHandler.value || !isMonitoring.value) {
    return
  }

  console.log('Stopping message monitoring...')
  currentHandler.value.stopMonitoring()
  isMonitoring.value = false
}

// Lifecycle hooks
onMounted(async () => {
  console.log('Message component mounted')
  
  // Make API request and log response
  try {
    const response = await request.get(
      config.api + `/api/company/1/ai/coze/config`
    )
    console.log('API Response:', response)
    
    // Store the config data and pass it to handler
    if (response.code === 0 && response.data) {
      cozeConfig.value = response.data
      initializeMessageHandler(response.data)
    } else {
      initializeMessageHandler()
    }
  } catch (error) {
    console.error('API request failed:', error)
    initializeMessageHandler()
  }
})

onUnmounted(() => {
  console.log('Message component unmounting')
  stopMonitoring()
})

// Expose functions for external use
defineExpose({
  startMonitoring,
  stopMonitoring,
  isMonitoring: () => isMonitoring.value,
  getCurrentHandler: () => currentHandler.value
})
</script>

<style lang="sass" scoped>
</style>