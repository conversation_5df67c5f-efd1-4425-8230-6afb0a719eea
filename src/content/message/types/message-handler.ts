export interface MessageHandlerConfig {
  websiteName: string
  urlPattern: string
  checkInterval: number
}

export interface UnreadMessageInfo {
  count: number
  contactElement: HTMLElement
  index: number
}

export interface MessageHandler {
  config: MessageHandlerConfig
  isValidPage: () => boolean
  checkForNewMessages: () => Promise<UnreadMessageInfo[]>
  handleUnreadMessage: (messageInfo: UnreadMessageInfo) => Promise<void>
  startMonitoring: () => void
  stopMonitoring: () => void
}

export interface MessageStats {
  todayProcessed: number
  weekProcessed: number
  lastProcessed?: Date
}

export interface MessageSettings {
  autoClosePages: boolean
  enableNotifications: boolean
  autoClickFirstUnread: boolean
} 