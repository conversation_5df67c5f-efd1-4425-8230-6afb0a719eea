import type { <PERSON><PERSON><PERSON><PERSON>, MessageHandlerConfig, UnreadMessageInfo } from '../types/message-handler'

interface HandlerFunctions {
  checkForNewMessages: () => Promise<UnreadMessageInfo[]>
  handleUnreadMessage: (messageInfo: UnreadMessageInfo) => Promise<void>
  getNextInterval?: () => number // Optional function to get dynamic intervals
}

export function createBaseMessageHandler(
  config: MessageHandlerConfig,
  handlerFunctions: HandlerFunctions
): MessageHandler {
  let monitoringInterval: NodeJS.Timeout | null = null
  let isMonitoring = false

  function isValidPage(): boolean {
    return window.location.href.includes(config.urlPattern)
  }

  function scheduleNextCheck(): void {
    const interval = handlerFunctions.getNextInterval 
      ? handlerFunctions.getNextInterval() 
      : config.checkInterval
    
    console.log(`Scheduling next check in ${interval}ms for ${config.websiteName}`)
    
    monitoringInterval = setTimeout(async () => {
      await checkAndHandleMessages()
      if (isMonitoring) {
        scheduleNextCheck() // Schedule the next check with a new random interval
      }
    }, interval)
  }

  function startMonitoring(): void {
    if (isMonitoring) {
      console.log(`${config.websiteName} monitoring already started`)
      return
    }

    console.log(`Starting ${config.websiteName} message monitoring...`)
    isMonitoring = true

    // Initial check after page loads
    setTimeout(async () => {
      await checkAndHandleMessages()
      if (isMonitoring) {
        scheduleNextCheck() // Start the dynamic scheduling
      }
    }, 2000)
  }

  function stopMonitoring(): void {
    if (monitoringInterval) {
      clearTimeout(monitoringInterval)
      monitoringInterval = null
    }
    isMonitoring = false
    console.log(`${config.websiteName} monitoring stopped`)
  }

  async function checkAndHandleMessages(): Promise<void> {
    try {
      const unreadMessages = await handlerFunctions.checkForNewMessages()
      
      if (unreadMessages.length > 0) {
        console.log(`Found ${unreadMessages.length} unread messages on ${config.websiteName}`)
        // Handle first unread message
        await handlerFunctions.handleUnreadMessage(unreadMessages[0])
      } else {
        console.log(`No unread messages found on ${config.websiteName}`)
      }
    } catch (error) {
      console.error(`Error checking messages on ${config.websiteName}:`, error)
    }
  }

  return {
    config,
    isValidPage,
    checkForNewMessages: handlerFunctions.checkForNewMessages,
    handleUnreadMessage: handlerFunctions.handleUnreadMessage,
    startMonitoring,
    stopMonitoring
  }
} 