function openResume() {
  if (self.frameElement) {
    return
  }

  const libInput = document.getElementById("inject-input-lid") as HTMLInputElement
  const expectIdInput = document.getElementById("inject-input-expectId") as HTMLInputElement
  const securityIdInput = document.getElementById("inject-input-securityId") as HTMLInputElement
  if (!libInput || !expectIdInput || !securityIdInput) { return }

  let lidVal = libInput.value;
  let expectIdVal = expectIdInput.value;
  let securityIdVal = securityIdInput.value;
  if ('' === lidVal || '' === expectIdVal || '' === securityIdVal) return

  let params = [{
    fromFastRecommend: false,
    suid: "",
    monitor: 1,
    lid: lidVal,
    expectId: expectIdVal,
    securityId: securityIdVal
  }]
  let callback = {
    buyExposureSource: 1,
    lid: lidVal,
    expectId: expectIdVal,
    securityId: securityIdVal,
    decryptSecurityId: "",
    isElite: 0,
    similarNum: 0,
    success: function (e:any, t:any) {
      console.log('success', e, t);
    },
    fail: function (e:any, t:any) {
      console.log('error', e, t);
    }
  }
  console.log(params, 'request boss resume')
  iBossRoot.resume.anonymousResumeViewer(params, callback);
}


setTimeout(()=>{
  openResume()

  // // add 2025.5.14
  // // 初始化收集简历 v2
  // initCollectionResume();
}, 2000)