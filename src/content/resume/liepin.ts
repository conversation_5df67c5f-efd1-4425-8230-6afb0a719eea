// @ts-expect-error
import {
  sleepRandom,
  moveMouseAround,
  simulateHumanClick,
} from "./auto-collection/utils/index";

export async function handleLiepinClosePage() {
  // 判断如果是来自聊天页面的 3-5秒 随机关闭页面
  const randomDelay = Math.floor(Math.random() * (5000 - 3000 + 1)) + 3000;

  // Use Promise-based delay instead of setTimeout to properly await
  await new Promise((resolve) => setTimeout(resolve, randomDelay));

  // if (document.referrer.includes('https://h.liepin.com/im/showmsgnewpage')) {
  //   // 处理你的逻辑
  //   const btn = document.querySelector('.resume-operation-btn');
  //   if (btn instanceof HTMLElement) btn.click();
  //   await sleepRandom(5000, 8000);
  //   // 处理完毕，通知原页面
  //   window.opener.postMessage('resume_done', '*');
  //   await sleepRandom(2000, 3000);
  //   window.close();
  // }
  const selector = '[data-tlg-elem-id="h_pc_res_detail_tel_buy_btn"]';
  const button = document.querySelector(selector);
  let hasFreeText = false;

  if (button) {
    // Find all spans within this button
    const spans = button.querySelectorAll("span");

    // Check each span for '免费' text
    spans.forEach((span) => {
      if (span.textContent.includes("免费")) {
        hasFreeText = true;
      }
    });

    // Click if any span contains '免费'
    if (hasFreeText) {
      (button as HTMLElement).click();
    }
  }

  // https://web.itp.smartdeer.work/
  if (window.location.href.includes("icbTrackId")) {
    moveMouseAround(
      document?.querySelector(".rd-operation-left")?.querySelector("button")
    );
    debugger;
    simulateHumanClick(
      document?.querySelector(".rd-operation-left")?.querySelector("button")
    );
    await sleepRandom(2000, 3000);
    simulateHumanClick(
      document
        ?.querySelector(".hpublic-job-and-msg-modal-cont-new")
        ?.querySelector(".directly-open-chat-btn")
    );
    // document?.querySelector('.hpublic-job-and-msg-modal-cont-new')?.querySelector('.directly-open-chat-btn')?.click()
    await sleepRandom(2000, 4000);

    const shouldAutoClose = window.location.href.includes("autoClose");
    if (shouldAutoClose) {
      window.close();
    }
  }
  return hasFreeText;
}
