/**
 * 生成随机延迟时间
 * @param {number} min 最小延迟时间(毫秒)
 * @param {number} max 最大延迟时间(毫秒)
 * @returns {number} 随机延迟时间
 */
function getRandomDelay(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 处理LinkedIn连接操作
 * 1. 随机延迟后点击连接按钮
 * 2. 随机延迟后点击添加消息按钮
 * 3. 随机延迟后输入消息并点击发送
 */
export function handleLinkedInConnection() {
  // 4-7秒后点击连接按钮
  setTimeout(() => {
    const connectButton = document.querySelector('.artdeco-button--primary[aria-label^="邀请"]')
    console.log('connect', connectButton)
    
    if (connectButton) {
      connectButton?.click()
      handleAddMessage()
    } else {
      // 如果找不到主要邀请按钮，尝试通过更多操作菜单
      const moreActionsButton = document.querySelector('.artdeco-button--secondary[aria-label="更多操作"]')
      console.log('more actions button', moreActionsButton)
      
      if (moreActionsButton) {
        moreActionsButton?.click()
        
        // 0.8-1.5秒后查找并点击邀请选项
        setTimeout(() => {
          const inviteOption = document.querySelector('.artdeco-dropdown__item[aria-label^="邀请"]')
          console.log('invite option', inviteOption)
          
          if (inviteOption) {
            inviteOption?.click()
            handleAddMessage()
          }
        }, getRandomDelay(800, 1500))
      }
    }
  }, getRandomDelay(4000, 7000))
}

function handleAddMessage() {
  // 1.5-3秒后点击添加消息按钮
  setTimeout(() => {
    // 检查是否在限制期内
    const customizeConnect = localStorage.getItem('customizeConnect')
    if (customizeConnect) {
      const { expiry } = JSON.parse(customizeConnect)
      if (new Date().getTime() < expiry) {
        // 在限制期内，直接点击"发送时不添加备注"按钮
        setTimeout(() => {
          const sendWithoutNoteButton = document.querySelector('.artdeco-button--primary[aria-label="发送时不添加备注"]')
          console.log('send without note button', sendWithoutNoteButton)
          if (sendWithoutNoteButton) {
            sendWithoutNoteButton?.click()
          }
        }, getRandomDelay(1500, 2500))
        return
      }
    }

    const modalOutlet = document.querySelector('.artdeco-button--secondary[aria-label^="添加消息"]')
    console.log('modal outlet', modalOutlet)
    if (modalOutlet) {
      modalOutlet?.click()
      
      // 1.5-3秒后输入消息并点击发送
      setTimeout(() => {
        // 检查是否存在modal-upsell-header元素
        const upsellHeader = document.getElementById('modal-upsell-header')
        if (upsellHeader) {
          console.log('Found modal-upsell-header, recording in localStorage')
          // 设置过期时间为当前自然月月底
          const now = new Date()
          const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
          const expiry = lastDayOfMonth.getTime()
          
          localStorage.setItem('customizeConnect', JSON.stringify({ expiry }))
          
          // 点击"发送时不添加备注"按钮
          const sendWithoutNoteButton = document.querySelector('.artdeco-button--primary[aria-label="发送时不添加备注"]')
          console.log('send without note button', sendWithoutNoteButton)
          if (sendWithoutNoteButton) {
            sendWithoutNoteButton?.click()
          }
          return
        }

        const textarea = document.getElementById('custom-message')
        const sendButton = document.querySelector('.artdeco-button--primary[aria-label="发送邀请"]')
        console.log('textarea and send button', textarea, sendButton)
        
        if (textarea && sendButton) {
          textarea.value = 'Hello, I am a headhunter. We have a position that is very suitable for you. Would you like to add me as a friend to communicate?';
          // 触发input和change事件
          textarea.dispatchEvent(new Event('input', { bubbles: true }));
          textarea.dispatchEvent(new Event('change', { bubbles: true }));
          // 0.8-1.5秒后点击发送按钮
          setTimeout(() => {
            sendButton.click()
          }, getRandomDelay(800, 1500))
        }
      }, getRandomDelay(1500, 3000))
    }
  }, getRandomDelay(1500, 3000))
} 