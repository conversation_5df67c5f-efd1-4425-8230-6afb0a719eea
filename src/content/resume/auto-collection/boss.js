import { sleepRandom, waitForElm, randomChance, moveMouseAround, typeLikeHuman, mouseMoveTo, simulateHumanClick, getRandomDelay, delay, waitForElement, generateMousePath, simulateHumanMouseMovement, initAntiDetection } from './utils/'
import { isDailyLimitReached, incDailyCount } from './utils/limitDailyCount'

export async function mainLoop(config) {
  // 时间段判断
  if (Array.isArray(config?.timeRange) && config.timeRange.length === 2) {
    const now = new Date();
    const pad = n => n.toString().padStart(2, '0');
    const cur = `${pad(now.getHours())}:${pad(now.getMinutes())}`;
    const [start, end] = config.timeRange;
    if (!(start <= cur && cur <= end)) {
      console.log('当前不在设定时间段内，跳过执行', cur, config.timeRange);
      return;
    }
  } else {
    console.log('未配置timeRange，跳过执行');
    return;
  }
  // === 每日消息数限制 ===
  if (isDailyLimitReached('boss_sent', 20)) {
    console.log('今日已达上限，跳过');
    return;
  }
  console.log('boss 开始: ', config);
  // debugger;
  await waitForElm('.chat-user');
  while (true) {
    console.log('boss 开始1')
    await sleepRandom(1500, 5000);
    const items = document.querySelectorAll('.geek-item-wrap');
    if (!items.length) break;
    for (let i = 0; i < items.length; i++) {
      if (isDailyLimitReached('boss_sent', 20)) {
        console.log('今日已达上限，跳出');
        return;
      }
      console.log('boss 开始: ', i);
      const item = items[i];
      if (randomChance(0.12)) { // 偶尔跳过
        console.log('boss 跳过: ', i);
        continue;
      }
      await moveMouseAround(item, randomChance(0.5) ? 2 : 4);
      mouseMoveTo(item);
      await sleepRandom(2500, 5000);
      item.click();
      await sleepRandom(2500, 5000);
      // 模拟逐字输入
      const input = document.querySelector('.boss-chat-editor-input');
      if (input) {
        if (randomChance(0.1)) { // 偶尔发呆不输入
          await sleepRandom(2500, 7500);
        } else {
          const replies = [
            "谢谢关注，方便的话麻烦发一个简历",
            "感谢您的联系，可以发份简历看看吗？",
            "您好，能否发一份简历给我？谢谢~",
            "感谢关注，有简历的话可以发我一份吗？",
            "您好，欢迎交流，能发下简历吗？",
            "谢谢您的关注，方便的话请发简历过来哈~",
            "感谢联系，能否先发一份简历？多谢！",
            "您好，麻烦发一下简历，感谢！",
            "感谢您的消息，可以发简历给我吗？",
            "您好，能发下您的简历吗？谢谢！",
            "感谢关注，方便的话请发份简历，谢谢~",
            "您好，欢迎投递简历，期待您的资料！"
          ];
          await typeLikeHuman(input, config?.contactContent || replies[Math.floor(Math.random() * replies.length)], 80, 200);
          await sleepRandom(2500, 5000);
          incDailyCount('boss_sent');
        }
      }
      const submit = document.querySelector('.submit');
      if (submit && randomChance(0.95)) { // 偶尔不发
        await moveMouseAround(submit, randomChance(0.5) ? 1 : 3);
        mouseMoveTo(submit);
        await sleepRandom(2500, 5000);
        simulateHumanClick(submit);
        await sleepRandom(2500, 5000);
      }
      // 点击求简历按钮 & 确认
      const askResume = document.querySelector('.operate-btn');
      if (document.querySelector('.nlp-exchange')) {
        // 模拟用户真实点击
        simulateHumanClick(document.querySelector('.nlp-exchange'));
      } else if (askResume && randomChance(0.9)) { 
        // 偶尔不点
        await moveMouseAround(askResume, randomChance(0.5) ? 1 : 2);
        mouseMoveTo(askResume);
        await sleepRandom(2500, 5000);
        simulateHumanClick(askResume);
        await sleepRandom(2500, 5000);
      }
      const confirm = document.querySelector('.boss-btn-primary');
      if (confirm && randomChance(0.95)) { // 偶尔不点
        await moveMouseAround(confirm, randomChance(0.5) ? 1 : 2);
        mouseMoveTo(confirm);
        await sleepRandom(2500, 5000);
        simulateHumanClick(confirm);
        await sleepRandom(2500, 5000);
      }
      if (randomChance(0.15)) { // 偶尔长时间发呆
        await sleepRandom(7500, 17500);
      }
    }
    break;
  }
}

// mainLoop();
