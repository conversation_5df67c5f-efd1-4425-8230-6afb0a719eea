import { sleepRandom, waitForElm, randomChance, moveMouseAround, typeLikeHuman, mouseMoveTo, simulateHumanClick, waitForElement } from './utils/index'
import { isDailyLimitReached, incDailyCount } from './utils/limitDailyCount'
import { getCozeConfig, getCozeChatParams, getCozeWorkflow } from '../ai/coze'

export async function mainLoop(config) {
  // 时间段判断
  if (Array.isArray(config?.timeRange) && config.timeRange.length === 2) {
    const now = new Date();
    const pad = n => n.toString().padStart(2, '0');
    const cur = `${pad(now.getHours())}:${pad(now.getMinutes())}`;
    const [start, end] = config.timeRange;
    if (!(start <= cur && cur <= end)) {
      console.log('当前不在设定时间段内，跳过执行', cur, config.timeRange);
      return;
    }
  } else {
    console.log('未配置timeRange，跳过执行');
    return;
  }
  // === 每日消息数限制 ===
  if (isDailyLimitReached('liepin_sent', 20)) {
    console.log('今日已达上限，跳过');
    return;
  }

  await sleepRandom(3000, 5000);

  if (document.querySelector('.ant-tabs-nav')?.querySelector('.ant-scroll-number') && Number(document.querySelector('.ant-scroll-number')?.getAttribute('title')) <= 0) {
    console.log('当前页面没有未读消息，跳过不做任何处理');
    return;
  }

  // 点击一下未读消息
  document.querySelector('#unRead')?.click();
  await sleepRandom(1000, 2000);
  const msgList = await waitForElm('.__im_pro__contact-info');
  if (!msgList) return;
  const items = document.querySelectorAll('.__im_pro__list-item');
  for (let i = 0; i < items.length; i++) {
    if (isDailyLimitReached('liepin_sent', 20)) {
      console.log('今日已达上限，跳出');
      return;
    }
    console.log('开始: ', i);
    items[i].click();
    await sleepRandom(800, 1800);

    if (document.querySelector('.__im_pro__message-msg-card-content') || 
      document.querySelector('.__im_basic__universal-card') || 
      document.querySelector('.__im_basic__send-resume-card')) {
      console.log('当前用户已发联系方式');
      // 点击打开右侧弹窗 然后 跳转详情页面
      // document.querySelector('.__im_basic__new-job-resume-card')?.click();
      document.querySelector('.__im_basic__action-svg-item')?.click()
      await sleepRandom(1000, 2000);
      document.querySelector('.__im_pro__slide-container')?.querySelector('.resume-actions')?.children[1]?.querySelector('a')?.click()
      await sleepRandom(1000, 2000);
      // 在 详情页面 点击查看简历
      document.querySelector('.resume-operation-btn')?.click();
      await sleepRandom(5000, 8000);
      // 关闭详情回来 继续执行流程 不生效
      // 等待新页面处理完毕
      window.addEventListener('message', function handler(e) {
        if (e.data === 'resume_done') {
          window.removeEventListener('message', handler);
          // 这里写你要在原页面继续执行的逻辑
          window.location.reload();
        }
      });
    } else {
      // // // 初始化AI聊天
      // const config = await getCozeConfig()
      // console.log('config', config?.data)
      // // debugger
      // const chatParams = await getCozeChatParams({
      //   site: 2,
      //   positionName: document.querySelectorAll('.__im_UI__system-tip')[1]?.querySelector('span')?.innerText,
      //   userInput: document.querySelectorAll('.__im_UI__txt-content')[document.querySelectorAll('.__im_UI__txt-content').length - 1]?.innerText
      // })
      // console.log('chatParams: ', chatParams);
      // // debugger

      // const workflow = await getCozeWorkflow({
      //   // ...config?.data, config?.data?.conversationId || 
      //   conversationId: 0, // 先传 0
      //   params: {
      //     ...chatParams?.data,
      //   }
      // }, config?.data)
      // console.log('workflow: ', workflow?.data);
      // debugger

      // 自动回复
      const input = document.querySelector('.__im_pro__textarea');
      if (input) {
        const replies = [
          "谢谢关注，方便的话麻烦发一个简历",
          "感谢您的联系，可以发份简历看看吗？",
          "您好，能否发一份简历给我？谢谢~",
          "感谢关注，有简历的话可以发我一份吗？",
          "您好，欢迎交流，能发下简历吗？",
          "谢谢您的关注，方便的话请发简历过来哈~",
          "感谢联系，能否先发一份简历？多谢！",
          "您好，麻烦发一下简历，感谢！",
          "感谢您的消息，可以发简历给我吗？",
          "您好，能发下您的简历吗？谢谢！",
          "感谢关注，方便的话请发份简历，谢谢~",
          "您好，欢迎投递简历，期待您的资料！"
        ];
        // JSON.parse(workflow?.data || {})?.output || 
        await typeLikeHuman(input, replies[Math.floor(Math.random() * replies.length)], 80, 200);
        await sleepRandom(2500, 5000);
        incDailyCount('liepin_sent');
      }

      const submit = document?.querySelector('.__im_pro__btn-send');
      if (submit && randomChance(0.95)) { // 偶尔不发
        await moveMouseAround(submit, randomChance(0.5) ? 1 : 3);
        mouseMoveTo(submit);
        await sleepRandom(2500, 5000);
        simulateHumanClick(submit);
        await sleepRandom(2500, 5000);
      }

      // 点击要手机
      const phoneButton = await waitForElement('.action-phone');
      if (!phoneButton) {
        console.log('未找到手机号按钮，跳过当前项');
        return false;
      }

      // 模拟人类点击手机号按钮
      await simulateHumanClick(phoneButton);

      await sleepRandom(2500, 5000);
      try {
        const confirmButton = await waitForElement('.ant-modal-confirm-btns .ant-btn-primary');
        if (!confirmButton) {
          console.log('未找到确认按钮，跳过当前项');
          return false;
        }
        // 模拟人类点击确认按钮
        await simulateHumanClick(confirmButton);
      } catch (error) {
        console.log('error: ', error);
        continue;
      }
      await sleepRandom(2500, 5000);
      try {
        if (document.querySelector('.__im_pro__message-msg-card-content') || 
          document.querySelector('.__im_basic__universal-card') || 
          document.querySelector('.__im_basic__send-resume-card')) {
          console.log('当前用户已发联系方式');
          // 跳转详情页面
          document.querySelector('.__im_basic__new-job-resume-card')?.click();
          await sleepRandom(2500, 5000);
          // 在 详情页面 点击查看简历
          // document.querySelector('.resume-operation-btn')?.click();
          document.querySelector('.__im_basic__action-svg-item')?.click()
          await sleepRandom(1000, 2000);
          document.querySelector('.__im_pro__slide-container')?.querySelector('.resume-actions')?.children[1]?.querySelector('a')?.click()
          await sleepRandom(1000, 2000);
          await sleepRandom(5000, 8000);
          // 关闭详情回来 继续执行流程 不生效
          // 等待新页面处理完毕
          window.addEventListener('message', function handler(e) {
            if (e.data === 'resume_done') {
              window.removeEventListener('message', handler);
              // 这里写你要在原页面继续执行的逻辑
              window.location.reload();
            }
          });
        }
      } catch (error) {
        console.log('error: ', error);
        return false;
      }
      window.location.reload();
    }
  }
}

