// 公用工具方法
export async function sleepRandom(min, max) {
  return new Promise(r => setTimeout(r, Math.random() * (max - min) + min));
}

export async function waitForElm(selector) {
  return new Promise(resolve => {
    if (document.querySelector(selector)) {
      return resolve(document.querySelector(selector));
    }
    const observer = new MutationObserver(() => {
      if (document.querySelector(selector)) {
        resolve(document.querySelector(selector));
        observer.disconnect();
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
  });
}

export function randomChance(p) {
  return Math.random() < p;
}

export async function moveMouseAround(el, times = 2) {
  if (!el) return;
  const rect = el.getBoundingClientRect();
  for (let i = 0; i < times; i++) {
    const x = rect.left + Math.random() * rect.width;
    const y = rect.top + Math.random() * rect.height;
    el.dispatchEvent(new MouseEvent('mousemove', { bubbles: true, clientX: x, clientY: y }));
    await sleepRandom(100, 400);
  }
}

export async function typeLikeHuman(el, text, minDelay = 80, maxDelay = 200) {
  el.innerHTML = '';
  let typo = false;
  for (let i = 0; i < text.length; i++) {
    if (!typo && randomChance(0.08) && i > 2 && i < text.length - 2) {
      el.innerHTML += '错';
      el.dispatchEvent(new Event('input', { bubbles: true }));
      await sleepRandom(120, 300);
      el.innerHTML = el.innerHTML.slice(0, -1);
      el.dispatchEvent(new Event('input', { bubbles: true }));
      await sleepRandom(80, 200);
      typo = true;
    }
    el.innerHTML += text[i];
    el.dispatchEvent(new Event('input', { bubbles: true }));
    await sleepRandom(minDelay, maxDelay);
  }
}

export function mouseMoveTo(el) {
  if (!el) return;
  const rect = el.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const mouseMove = new MouseEvent('mousemove', {
    bubbles: true,
    clientX: x,
    clientY: y
  });
  el.dispatchEvent(mouseMove);
}

export async function simulateHumanClick(el) {
  if (!el) return;
  const rect = el.getBoundingClientRect();
  const points = Math.floor(Math.random() * 8) + 5;
  let prevX = Math.random() * window.innerWidth;
  let prevY = Math.random() * window.innerHeight;
  for (let i = 0; i < points; i++) {
    const t = (i + 1) / points;
    const x = prevX * (1 - t) + (rect.left + rect.width / 2) * t + (Math.random() - 0.5) * 8;
    const y = prevY * (1 - t) + (rect.top + rect.height / 2) * t + (Math.random() - 0.5) * 8;
    el.dispatchEvent(new MouseEvent('mousemove', { bubbles: true, clientX: x, clientY: y }));
    await sleepRandom(10, 40);
    prevX = x;
    prevY = y;
  }
  const x = rect.left + Math.random() * rect.width;
  const y = rect.top + Math.random() * rect.height;
  el.dispatchEvent(new MouseEvent('mousemove', { bubbles: true, clientX: x, clientY: y }));
  await sleepRandom(30, 120);
  el.dispatchEvent(new MouseEvent('mousedown', { bubbles: true, clientX: x, clientY: y, button: 0 }));
  await sleepRandom(30, 120);
  el.dispatchEvent(new MouseEvent('mouseup', { bubbles: true, clientX: x, clientY: y, button: 0 }));
  await sleepRandom(10, 60);
  el.dispatchEvent(new MouseEvent('click', { bubbles: true, clientX: x, clientY: y, button: 0 }));
}

// 正态分布的自然延迟
export function getRandomDelay(min, max) {
  const u1 = Math.random();
  const u2 = Math.random();
  const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
  const mean = (min + max) / 2;
  const stdDev = (max - min) / 6;
  let delay = Math.floor(z0 * stdDev + mean);
  delay = Math.max(min, Math.min(max, delay));
  return delay;
}

// 延迟，带正负100ms抖动
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms + getRandomDelay(-100, 100)));
}

// 更强大的等待元素，带超时和重试
export async function waitForElement(selector, timeout = 10000, retryInterval = 500) {
  const startTime = Date.now();
  let lastError = null;
  let retryCount = 0;
  const maxRetries = 3;
  while (Date.now() - startTime < timeout) {
    try {
      const element = document.querySelector(selector);
      if (element) {
        await delay(getRandomDelay(100, 300));
        return element;
      }
    } catch (error) {
      lastError = error;
      retryCount++;
      if (retryCount >= maxRetries) throw new Error(`重试次数超过限制: ${error.message}`);
    }
    await delay(retryInterval * Math.pow(2, retryCount));
  }
  throw new Error(`等待元素 ${selector} 超时: ${lastError?.message || '未知错误'}`);
}

// 贝塞尔曲线生成自然鼠标轨迹
export function generateMousePath(startX, startY, endX, endY) {
  const points = [];
  const steps = Math.floor(Math.random() * 10) + 10;
  const controlPoints = [
    { x: startX, y: startY },
    { x: startX + (endX - startX) * 0.3 + (Math.random() - 0.5) * 100, y: startY + (endY - startY) * 0.3 + (Math.random() - 0.5) * 100 },
    { x: startX + (endX - startX) * 0.7 + (Math.random() - 0.5) * 100, y: startY + (endY - startY) * 0.7 + (Math.random() - 0.5) * 100 },
    { x: endX, y: endY }
  ];
  for (let i = 0; i <= steps; i++) {
    const t = i / steps;
    const x = Math.pow(1 - t, 3) * controlPoints[0].x +
              3 * Math.pow(1 - t, 2) * t * controlPoints[1].x +
              3 * (1 - t) * Math.pow(t, 2) * controlPoints[2].x +
              Math.pow(t, 3) * controlPoints[3].x;
    const y = Math.pow(1 - t, 3) * controlPoints[0].y +
              3 * Math.pow(1 - t, 2) * t * controlPoints[1].y +
              3 * (1 - t) * Math.pow(t, 2) * controlPoints[2].y +
              Math.pow(t, 3) * controlPoints[3].y;
    points.push({ x: x + (Math.random() - 0.5) * 2, y: y + (Math.random() - 0.5) * 2 });
  }
  return points;
}

// 更自然的鼠标移动
export function simulateHumanMouseMovement(element) {
  if (!element) return 0;
  const rect = element.getBoundingClientRect();
  const startX = Math.random() * window.innerWidth;
  const startY = Math.random() * window.innerHeight;
  const endX = rect.left + (Math.random() * rect.width);
  const endY = rect.top + (Math.random() * rect.height);
  const path = generateMousePath(startX, startY, endX, endY);
  let lastTime = Date.now();
  path.forEach((point, index) => {
    const progress = index / path.length;
    const easeInOut = t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    const d = easeInOut(progress) * 20 + Math.random() * 10;
    setTimeout(() => {
      const now = Date.now();
      lastTime = now;
      const mouseEvent = new MouseEvent('mousemove', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: point.x,
        clientY: point.y,
        movementX: index > 0 ? point.x - path[index - 1].x : 0,
        movementY: index > 0 ? point.y - path[index - 1].y : 0,
        timeStamp: now
      });
      element.dispatchEvent(mouseEvent);
    }, index * d);
  });
  return path.length * 20;
}

// 反检测机制
export function initAntiDetection() {
  // ...直接复制liepin.js里的实现即可...
  // 只导出initAntiDetection，内部私有
  function isDevTools() {
    const threshold = 160;
    const widthThreshold = window.outerWidth - window.innerWidth > threshold;
    const heightThreshold = window.outerHeight - window.innerHeight > threshold;
    return widthThreshold || heightThreshold;
  }
  function detectAutomation() {
    return (
      navigator.webdriver ||
      window.navigator.chrome ||
      window.navigator.webdriver ||
      window.navigator.languages === undefined ||
      window.navigator.plugins.length === 0 ||
      window.navigator.languages.length === 0
    );
  }
  function detectDebugger() {
    const startTime = Date.now();
    return Date.now() - startTime > 100;
  }
  function detectConsole() {
    const console = window.console;
    if (!console) return true;
    const methods = ['log', 'debug', 'info', 'warn', 'error'];
    for (const method of methods) {
      if (typeof console[method] !== 'function') return true;
    }
    return false;
  }
  function detectProxy() {
    return window.Proxy === undefined || window.Proxy.toString().indexOf('native code') === -1;
  }
  function detectTimeManipulation() {
    const start = performance.now();
    const end = performance.now();
    return end - start > 100;
  }
  function detectEventListeners() {
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      if (type === 'load' || type === 'DOMContentLoaded') return;
      return originalAddEventListener.call(this, type, listener, options);
    };
  }
  function detectMutationObserver() {
    const originalMO = window.MutationObserver;
    window.MutationObserver = function(callback) {
      if (typeof callback !== 'function') return new originalMO(callback);
      return new originalMO((mutations, observer) => {
        try { callback(mutations, observer); } catch (e) {}
      });
    };
  }
  function detectConsoleOutput() {
    const originalConsole = window.console;
    const methods = ['log', 'debug', 'info', 'warn', 'error'];
    methods.forEach(method => {
      const original = originalConsole[method];
      originalConsole[method] = function() {
        const stack = new Error().stack;
        if (stack && stack.includes('chrome-extension')) return;
        return original.apply(this, arguments);
      };
    });
  }
  function detectNetworkRequests() {
    const originalFetch = window.fetch;
    window.fetch = function() {
      const url = arguments[0];
      if (typeof url === 'string' && (
        url.includes('chrome-extension') ||
        url.includes('extension') ||
        url.includes('plugin')
      )) {
        return Promise.reject(new Error('Blocked'));
      }
      return originalFetch.apply(this, arguments);
    };
  }
  function detectDOMOperations() {
    const originalQuerySelector = document.querySelector;
    document.querySelector = function() {
      const result = originalQuerySelector.apply(this, arguments);
      if (result && result.getAttribute('data-automation')) return null;
      return result;
    };
  }
  if (isDevTools() || detectAutomation() || detectDebugger() || detectConsole() || detectProxy() || detectTimeManipulation()) {
    return false;
  }
  detectEventListeners();
  detectMutationObserver();
  detectConsoleOutput();
  detectNetworkRequests();
  detectDOMOperations();
  return true;
} 