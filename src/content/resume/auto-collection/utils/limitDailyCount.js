export function getDailyCount(key) {
  const today = new Date().toISOString().slice(0, 10);
  let data = JSON.parse(localStorage.getItem(key) || '{}');
  if (data.date !== today) data = { date: today, count: 0 };
  return data.count;
}

export function incDailyCount(key) {
  const today = new Date().toISOString().slice(0, 10);
  let data = JSON.parse(localStorage.getItem(key) || '{}');
  if (data.date !== today) data = { date: today, count: 0 };
  data.count += 1;
  localStorage.setItem(key, JSON.stringify(data));
}

export function isDailyLimitReached(key, limit) {
  return getDailyCount(key) >= limit;
} 