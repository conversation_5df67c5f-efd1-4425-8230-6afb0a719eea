import { sleepRandom, waitForElm, randomChance, moveMouseAround, typeLikeHuman, mouseMoveTo, simulateHumanClick, getRandomDelay, delay, waitForElement, generateMousePath, simulateHumanMouseMovement, initAntiDetection } from './utils/'
import { isDailyLimitReached, incDailyCount } from './utils/limitDailyCount'

export async function mainLoop(config) {
  // 时间段判断
  if (Array.isArray(config?.timeRange) && config.timeRange.length === 2) {
    const now = new Date();
    const pad = n => n.toString().padStart(2, '0');
    const cur = `${pad(now.getHours())}:${pad(now.getMinutes())}`;
    const [start, end] = config.timeRange;
    if (!(start <= cur && cur <= end)) {
      console.log('当前不在设定时间段内，跳过执行', cur, config.timeRange);
      return;
    }
  } else {
    console.log('未配置timeRange，跳过执行');
    return;
  }
  // === 每日消息数限制 ===
  if (isDailyLimitReached('maimai_sent', 20)) {
    console.log('今日已达上限，跳过');
    return;
  }
  await sleepRandom(3000, 5000);

  document.querySelector('.top-bar-left')?.children[2]?.click();
  await sleepRandom(3000, 5000);
  document.querySelector('.message-top-bar-left')?.children[0].click()
  await sleepRandom(1000, 2000);

  const msgList = document.querySelector('.message-list');
  if (!msgList) return;

  const items = document.querySelectorAll('.message-item');

  // debugger;
  for (let i = 1; i < items.length; i++) {
    if (isDailyLimitReached('maimai_sent', 20)) {
      console.log('今日已达上限，跳出');
      return;
    }
    console.log('开始: ', i);
    items[i].click();
    await sleepRandom(800, 1800);
    // 自动回复
    const input = document.querySelector('.inputPanel');
    if (input) {
      const replies = [
        "谢谢关注，方便的话麻烦发一个简历",
        "感谢您的联系，可以发份简历看看吗？",
        "您好，能否发一份简历给我？谢谢~",
        "感谢关注，有简历的话可以发我一份吗？",
        "您好，欢迎交流，能发下简历吗？",
        "谢谢您的关注，方便的话请发简历过来哈~",
        "感谢联系，能否先发一份简历？多谢！",
        "您好，麻烦发一下简历，感谢！",
        "感谢您的消息，可以发简历给我吗？",
        "您好，能发下您的简历吗？谢谢！",
        "感谢关注，方便的话请发份简历，谢谢~",
        "您好，欢迎投递简历，期待您的资料！"
      ];
      await typeLikeHuman(input, config.message || replies[Math.floor(Math.random() * replies.length)], 80, 200);
      await sleepRandom(2500, 5000);
      incDailyCount('maimai_sent');
    }

    const submit = document?.querySelector('.dialogue-input-container')?.lastChild?.querySelector('div')?.querySelector('a');
    if (submit && randomChance(0.95)) { // 偶尔不发
      await moveMouseAround(submit, randomChance(0.5) ? 1 : 3);
      mouseMoveTo(submit);
      await sleepRandom(2500, 5000);
      simulateHumanClick(submit);
      await sleepRandom(2500, 5000);
    }

    // 点击要手机
    const tools = document.querySelectorAll('.tool');
    if (tools[0]) {
      tools[0].click();
      await sleepRandom(2500, 5000);
    }

  }
}
