// 反扫描和反检测机制
import {
  getRandomDelay,
  delay,
  waitForElement,
  simulateHumanClick,
  simulateHumanMouseMovement,
  generateMousePath,
  initAntiDetection
} from './utils/index';




// 处理单个列表项
async function processListItem(listItem, config) {
  try {
    if (!listItem) {
      console.log('列表项不存在，跳过处理');
      return false;
    }
    // 随机等待一段时间，模拟人类思考时间
    await delay(getRandomDelay(800, 2000));
    // 模拟人类点击列表项
    await simulateHumanClick(listItem);
    await delay(getRandomDelay(1000, 2000));

    const replies = [
      "谢谢关注，方便的话麻烦发一个简历",
      "感谢您的联系，可以发份简历看看吗？",
      "您好，能否发一份简历给我？谢谢~",
      "感谢关注，有简历的话可以发我一份吗？",
      "您好，欢迎交流，能发下简历吗？",
      "谢谢您的关注，方便的话请发简历过来哈~",
      "感谢联系，能否先发一份简历？多谢！",
      "您好，麻烦发一下简历，感谢！",
      "感谢您的消息，可以发简历给我吗？",
      "您好，能发下您的简历吗？谢谢！",
      "感谢关注，方便的话请发份简历，谢谢~",
      "您好，欢迎投递简历，期待您的资料！"
    ];

    await typeLikeHuman(document.querySelector('.__im_pro__textarea'), replies[Math.floor(Math.random() * replies.length)], 80, 200);
    await delay(getRandomDelay(2500, 5000));
    document.querySelector('.__im_pro__textarea').dispatchEvent(new Event('input', { bubbles: true }));
    // 发送消息
    document.querySelector('.__im_pro__btn-send').click()

    // 等待并点击索要手机号按钮
    try {
      const phoneButton = await waitForElement('.action-phone');
      if (!phoneButton) {
        console.log('未找到手机号按钮，跳过当前项');
        return false;
      }
      // 模拟人类点击手机号按钮
      await simulateHumanClick(phoneButton);
      await delay(getRandomDelay(1500, 3000));
    } catch (error) {
      console.error('点击手机号按钮失败:', error);
      return false;
    }
    // 等待并点击确认按钮
    try {
      const confirmButton = await waitForElement('.ant-modal-confirm-btns .ant-btn-primary');
      if (!confirmButton) {
        console.log('未找到确认按钮，跳过当前项');
        return false;
      }
      // 模拟人类点击确认按钮
      await simulateHumanClick(confirmButton);
      await delay(getRandomDelay(2000, 4000));
    } catch (error) {
      console.error('点击确认按钮失败:', error);
      return false;
    }
    // 处理完成，随机等待一段时间
    await delay(getRandomDelay(3000, 6000));
    return true;
  } catch (error) {
    console.error('处理列表项时出错:', error);
    return false;
  }
}

// 标记是否正在处理列表
let isProcessing = false;

// 顺序处理所有列表项
async function processAllListItems(config) {
  if (isProcessing) {
    console.log('正在处理列表项，请等待当前处理完成');
    return;
  }

  try {
    isProcessing = true;
    const listItems = Array.from(document.querySelectorAll('.__im_pro__list-item'));
    console.log(`找到 ${listItems.length} 个待处理的列表项`);
    
    for (let i = 0; i < listItems.length; i++) {
      // 随机等待一段时间，模拟人类操作间隔
      await delay(getRandomDelay(2000, 5000));
      
      console.log(`开始处理第 ${i + 1}/${listItems.length} 个列表项`);
      const success = await processListItem(listItems[i], config);
      
      if (!success) {
        console.log(`第 ${i + 1} 个列表项处理失败，继续处理下一项`);
        await delay(getRandomDelay(2000, 4000));
        continue;
      }
      
      console.log(`第 ${i + 1} 个列表项处理完成`);
    }
    
    console.log('所有列表项处理完成');
  } catch (error) {
    console.error('处理列表时发生错误:', error);
  } finally {
    isProcessing = false;
  }
}

// 标记是否已经点击过未读消息标签
let hasClickedUnreadTab = false;

// 监控头像元素的函数
export async function mainLoop(config) {
  try {
    // 仅在首次加载时点击未读消息标签
    if (!hasClickedUnreadTab) {
      await delay(getRandomDelay(2000, 5000));

      const unreadTab = document.querySelector('#im-search')?.querySelectorAll('.ant-space-item')?.[1]?.querySelector('.__im_pro__filter-item');
      if (unreadTab) {
        // 模拟人类点击未读消息标签
        simulateHumanClick(unreadTab);
        hasClickedUnreadTab = true;

        await processAllListItems(config);
      }
    }

  } catch (error) {
    console.error('监控头像元素时出错:', error);
  }
}
