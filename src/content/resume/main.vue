<template lang="pug">
//- ------------------------------
mixin FloatPanel
  .float-panel(@click.prevent="handleClick" v-dragable v-if="status.showFloatPanel")
    .loading(:class="{show: status.loading}")
    SmartDeerLogo.logo

//- ------------------------------
mixin PanelHead
  .panel-head
    .panel-title
      SmartDeerLogo.logo
      .title-text {{ manifest.name }}

    .panel-actions
      .action-item(@click="status.showInfoPanel = false")
        span 收起
        img(src="/assets/icons/icon-back.svg" height="14px")

//- ------------------------------
mixin TalentActions
  .save-to-talent(v-if="!currentTalent.talent.id")
    Row.info-form(:gutter="[12,12]")
      Col(:span="8")
        Input(placeholder="姓名" v-model:value="currentTalent.talent.realName")

      Col(:span="16")
        Input(:placeholder=" contactForm.contactType === 'mobile' ? '手机号码' : '邮箱地址' " v-model:value="contactForm.contact")
          template(#addonAfter)
            Select(v-model:value="contactForm.contactType" :options="contactOptions" @change="handleContactTypeChange")

      Col(:span="24")
        Button(type="primary" block @click="() => handleSaveTalentClick()") 保存到人才库

  .talent-info-detail(v-else)
    Row.info-form(:gutter="[12, 12]")
      Col(:span="12")
        Button(type="primary" block @click="() => showItpTalentDetail(currentTalent.talent.id)") 查看人才详情
      Col(:span="12")
        Button(type="primary" ghost block @click="status.showAddTalentToJob = true") 加入项目


mixin JobSearch
  Drawer(
    v-model:open="status.showAddTalentToJob"
    title="加入项目"
    placement="bottom"
    :keyboard="false"
    :get-container="false"
    height="80%"
    :bodyStyle="{padding: 0}"
    :headerStyle="{padding: '8px 20px'}"
  )
    JobSearch(@select="handleAddTalentToJob")

//- ------------------------------
mixin InfoTabs
  .info-tabs
    Tabs(v-model:activeKey="activeTab" :tabBarGutter="20" @change="handleTabChange")
      TabPane(:key="'similar-talents'" tab="相似人才" v-if="!isLinkedin")
        SimilarTalents(:talents="similarTalents" :talentId="currentTalent.talent.id" :candidateId="currentCandidateId")
      TabPane(:key="'talent-jobs'" :tab="`项目记录${`(${jobCount})`}`" v-if="currentTalent.talent.id")
        TalentJobList(:talentId="currentTalent.talent.id" @count="(count)=> jobCount = count")
      TabPane(:key="'talent-followups'" :tab="`跟进记录${`(${followupCount})`}`" v-if="currentTalent.talent.id")
        TalentFollowUp(:talentId="currentTalent.talent.id" @count="(count)=> followupCount = count")

//- ------------------------------
mixin CandidateInfo
  TalentInfo(:talent="currentTalent.talent")
  +TalentActions
  TalentExp(:talent="currentTalent")

//- ------------------------------
mixin DuplicateTalent
  Drawer(
    v-model:open="status.showDuplicateTalent"
    :title="`重复人才(${duplicateTalents.length})`"
    placement="bottom"
    :keyboard="false"
    :get-container="false"
    height="80%"
    :bodyStyle="{padding: 0}"
    :headerStyle="{padding: '8px 20px'}"
  )
    DuplicateTalents(
      :talents="duplicateTalents"
      :talentId="currentTalent.talent.id"
      :candidateId="currentCandidateId"
      @select="handleUseDuplicateTalent" 
      @close="status.showDuplicateTalent = false")

//- ------------------------------
mixin MessageBox
  .message-box(
    :class="{show: message.show}" 
    :error="message.type === 'error'" 
    :success="message.type === 'success'" 
    :warning="message.type === 'warning'"
  )
    .message-content {{ message.content }}


mixin LoginGuide
  Drawer(
    v-model:open="status.showLoginGuide"
    title="请登录"
    :closable="false"
    placement="bottom"
    :keyboard="false"
    :get-container="false"
    height="100%"
    :bodyStyle="{padding: 0}"
    :headerStyle="{padding: '8px 20px'}"
  )
    .login-form
      .title 请登录
      .desc 为方便使用，您需要登录ITP系统。<br/>登录后，您可以使用更加丰富的功能，包括人才入库，查看人才流程记录，查看跟进记录等。
      Button.login-btn(@click="gotoLogin" type="primary" size="large") 登录 ITP
      Button(@click="init" type="text" block size="small") 我已登录，立即刷新

//- ------------------------------
mixin InfoPanel
  .info-panel(
    :class="{show: status.showInfoPanel}" 
    ref="infoPanel" 
    :style="{overflowY: status.showDuplicateTalent || status.showAddTalentToJob ? 'hidden' : 'scroll'}"
  )
    Spin(:spinning="status.loading" tip="正在解析，请稍后...")
      +MessageBox
      +PanelHead
      +CandidateInfo
      +InfoTabs

    +LoginGuide
    +DuplicateTalent
    +JobSearch



.app-base(:style="{'--base-zindex': zIndex }")
  ConfigProvider(:theme="theme")
    StyleProvider(hash-priority="high")
      //- 浮动按钮
      +FloatPanel

      //- 人才详情面板
      +InfoPanel

</template>

<script lang="ts" setup>

/**
 * 人才处理策略
 * 1. 当前人才，即解析后直接获取得到的人才信息。这个信息仅仅是html解析后获取得到的信息。没有入库。
 * 2. 重复人才，是系统判断是重复的人才，当用户反馈重复判断不正确后，应该恢复到之前的当前人才的流程。
 * 3. 相似人才，是系统推荐的相似人才列表，这个单独处理，没有和其他有关联的地方。
 */

import { computed, inject, onMounted, reactive, ref } from 'vue'
import {
  Divider, Input, Button, Tabs, TabPane,
  Row, Col, Select, SelectOption, StyleProvider,
  Drawer, Modal, ConfigProvider, Spin, message
} from 'ant-design-vue'
import SmartDeerLogo from '@/assets/icons/logo.vue'
import request from '@/utils/plugin-request'
import config from '@/config'
import TalentInfo from '@/components/talent-info.vue'
import TalentExp from '@/components/talent-exp.vue'
import TalentFollowUp from '@/components/talent-follow-up.vue'
import SimilarTalents from '@/components/similar-talents.vue'
import DuplicateTalents from '@/components/duplicate-talents.vue'
import TalentJobList from '@/components/talent-job.vue'
import JobSearch from '@/components/job-search.vue'
import ZhipinInjector from '@/utils/zhipin-injector'
import MaimaiInjector from '@/utils/maimai-injector'
import { EventTracker, useTracker } from '@/utils/track'
import manifest from '@/manifest.json'
import { useUserStore } from '@/content/user-store'
import { isZhipin, isMaimaiEnt, isMaimaiProfile, isLiepin, isLinkedin } from './constants'
import { handleLinkedInConnection } from './linkedin'
import { handleLiepinClosePage } from './liepin'
import { initCollectionResume } from './auto-collection/index'
import { getCozeConfig, getCozeChatParams, getCozeWorkflow } from './ai/coze'


const infoPanel = ref<HTMLElement>()
const tracker = ref<EventTracker>()
const userStore = useUserStore()

message.config({
  getContainer: () => infoPanel.value!,
  prefixCls: 'shen-message'
})

const zindex = getZIndex()
const zIndex = ref(zindex)
const theme = ref({
  token: {
    colorPrimary: '#ff9111',
    zIndexBase: zindex,
    zIndexPopupBase: zindex + 1
  }
})


const status = reactive({
  showInfoPanel: false,
  showFloatPanel: false,
  loading: false,
  showDuplicateTalent: false,
  showAddTalentToJob: false,
  showLoginGuide: false
})

const contactForm = reactive({
  contact: '',
  contactType: 'mobile'
})

const contactOptions = ref([
  { label: '手机', value: 'mobile' },
  { label: '邮箱', value: 'email' }
])

const activeTab = ref<string>('similar-talents')
const currentTalent = ref<any>({ talent: {} })
const currentCandidateId = ref<number>()
const similarTalents = ref<any[]>()
const duplicateTalents = ref<any[]>([])
const res = ref<any>({})
const jobCount = ref<number>(0)
const followupCount = ref<number>(0)

function handleClick() {
  status.showInfoPanel = !status.showInfoPanel
}

/**
 * 很多页面有聊天功能，为了不遮挡这个聊天功能，需要单独对某些页面调整Zindex，避免遮挡。
 */
function getZIndex(): number {
  const host = window.location.hostname
  if (host.includes('zhipin')) return 1009
  else if (host.includes('liepin')) return 10
  else if (host.includes('linkedin')) return 99999
  else return 999998
}

async function collectResume(htmlRootElement: HTMLElement | Element | null) {
  if (!htmlRootElement) return

  status.showFloatPanel = true
  try {
    status.loading = true
    const resume = htmlRootElement.outerHTML
    const response = await request.post(
      config.api + '/api/talent/third/talentCollector',
      { data: encodeURIComponent(resume).replace(/^"|"$/g, '') }
    )

    // 人才详情，包括基本信息和履历
    currentTalent.value = response.talent
    // 解析后candidateId
    currentCandidateId.value = response.candidateId
    // 重复人才, similarTalents是为了兼容老版本存在的。
    duplicateTalents.value = response.duplicateTalents || response.similarTalents || []

    // 触发一次解析打点。
    tracker.value?.event('collect_resume', {
      candidate_id: currentCandidateId.value,
      talent_id: currentTalent.value.talent.id
    })

    // 当当前用户有ID，并且命中了50%-30%的重复人才是，才会弹窗
    status.showDuplicateTalent = !!duplicateTalents.value.length && !currentTalent.value.talent.id && !isLinkedin
    // 相似人才
    similarTalents.value = isLinkedin ? [] : (response.similarTalentsV2 || [])

    // 人才的联系方式表单的初始化, 如果有解析出联系方式，则使用解析结果初始化表单, 
    // 优先使用手机号码，其次是邮箱。如果没有解析出来，也要保证表单被清空
    contactForm.contactType = 'mobile'
    contactForm.contact = ''
    if (currentTalent.value.talent.email) {
      contactForm.contactType = 'email'
      contactForm.contact = currentTalent.value.talent.email
    }
    if (currentTalent.value.talent.mobileNumber) {
      contactForm.contactType = 'mobile'
      contactForm.contact = currentTalent.value.talent.mobileNumber
    }

    if (currentTalent.value.talent.id) {
      initTabCount(currentTalent.value.talent.id)
      tracker.value?.event('talent_match', {
        talent_id: currentTalent.value.talent.id,
        candidate_id: currentCandidateId.value
      })
    }

    status.showInfoPanel = true
    status.loading = false

  } catch (err: any) {
    tracker.value?.exception('app_exception', err)
  }
}

function gotoLogin() {
  tracker.value?.click('user_login_click', {})
  window.open(`${config.itp}/account/login`)
}

function initZhipinResume() {

  // 如果是在iframe里，则不显示
  if (window.self !== window.top) return

  const zhipin = new ZhipinInjector()
  zhipin.showResume()
  zhipin.onResumeShow((resumeEle: Element) => {
    setTimeout(() => { collectResume(resumeEle) }, 1000)
  })
  zhipin.onResumeClose(() => { status.showInfoPanel = false })
}

async function initTabCount(talentId: number) {
  if (!talentId) return
  try {
    const jobres = await request.get(config.api + `/api/company/${userStore.companyId}/pipeline/job/require/pipeline/by/talent/${talentId}`)
    const followupres = await request.get(
      config.api + `/api/company/1/talent/${talentId}/followups`,
      { params: { current: 1, size: 10 } }
    )
    jobCount.value = jobres.data.length
    followupCount.value = followupres.data.total
  } catch (err: any) {
    tracker.value?.exception('app_exception', err)
    message.error(err.message)
  }
}

async function bindCandidate(canidateId: number, talentId: number) {
  const res = await request.post(
    `${config.api}/api/company/${userStore.companyId}/candidate/${canidateId}/talent`,
    { data: { talentId } }
  )
  tracker.value?.event('bind_talent_candidate', { talent_id: talentId, canidate_id: canidateId })
  return res
}

async function createOrUpdateTalent(talent: any) {
  // 保存或者更新人才，并返回人才ID
  const res = await request.post(`${config.api}/api/talent/createOrUpdate`, { data: talent })
  tracker.value?.event('create_update_talent', { talent_id: res.data })
  return res.data
}

async function addTalentToJob(talentId: number, jobId: number) {
  status.loading = true
  try {
    const res = await request.post(`${config.api}/api/company/${userStore.companyId}/pipeline/addToJobRequirement`, {
      data: { talentId, jobRequirementId: jobId }
    })
    tracker.value?.event('add_talent_job', {
      talent_id: talentId, job_id: jobId
    })
  } catch (err: any) {
    tracker.value?.exception('app_exception', err)
    message.error(err.message)
  }
  status.loading = false
}

function handleAddTalentToJob(job: any) {
  Modal.confirm({
    getContainer: () => infoPanel.value!,
    title: '确认加入项目',
    content: `确认将人才加入项目：${job.processName}？`,
    wrapClassName: 'shen-modal',
    bodyStyle: { padding: 0 },
    width: "90%",
    onOk: async () => {
      await addTalentToJob(currentTalent.value.talent.id, job.id)
      message.success('加入项目成功')
      status.showAddTalentToJob = false
      initTabCount(currentTalent.value.talent.id)
    }
  })
}

function handleContactTypeChange(value: string) {
  if (value === 'mobile') {
    contactForm.contact = currentTalent.value.talent.mobileNumber
  } else if (value === 'email') {
    contactForm.contact = currentTalent.value.talent.email
  }
}

function handleUseDuplicateTalent(talent: any) {
  currentTalent.value.talent.realName = talent.realName
  currentTalent.value.talent.mobileNumber = talent.mobileNumber
  currentTalent.value.talent.email = talent.email

  if (talent.mobileNumber) {
    contactForm.contactType = 'mobile'
    contactForm.contact = talent.mobileNumber
  } else if (talent.email) {
    contactForm.contactType = 'email'
    contactForm.contact = talent.email
  }

  status.showDuplicateTalent = false
}

async function handleSaveTalentClick() {
  status.loading = true
  try {
    tracker.value?.click('save_talent_button_click', { candidate_id: currentCandidateId.value })
    // 注意，联系方式是覆盖模式，即，如果不填写，则使用解析结果。如果解析结果也不满足，则提示用户补充。
    // 同时如果用户有修改联系方式，则使用用户填写的联系方式。
    const talentHasContact = !!currentTalent.value.talent.mobileNumber || !!currentTalent.value.talent.email
    if (!talentHasContact && !contactForm.contact) message.error('请至少填写一种联系方式。')
    const mobile = contactForm.contactType === 'mobile' ? contactForm.contact : currentTalent.value.talent.mobileNumber || ''
    const email = contactForm.contactType === 'email' ? contactForm.contact : currentTalent.value.talent.email || ''

    const params = {
      realName: currentTalent.value.talent.realName,
      mobileNumber: mobile,
      email: email,
      idCardNumber: ''
    }

    const res = await request.get(`${config.api}/api/talent/checkDuplicate`, { params })
    const duplicateTalents = res.data

    if (duplicateTalents.length > 0) {
      const targetTalent = duplicateTalents[0]
      const targetTalentId = targetTalent.talent.id
      // 如果有重复人才，则和当前人才进行合并
      const mergedTalentData = await request.post(
        `${config.api}/api/talent/mergeToTalent/${targetTalentId}`,
        { data: currentTalent.value }
      )
      const talentId = await createOrUpdateTalent(mergedTalentData.data)
      currentTalent.value.talent.id = talentId
      await bindCandidate(currentCandidateId.value!, talentId)
      message.success('人才入库成功')
    } else {
      // 这里将用户填写的联系方式补充回当前人才详情
      if (contactForm.contactType === 'mobile') currentTalent.value.talent.mobileNumber = contactForm.contact
      if (contactForm.contactType === 'email') currentTalent.value.talent.email = contactForm.contact
      const talentId = await createOrUpdateTalent(currentTalent.value)
      await bindCandidate(currentCandidateId.value!, currentTalent.value.talent.id)
      currentTalent.value.talent.id = talentId
      message.success('人才入库成功')
    }
  } catch (err: any) {
    tracker.value?.exception('app_exception', err)
    message.error(err.message)
  }
  status.loading = false
}

function initMaimaiEntResume() {
  const maimai = new MaimaiInjector()
  maimai.init()
  maimai.onResumeShow((resumeEle: HTMLElement) => {
    setTimeout(() => {
      collectResume(resumeEle)
    }, 1000)
  })
  maimai.onResumeClose(() => {
    status.showInfoPanel = false
  })
}

function showItpTalentDetail(talentId: number) {
  const url = `${config.itp}/talent/${talentId}/detail`
  window.open(url)
}

async function checkLogin() {
  try {
    const res = await request.get(`${config.api}/api/company/checkLogin`)
    status.showLoginGuide = !res.data
    userStore.companyId = res.data.companyId
    userStore.id = res.data.id
  } catch (err: any) {
    tracker.value?.exception('app_exception', err)
    message.error(err)
  }
}

async function handleTabChange(tab: string) {
  tracker.value?.click(`${tab}_tab_click`, {
    talent_id: currentTalent.value.talent.id,
    candidate_id: currentCandidateId.value
  })
}

/**
 * 因为可能会导致屏幕遮挡，通过监听ESC键来收起
 */
async function handleEscKeyup() {
  document.addEventListener('keyup', (e) => {
    if (e.key === 'Escape') {
      status.showInfoPanel = false
    }
  })
}


async function init() {
  // 初始化打点
  tracker.value = await useTracker()

  // 初始化登录状态和用户状态
  await checkLogin()

  if (isZhipin) initZhipinResume()
  else if (isMaimaiEnt) initMaimaiEntResume()
  // maimai 是SSR，可以直接进行请求。
  else if (isMaimaiProfile) setTimeout(() => {
    collectResume(document.querySelector('.PCcontent'))
  }, 300)
  else if (isLiepin) setTimeout(async () => {
    // 处理猎聘详情关闭页面
    // add in 2025-05-19
    await handleLiepinClosePage()
    collectResume(document.querySelector('#resume-detail-single'))
  }, 1000)
  else if (isLinkedin) setTimeout(() => collectResume(document.querySelector('.scaffold-layout__content')), 1000)
  else setTimeout(() => collectResume(document.body), 1000)

  handleEscKeyup()

  // // 初始化AI聊天
  // const config = await getCozeConfig()
  // console.log('config', config)
  // debugger
  // const chatParams = await getCozeChatParams({
  //   site: 2,
  //   positionName: '',
  //   userInput: ''+
  // })
  // console.log('chatParams: ', chatParams);
  // debugger



  // 初始化收集简历 
  // add in 2025-05-15
  // setTimeout(
  //   () => initCollectionResume(), 
  //   Math.floor(Math.random() * (5000 - 3000) + 3000)
  // );

}

onMounted(() => {
  init()
})

</script>

<style lang="sass" scoped>
.app-base

.float-panel
  position: fixed
  z-index: var(--base-zindex)
  height: 68px
  width: 68px
  top: 20px
  right: 28px
  cursor: pointer
  display: flex
  justify-content: center
  align-items: center

  .logo
    width: 58px
    height: 58px
    border-radius: 50%
    overflow: hidden
    background-color: #ff9111
    position: relative

.info-panel
  position: fixed
  z-index: var(--base-zindex)
  height: 100vh
  min-height: 600px
  width: 335px
  top: 0
  right: 0
  transition: all .2s
  transform: translateX(100%)
  overflow-y: scroll
  background-color: #fff
  border-radius: 3px
  outline: 1px solid rgba(0,0,0,.1)

  h1
    font-size: 12px

  &::-webkit-scrollbar
    display: none

  &.show
    right: 0
    transition: all .2s
    transform: translateX(0)

  .panel-head
    padding: 8px 12px
    border-bottom: 1px solid #f0f0f0
    display: flex
    align-items: center
    position: sticky
    top: 0
    z-index: calc(var(--base-zindex) + 1)
    background-color: #fff

    .panel-title
      display: flex
      align-items: center
      .logo
        width: 24px
        height: 24px
        margin-right: 8px
        border-radius: 50%
        overflow: hidden
        background-color: #ff9111
      .title-text
        font-size: 16px
        font-weight: bold

    .panel-actions
      margin-left: auto
      .action-item
        display: flex
        align-items: center
        cursor: pointer
.save-to-talent, .talent-info-detail
  padding: 0 12px
  margin: 12px 0

  .info-form
    display: flex
    select
      border: none
      background-color: transparent
      outline: none

.loading
  width: 0%
  height: 0%
  border-radius: 50%
  display: inline-block
  position: relative
  background: linear-gradient(0deg,#FFF 33%, #ff9111 100%)
  box-sizing: border-box
  animation: rotation 1s linear infinite
  position: absolute
  transition: all .8s

  &.show
    width: 100%
    height: 100%
    transition: all .8s

  &::after 
    content: ''
    box-sizing: border-box
    position: absolute
    left: 50%
    top: 50%
    transform: translate(-50%, -50%)
    width: 44px
    height: 44px
    border-radius: 50%
    background: #263238

.info-tabs
  padding: 12px 12px


.message-box
  top: 0
  z-index: var(--base-zindex)
  transform: translateY(-150%)
  position: absolute
  border: 1px solid #ff9111
  background: #fff
  padding: 8px 12px
  transition: all .2s
  margin: 8px auto
  width: 80%
  box-shadow: 0 0 8px rgba(0,0,0,.1)
  border-radius: 8px

  &.show
    transform: translateY(0)
    transition: all .2s

.login-form
  display: flex
  justify-content: center
  flex-direction: column
  height: 70%
  padding: 32px

  .login-btn
    margin-bottom: 8px

  .title
    font-size: 24px
    font-weight: bold
    color: #ff9111
    margin-bottom: 24px

  .desc
    font-size: 14px
    line-height: 1.6
    margin-bottom: 16px
    

@keyframes rotation
  0% 
    transform: rotate(0deg)
  100% 
    transform: rotate(360deg)

</style>