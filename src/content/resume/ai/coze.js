
import request from '@/utils/plugin-request'
import config from '@/config'

// /company/${userStore.companyId}/ai/coze/config
export const getCozeConfig = () => {
  return request.get(config.api + `/api/company/1/ai/coze/config`)
}

// 获取聊天参数
export const getCozeChatParams = ({ site, positionName, userInput }) => {
  return request.post(config.api + `/api/company/1/ai/chatParams/get`, {
    data: {
      site,
      positionName,
      action: '2', // 聊天统一为2
      userInput // 必填，多条用 \n 分隔
    }
  })
}

// POST /company/1/ai/coze/appId/${appId}/workflowId/${workflowId}
// {
//     appId,
//     workFlowId,
//     {
//         "jobDesc": "xxx", //取jobRequirement的面试流程+职位描述字段，两个字段之间用\n分割
//         "action": 2,
//         "site": 2, // 2: 猎聘；3:脉脉；4:LinkedIN，5:Boss，7:ITP
//         "userInput": "xxx", // 用户输入聊天内容,
//         "conversationId": 0 // 先传 0
//     }
// }
export const getCozeWorkflow = (data, cfg) => {
  return request.post(config.api + `/api/company/1/ai/coze/appId/${cfg?.appId}/workflowId/${cfg?.onlineChatFlowId}`, {
    data
  })
}





