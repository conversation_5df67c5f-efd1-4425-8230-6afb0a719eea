parcelRequire=function(e,r,t,n){var i,o="function"==typeof parcelRequire&&parcelRequire,u="function"==typeof require&&require;function f(t,n){if(!r[t]){if(!e[t]){var i="function"==typeof parcelRequire&&parcelRequire;if(!n&&i)return i(t,!0);if(o)return o(t,!0);if(u&&"string"==typeof t)return u(t);var c=new Error("Cannot find module '"+t+"'");throw c.code="MODULE_NOT_FOUND",c}p.resolve=function(r){return e[t][1][r]||r},p.cache={};var l=r[t]=new f.Module(t);e[t][0].call(l.exports,p,l,l.exports,this)}return r[t].exports;function p(e){return f(p.resolve(e))}}f.isParcelRequire=!0,f.Module=function(e){this.id=e,this.bundle=f,this.exports={}},f.modules=e,f.cache=r,f.parent=o,f.register=function(r,t){e[r]=[function(e,r){r.exports=t},{}]};for(var c=0;c<t.length;c++)try{f(t[c])}catch(e){i||(i=e)}if(t.length){var l=f(t[t.length-1]);"object"==typeof exports&&"undefined"!=typeof module?module.exports=l:"function"==typeof define&&define.amd?define(function(){return l}):n&&(this[n]=l)}if(parcelRequire=f,i)throw i;return f}({"qf4T":[function(require,module,exports) {

var e=module.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e);
},{}],"ss9A":[function(require,module,exports) {
var e=module.exports={version:"2.6.11"};"number"==typeof __e&&(__e=e);
},{}],"M7z6":[function(require,module,exports) {
module.exports=function(o){return"object"==typeof o?null!==o:"function"==typeof o};
},{}],"eT53":[function(require,module,exports) {
var r=require("./_is-object");module.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e};
},{"./_is-object":"M7z6"}],"BXiR":[function(require,module,exports) {
module.exports=function(r){try{return!!r()}catch(t){return!0}};
},{}],"P9Ib":[function(require,module,exports) {
module.exports=!require("./_fails")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a});
},{"./_fails":"BXiR"}],"vZ6E":[function(require,module,exports) {
var e=require("./_is-object"),r=require("./_global").document,t=e(r)&&e(r.createElement);module.exports=function(e){return t?r.createElement(e):{}};
},{"./_is-object":"M7z6","./_global":"qf4T"}],"o6Gq":[function(require,module,exports) {
module.exports=!require("./_descriptors")&&!require("./_fails")(function(){return 7!=Object.defineProperty(require("./_dom-create")("div"),"a",{get:function(){return 7}}).a});
},{"./_descriptors":"P9Ib","./_fails":"BXiR","./_dom-create":"vZ6E"}],"y37I":[function(require,module,exports) {
var t=require("./_is-object");module.exports=function(r,e){if(!t(r))return r;var o,n;if(e&&"function"==typeof(o=r.toString)&&!t(n=o.call(r)))return n;if("function"==typeof(o=r.valueOf)&&!t(n=o.call(r)))return n;if(!e&&"function"==typeof(o=r.toString)&&!t(n=o.call(r)))return n;throw TypeError("Can't convert object to primitive value")};
},{"./_is-object":"M7z6"}],"nw8e":[function(require,module,exports) {
var e=require("./_an-object"),r=require("./_ie8-dom-define"),t=require("./_to-primitive"),i=Object.defineProperty;exports.f=require("./_descriptors")?Object.defineProperty:function(o,n,u){if(e(o),n=t(n,!0),e(u),r)try{return i(o,n,u)}catch(c){}if("get"in u||"set"in u)throw TypeError("Accessors not supported!");return"value"in u&&(o[n]=u.value),o};
},{"./_an-object":"eT53","./_ie8-dom-define":"o6Gq","./_to-primitive":"y37I","./_descriptors":"P9Ib"}],"uJ6d":[function(require,module,exports) {
module.exports=function(e,r){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:r}};
},{}],"NXbe":[function(require,module,exports) {
var r=require("./_object-dp"),e=require("./_property-desc");module.exports=require("./_descriptors")?function(t,u,o){return r.f(t,u,e(1,o))}:function(r,e,t){return r[e]=t,r};
},{"./_object-dp":"nw8e","./_property-desc":"uJ6d","./_descriptors":"P9Ib"}],"uHgd":[function(require,module,exports) {
var r={}.hasOwnProperty;module.exports=function(e,n){return r.call(e,n)};
},{}],"U49f":[function(require,module,exports) {
var o=0,t=Math.random();module.exports=function(n){return"Symbol(".concat(void 0===n?"":n,")_",(++o+t).toString(36))};
},{}],"H21C":[function(require,module,exports) {
module.exports=!1;
},{}],"zGcK":[function(require,module,exports) {

var r=require("./_core"),e=require("./_global"),o="__core-js_shared__",i=e[o]||(e[o]={});(module.exports=function(r,e){return i[r]||(i[r]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:require("./_library")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"});
},{"./_core":"ss9A","./_global":"qf4T","./_library":"H21C"}],"d5RU":[function(require,module,exports) {
module.exports=require("./_shared")("native-function-to-string",Function.toString);
},{"./_shared":"zGcK"}],"PHot":[function(require,module,exports) {

var e=require("./_global"),r=require("./_hide"),t=require("./_has"),i=require("./_uid")("src"),n=require("./_function-to-string"),o="toString",u=(""+n).split(o);require("./_core").inspectSource=function(e){return n.call(e)},(module.exports=function(n,o,c,l){var s="function"==typeof c;s&&(t(c,"name")||r(c,"name",o)),n[o]!==c&&(s&&(t(c,i)||r(c,i,n[o]?""+n[o]:u.join(String(o)))),n===e?n[o]=c:l?n[o]?n[o]=c:r(n,o,c):(delete n[o],r(n,o,c)))})(Function.prototype,o,function(){return"function"==typeof this&&this[i]||n.call(this)});
},{"./_global":"qf4T","./_hide":"NXbe","./_has":"uHgd","./_uid":"U49f","./_function-to-string":"d5RU","./_core":"ss9A"}],"kYjc":[function(require,module,exports) {
module.exports=function(o){if("function"!=typeof o)throw TypeError(o+" is not a function!");return o};
},{}],"E3Kh":[function(require,module,exports) {
var r=require("./_a-function");module.exports=function(n,t,u){if(r(n),void 0===t)return n;switch(u){case 1:return function(r){return n.call(t,r)};case 2:return function(r,u){return n.call(t,r,u)};case 3:return function(r,u,e){return n.call(t,r,u,e)}}return function(){return n.apply(t,arguments)}};
},{"./_a-function":"kYjc"}],"izCb":[function(require,module,exports) {

var e=require("./_global"),r=require("./_core"),o=require("./_hide"),i=require("./_redefine"),u=require("./_ctx"),n="prototype",t=function(c,f,l){var q,_,a,d,p=c&t.F,v=c&t.G,F=c&t.S,x=c&t.P,y=c&t.B,B=v?e:F?e[f]||(e[f]={}):(e[f]||{})[n],G=v?r:r[f]||(r[f]={}),P=G[n]||(G[n]={});for(q in v&&(l=f),l)a=((_=!p&&B&&void 0!==B[q])?B:l)[q],d=y&&_?u(a,e):x&&"function"==typeof a?u(Function.call,a):a,B&&i(B,q,a,c&t.U),G[q]!=a&&o(G,q,d),x&&P[q]!=a&&(P[q]=a)};e.core=r,t.F=1,t.G=2,t.S=4,t.P=8,t.B=16,t.W=32,t.U=64,t.R=128,module.exports=t;
},{"./_global":"qf4T","./_core":"ss9A","./_hide":"NXbe","./_redefine":"PHot","./_ctx":"E3Kh"}],"Z5df":[function(require,module,exports) {
var r={}.toString;module.exports=function(t){return r.call(t).slice(8,-1)};
},{}],"JTrm":[function(require,module,exports) {
var r=require("./_cof");module.exports=Array.isArray||function(e){return"Array"==r(e)};
},{"./_cof":"Z5df"}],"yjVO":[function(require,module,exports) {
var o=Math.ceil,r=Math.floor;module.exports=function(t){return isNaN(t=+t)?0:(t>0?r:o)(t)};
},{}],"dJBs":[function(require,module,exports) {
var e=require("./_to-integer"),r=Math.min;module.exports=function(t){return t>0?r(e(t),9007199254740991):0};
},{"./_to-integer":"yjVO"}],"AIP1":[function(require,module,exports) {
var e=require("./_shared")("wks"),r=require("./_uid"),o=require("./_global").Symbol,u="function"==typeof o,i=module.exports=function(i){return e[i]||(e[i]=u&&o[i]||(u?o:r)("Symbol."+i))};i.store=e;
},{"./_shared":"zGcK","./_uid":"U49f","./_global":"qf4T"}],"emcv":[function(require,module,exports) {
"use strict";var r=require("./_is-array"),e=require("./_is-object"),i=require("./_to-length"),t=require("./_ctx"),o=require("./_wks")("isConcatSpreadable");function u(s,a,n,c,f,l,q,_){for(var d,h,p=f,v=0,b=!!q&&t(q,_,3);v<c;){if(v in n){if(d=b?b(n[v],v,a):n[v],h=!1,e(d)&&(h=void 0!==(h=d[o])?!!h:r(d)),h&&l>0)p=u(s,a,d,i(d.length),p,l-1)-1;else{if(p>=9007199254740991)throw TypeError();s[p]=d}p++}v++}return p}module.exports=u;
},{"./_is-array":"JTrm","./_is-object":"M7z6","./_to-length":"dJBs","./_ctx":"E3Kh","./_wks":"AIP1"}],"BjjL":[function(require,module,exports) {
module.exports=function(o){if(null==o)throw TypeError("Can't call method on  "+o);return o};
},{}],"rfVX":[function(require,module,exports) {
var e=require("./_defined");module.exports=function(r){return Object(e(r))};
},{"./_defined":"BjjL"}],"NNbH":[function(require,module,exports) {
var r=require("./_is-object"),e=require("./_is-array"),o=require("./_wks")("species");module.exports=function(i){var t;return e(i)&&("function"!=typeof(t=i.constructor)||t!==Array&&!e(t.prototype)||(t=void 0),r(t)&&null===(t=t[o])&&(t=void 0)),void 0===t?Array:t};
},{"./_is-object":"M7z6","./_is-array":"JTrm","./_wks":"AIP1"}],"igas":[function(require,module,exports) {
var r=require("./_array-species-constructor");module.exports=function(e,n){return new(r(e))(n)};
},{"./_array-species-constructor":"NNbH"}],"Z7eD":[function(require,module,exports) {
var e=require("./_wks")("unscopables"),r=Array.prototype;null==r[e]&&require("./_hide")(r,e,{}),module.exports=function(o){r[e][o]=!0};
},{"./_wks":"AIP1","./_hide":"NXbe"}],"I8vV":[function(require,module,exports) {
"use strict";var r=require("./_export"),e=require("./_flatten-into-array"),t=require("./_to-object"),a=require("./_to-length"),i=require("./_a-function"),u=require("./_array-species-create");r(r.P,"Array",{flatMap:function(r){var n,o,c=t(this);return i(r),n=a(c.length),o=u(c,0),e(o,c,c,n,0,1,r,arguments[1]),o}}),require("./_add-to-unscopables")("flatMap");
},{"./_export":"izCb","./_flatten-into-array":"emcv","./_to-object":"rfVX","./_to-length":"dJBs","./_a-function":"kYjc","./_array-species-create":"igas","./_add-to-unscopables":"Z7eD"}],"RnOJ":[function(require,module,exports) {
var r=require("./_an-object");module.exports=function(t,e,o,a){try{return a?e(r(o)[0],o[1]):e(o)}catch(n){var c=t.return;throw void 0!==c&&r(c.call(t)),n}};
},{"./_an-object":"eT53"}],"JO4d":[function(require,module,exports) {
module.exports={};
},{}],"B0pB":[function(require,module,exports) {
var r=require("./_iterators"),e=require("./_wks")("iterator"),t=Array.prototype;module.exports=function(o){return void 0!==o&&(r.Array===o||t[e]===o)};
},{"./_iterators":"JO4d","./_wks":"AIP1"}],"JCwR":[function(require,module,exports) {
"use strict";var e=require("./_object-dp"),r=require("./_property-desc");module.exports=function(t,i,o){i in t?e.f(t,i,r(0,o)):t[i]=o};
},{"./_object-dp":"nw8e","./_property-desc":"uJ6d"}],"GM7B":[function(require,module,exports) {
var e=require("./_cof"),t=require("./_wks")("toStringTag"),n="Arguments"==e(function(){return arguments}()),r=function(e,t){try{return e[t]}catch(n){}};module.exports=function(u){var o,c,i;return void 0===u?"Undefined":null===u?"Null":"string"==typeof(c=r(o=Object(u),t))?c:n?e(o):"Object"==(i=e(o))&&"function"==typeof o.callee?"Arguments":i};
},{"./_cof":"Z5df","./_wks":"AIP1"}],"ia42":[function(require,module,exports) {
var r=require("./_classof"),e=require("./_wks")("iterator"),t=require("./_iterators");module.exports=require("./_core").getIteratorMethod=function(o){if(null!=o)return o[e]||o["@@iterator"]||t[r(o)]};
},{"./_classof":"GM7B","./_wks":"AIP1","./_iterators":"JO4d","./_core":"ss9A"}],"md62":[function(require,module,exports) {
var r=require("./_wks")("iterator"),t=!1;try{var n=[7][r]();n.return=function(){t=!0},Array.from(n,function(){throw 2})}catch(e){}module.exports=function(n,u){if(!u&&!t)return!1;var o=!1;try{var c=[7],a=c[r]();a.next=function(){return{done:o=!0}},c[r]=function(){return a},n(c)}catch(e){}return o};
},{"./_wks":"AIP1"}],"RRcs":[function(require,module,exports) {
"use strict";var e=require("./_ctx"),r=require("./_export"),t=require("./_to-object"),i=require("./_iter-call"),o=require("./_is-array-iter"),u=require("./_to-length"),n=require("./_create-property"),a=require("./core.get-iterator-method");r(r.S+r.F*!require("./_iter-detect")(function(e){Array.from(e)}),"Array",{from:function(r){var l,c,f,q,_=t(r),h="function"==typeof this?this:Array,v=arguments.length,y=v>1?arguments[1]:void 0,d=void 0!==y,s=0,g=a(_);if(d&&(y=e(y,v>2?arguments[2]:void 0,2)),null==g||h==Array&&o(g))for(c=new h(l=u(_.length));l>s;s++)n(c,s,d?y(_[s],s):_[s]);else for(q=g.call(_),c=new h;!(f=q.next()).done;s++)n(c,s,d?i(q,y,[f.value,s],!0):f.value);return c.length=s,c}});
},{"./_ctx":"E3Kh","./_export":"izCb","./_to-object":"rfVX","./_iter-call":"RnOJ","./_is-array-iter":"B0pB","./_to-length":"dJBs","./_create-property":"JCwR","./core.get-iterator-method":"ia42","./_iter-detect":"md62"}],"Hh2M":[function(require,module,exports) {
"use strict";var l=require("./_fails");module.exports=function(n,u){return!!n&&l(function(){u?n.call(null,function(){},1):n.call(null)})};
},{"./_fails":"BXiR"}],"nrVf":[function(require,module,exports) {
"use strict";var r=require("./_export"),t=require("./_a-function"),i=require("./_to-object"),e=require("./_fails"),o=[].sort,u=[1,2,3];r(r.P+r.F*(e(function(){u.sort(void 0)})||!e(function(){u.sort(null)})||!require("./_strict-method")(o)),"Array",{sort:function(r){return void 0===r?o.call(i(this)):o.call(i(this),t(r))}});
},{"./_export":"izCb","./_a-function":"kYjc","./_to-object":"rfVX","./_fails":"BXiR","./_strict-method":"Hh2M"}],"h4dH":[function(require,module,exports) {

"use strict";var e=require("./_global"),r=require("./_object-dp"),i=require("./_descriptors"),t=require("./_wks")("species");module.exports=function(u){var s=e[u];i&&s&&!s[t]&&r.f(s,t,{configurable:!0,get:function(){return this}})};
},{"./_global":"qf4T","./_object-dp":"nw8e","./_descriptors":"P9Ib","./_wks":"AIP1"}],"smn3":[function(require,module,exports) {
require("./_set-species")("Array");
},{"./_set-species":"h4dH"}],"NaGB":[function(require,module,exports) {
var e=require("./_shared")("keys"),r=require("./_uid");module.exports=function(u){return e[u]||(e[u]=r(u))};
},{"./_shared":"zGcK","./_uid":"U49f"}],"q6yw":[function(require,module,exports) {
var t=require("./_has"),e=require("./_to-object"),o=require("./_shared-key")("IE_PROTO"),r=Object.prototype;module.exports=Object.getPrototypeOf||function(c){return c=e(c),t(c,o)?c[o]:"function"==typeof c.constructor&&c instanceof c.constructor?c.constructor.prototype:c instanceof Object?r:null};
},{"./_has":"uHgd","./_to-object":"rfVX","./_shared-key":"NaGB"}],"a7bX":[function(require,module,exports) {
"use strict";var t=require("./_is-object"),e=require("./_object-gpo"),r=require("./_wks")("hasInstance"),i=Function.prototype;r in i||require("./_object-dp").f(i,r,{value:function(r){if("function"!=typeof this||!t(r))return!1;if(!t(this.prototype))return r instanceof this;for(;r=e(r);)if(this.prototype===r)return!0;return!1}});
},{"./_is-object":"M7z6","./_object-gpo":"q6yw","./_wks":"AIP1","./_object-dp":"nw8e"}],"nGau":[function(require,module,exports) {
var e=require("./_cof");module.exports=Object("z").propertyIsEnumerable(0)?Object:function(r){return"String"==e(r)?r.split(""):Object(r)};
},{"./_cof":"Z5df"}],"g6sb":[function(require,module,exports) {
var e=require("./_iobject"),r=require("./_defined");module.exports=function(i){return e(r(i))};
},{"./_iobject":"nGau","./_defined":"BjjL"}],"vfEH":[function(require,module,exports) {
var e=require("./_to-integer"),r=Math.max,t=Math.min;module.exports=function(n,a){return(n=e(n))<0?r(n+a,0):t(n,a)};
},{"./_to-integer":"yjVO"}],"Ca7J":[function(require,module,exports) {
var e=require("./_to-iobject"),r=require("./_to-length"),t=require("./_to-absolute-index");module.exports=function(n){return function(i,o,u){var f,l=e(i),a=r(l.length),c=t(u,a);if(n&&o!=o){for(;a>c;)if((f=l[c++])!=f)return!0}else for(;a>c;c++)if((n||c in l)&&l[c]===o)return n||c||0;return!n&&-1}};
},{"./_to-iobject":"g6sb","./_to-length":"dJBs","./_to-absolute-index":"vfEH"}],"vL0Z":[function(require,module,exports) {
var r=require("./_has"),e=require("./_to-iobject"),u=require("./_array-includes")(!1),i=require("./_shared-key")("IE_PROTO");module.exports=function(o,a){var n,s=e(o),t=0,h=[];for(n in s)n!=i&&r(s,n)&&h.push(n);for(;a.length>t;)r(s,n=a[t++])&&(~u(h,n)||h.push(n));return h};
},{"./_has":"uHgd","./_to-iobject":"g6sb","./_array-includes":"Ca7J","./_shared-key":"NaGB"}],"bbv4":[function(require,module,exports) {
module.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",");
},{}],"U9a7":[function(require,module,exports) {
var e=require("./_object-keys-internal"),r=require("./_enum-bug-keys");module.exports=Object.keys||function(u){return e(u,r)};
},{"./_object-keys-internal":"vL0Z","./_enum-bug-keys":"bbv4"}],"MiMz":[function(require,module,exports) {
var e=require("./_object-dp"),r=require("./_an-object"),t=require("./_object-keys");module.exports=require("./_descriptors")?Object.defineProperties:function(o,i){r(o);for(var u,c=t(i),n=c.length,s=0;n>s;)e.f(o,u=c[s++],i[u]);return o};
},{"./_object-dp":"nw8e","./_an-object":"eT53","./_object-keys":"U9a7","./_descriptors":"P9Ib"}],"xjB1":[function(require,module,exports) {
var e=require("./_global").document;module.exports=e&&e.documentElement;
},{"./_global":"qf4T"}],"sYaK":[function(require,module,exports) {
var e=require("./_an-object"),r=require("./_object-dps"),t=require("./_enum-bug-keys"),n=require("./_shared-key")("IE_PROTO"),o=function(){},i="prototype",u=function(){var e,r=require("./_dom-create")("iframe"),n=t.length;for(r.style.display="none",require("./_html").appendChild(r),r.src="javascript:",(e=r.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u[i][t[n]];return u()};module.exports=Object.create||function(t,c){var a;return null!==t?(o[i]=e(t),a=new o,o[i]=null,a[n]=t):a=u(),void 0===c?a:r(a,c)};
},{"./_an-object":"eT53","./_object-dps":"MiMz","./_enum-bug-keys":"bbv4","./_shared-key":"NaGB","./_dom-create":"vZ6E","./_html":"xjB1"}],"J0Tl":[function(require,module,exports) {
var r=require("./_redefine");module.exports=function(e,n,i){for(var o in n)r(e,o,n[o],i);return e};
},{"./_redefine":"PHot"}],"yJTF":[function(require,module,exports) {
module.exports=function(o,n,r,i){if(!(o instanceof n)||void 0!==i&&i in o)throw TypeError(r+": incorrect invocation!");return o};
},{}],"Abke":[function(require,module,exports) {
var e=require("./_ctx"),r=require("./_iter-call"),t=require("./_is-array-iter"),i=require("./_an-object"),o=require("./_to-length"),n=require("./core.get-iterator-method"),u={},a={},f=module.exports=function(f,l,c,q,_){var h,s,d,g,p=_?function(){return f}:n(f),v=e(c,q,l?2:1),x=0;if("function"!=typeof p)throw TypeError(f+" is not iterable!");if(t(p)){for(h=o(f.length);h>x;x++)if((g=l?v(i(s=f[x])[0],s[1]):v(f[x]))===u||g===a)return g}else for(d=p.call(f);!(s=d.next()).done;)if((g=r(d,v,s.value,l))===u||g===a)return g};f.BREAK=u,f.RETURN=a;
},{"./_ctx":"E3Kh","./_iter-call":"RnOJ","./_is-array-iter":"B0pB","./_an-object":"eT53","./_to-length":"dJBs","./core.get-iterator-method":"ia42"}],"rq3q":[function(require,module,exports) {
var e=require("./_object-dp").f,r=require("./_has"),o=require("./_wks")("toStringTag");module.exports=function(t,u,i){t&&!r(t=i?t:t.prototype,o)&&e(t,o,{configurable:!0,value:u})};
},{"./_object-dp":"nw8e","./_has":"uHgd","./_wks":"AIP1"}],"ebgP":[function(require,module,exports) {
"use strict";var e=require("./_object-create"),r=require("./_property-desc"),t=require("./_set-to-string-tag"),i={};require("./_hide")(i,require("./_wks")("iterator"),function(){return this}),module.exports=function(o,u,s){o.prototype=e(i,{next:r(1,s)}),t(o,u+" Iterator")};
},{"./_object-create":"sYaK","./_property-desc":"uJ6d","./_set-to-string-tag":"rq3q","./_hide":"NXbe","./_wks":"AIP1"}],"mH0U":[function(require,module,exports) {
"use strict";var e=require("./_library"),r=require("./_export"),t=require("./_redefine"),i=require("./_hide"),n=require("./_iterators"),u=require("./_iter-create"),o=require("./_set-to-string-tag"),s=require("./_object-gpo"),a=require("./_wks")("iterator"),c=!([].keys&&"next"in[].keys()),f="@@iterator",l="keys",q="values",y=function(){return this};module.exports=function(_,p,h,k,v,w,d){u(h,p,k);var x,b,g,j=function(e){if(!c&&e in I)return I[e];switch(e){case l:case q:return function(){return new h(this,e)}}return function(){return new h(this,e)}},m=p+" Iterator",A=v==q,F=!1,I=_.prototype,O=I[a]||I[f]||v&&I[v],P=O||j(v),z=v?A?j("entries"):P:void 0,B="Array"==p&&I.entries||O;if(B&&(g=s(B.call(new _)))!==Object.prototype&&g.next&&(o(g,m,!0),e||"function"==typeof g[a]||i(g,a,y)),A&&O&&O.name!==q&&(F=!0,P=function(){return O.call(this)}),e&&!d||!c&&!F&&I[a]||i(I,a,P),n[p]=P,n[m]=y,v)if(x={values:A?P:j(q),keys:w?P:j(l),entries:z},d)for(b in x)b in I||t(I,b,x[b]);else r(r.P+r.F*(c||F),p,x);return x};
},{"./_library":"H21C","./_export":"izCb","./_redefine":"PHot","./_hide":"NXbe","./_iterators":"JO4d","./_iter-create":"ebgP","./_set-to-string-tag":"rq3q","./_object-gpo":"q6yw","./_wks":"AIP1"}],"x8b3":[function(require,module,exports) {
module.exports=function(e,n){return{value:n,done:!!e}};
},{}],"AoVy":[function(require,module,exports) {
var e=require("./_uid")("meta"),r=require("./_is-object"),t=require("./_has"),n=require("./_object-dp").f,i=0,u=Object.isExtensible||function(){return!0},f=!require("./_fails")(function(){return u(Object.preventExtensions({}))}),o=function(r){n(r,e,{value:{i:"O"+ ++i,w:{}}})},s=function(n,i){if(!r(n))return"symbol"==typeof n?n:("string"==typeof n?"S":"P")+n;if(!t(n,e)){if(!u(n))return"F";if(!i)return"E";o(n)}return n[e].i},c=function(r,n){if(!t(r,e)){if(!u(r))return!0;if(!n)return!1;o(r)}return r[e].w},E=function(r){return f&&a.NEED&&u(r)&&!t(r,e)&&o(r),r},a=module.exports={KEY:e,NEED:!1,fastKey:s,getWeak:c,onFreeze:E};
},{"./_uid":"U49f","./_is-object":"M7z6","./_has":"uHgd","./_object-dp":"nw8e","./_fails":"BXiR"}],"FW4z":[function(require,module,exports) {
var r=require("./_is-object");module.exports=function(e,i){if(!r(e)||e._t!==i)throw TypeError("Incompatible receiver, "+i+" required!");return e};
},{"./_is-object":"M7z6"}],"aIiY":[function(require,module,exports) {
"use strict";var e=require("./_object-dp").f,r=require("./_object-create"),t=require("./_redefine-all"),i=require("./_ctx"),n=require("./_an-instance"),_=require("./_for-of"),o=require("./_iter-define"),u=require("./_iter-step"),f=require("./_set-species"),s=require("./_descriptors"),l=require("./_meta").fastKey,c=require("./_validate-collection"),v=s?"_s":"size",a=function(e,r){var t,i=l(r);if("F"!==i)return e._i[i];for(t=e._f;t;t=t.n)if(t.k==r)return t};module.exports={getConstructor:function(o,u,f,l){var h=o(function(e,t){n(e,h,u,"_i"),e._t=u,e._i=r(null),e._f=void 0,e._l=void 0,e[v]=0,null!=t&&_(t,f,e[l],e)});return t(h.prototype,{clear:function(){for(var e=c(this,u),r=e._i,t=e._f;t;t=t.n)t.r=!0,t.p&&(t.p=t.p.n=void 0),delete r[t.i];e._f=e._l=void 0,e[v]=0},delete:function(e){var r=c(this,u),t=a(r,e);if(t){var i=t.n,n=t.p;delete r._i[t.i],t.r=!0,n&&(n.n=i),i&&(i.p=n),r._f==t&&(r._f=i),r._l==t&&(r._l=n),r[v]--}return!!t},forEach:function(e){c(this,u);for(var r,t=i(e,arguments.length>1?arguments[1]:void 0,3);r=r?r.n:this._f;)for(t(r.v,r.k,this);r&&r.r;)r=r.p},has:function(e){return!!a(c(this,u),e)}}),s&&e(h.prototype,"size",{get:function(){return c(this,u)[v]}}),h},def:function(e,r,t){var i,n,_=a(e,r);return _?_.v=t:(e._l=_={i:n=l(r,!0),k:r,v:t,p:i=e._l,n:void 0,r:!1},e._f||(e._f=_),i&&(i.n=_),e[v]++,"F"!==n&&(e._i[n]=_)),e},getEntry:a,setStrong:function(e,r,t){o(e,r,function(e,t){this._t=c(e,r),this._k=t,this._l=void 0},function(){for(var e=this._k,r=this._l;r&&r.r;)r=r.p;return this._t&&(this._l=r=r?r.n:this._t._f)?u(0,"keys"==e?r.k:"values"==e?r.v:[r.k,r.v]):(this._t=void 0,u(1))},t?"entries":"values",!t,!0),f(r)}};
},{"./_object-dp":"nw8e","./_object-create":"sYaK","./_redefine-all":"J0Tl","./_ctx":"E3Kh","./_an-instance":"yJTF","./_for-of":"Abke","./_iter-define":"mH0U","./_iter-step":"x8b3","./_set-species":"h4dH","./_descriptors":"P9Ib","./_meta":"AoVy","./_validate-collection":"FW4z"}],"vjRp":[function(require,module,exports) {
exports.f={}.propertyIsEnumerable;
},{}],"uIjZ":[function(require,module,exports) {
var e=require("./_object-pie"),r=require("./_property-desc"),i=require("./_to-iobject"),t=require("./_to-primitive"),o=require("./_has"),c=require("./_ie8-dom-define"),u=Object.getOwnPropertyDescriptor;exports.f=require("./_descriptors")?u:function(p,q){if(p=i(p),q=t(q,!0),c)try{return u(p,q)}catch(_){}if(o(p,q))return r(!e.f.call(p,q),p[q])};
},{"./_object-pie":"vjRp","./_property-desc":"uJ6d","./_to-iobject":"g6sb","./_to-primitive":"y37I","./_has":"uHgd","./_ie8-dom-define":"o6Gq","./_descriptors":"P9Ib"}],"vn3S":[function(require,module,exports) {
var t=require("./_is-object"),e=require("./_an-object"),r=function(r,o){if(e(r),!t(o)&&null!==o)throw TypeError(o+": can't set as prototype!")};module.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,o){try{(o=require("./_ctx")(Function.call,require("./_object-gopd").f(Object.prototype,"__proto__").set,2))(t,[]),e=!(t instanceof Array)}catch(c){e=!0}return function(t,c){return r(t,c),e?t.__proto__=c:o(t,c),t}}({},!1):void 0),check:r};
},{"./_is-object":"M7z6","./_an-object":"eT53","./_ctx":"E3Kh","./_object-gopd":"uIjZ"}],"ogxf":[function(require,module,exports) {
var t=require("./_is-object"),o=require("./_set-proto").set;module.exports=function(r,e,p){var u,n=e.constructor;return n!==p&&"function"==typeof n&&(u=n.prototype)!==p.prototype&&t(u)&&o&&o(r,u),r};
},{"./_is-object":"M7z6","./_set-proto":"vn3S"}],"hWYB":[function(require,module,exports) {

"use strict";var e=require("./_global"),r=require("./_export"),t=require("./_redefine"),n=require("./_redefine-all"),i=require("./_meta"),u=require("./_for-of"),o=require("./_an-instance"),c=require("./_is-object"),a=require("./_fails"),s=require("./_iter-detect"),l=require("./_set-to-string-tag"),f=require("./_inherit-if-required");module.exports=function(d,h,q,_,p,g){var v=e[d],w=v,y=p?"set":"add",x=w&&w.prototype,E={},b=function(e){var r=x[e];t(x,e,"delete"==e?function(e){return!(g&&!c(e))&&r.call(this,0===e?0:e)}:"has"==e?function(e){return!(g&&!c(e))&&r.call(this,0===e?0:e)}:"get"==e?function(e){return g&&!c(e)?void 0:r.call(this,0===e?0:e)}:"add"==e?function(e){return r.call(this,0===e?0:e),this}:function(e,t){return r.call(this,0===e?0:e,t),this})};if("function"==typeof w&&(g||x.forEach&&!a(function(){(new w).entries().next()}))){var m=new w,j=m[y](g?{}:-0,1)!=m,C=a(function(){m.has(1)}),D=s(function(e){new w(e)}),F=!g&&a(function(){for(var e=new w,r=5;r--;)e[y](r,r);return!e.has(-0)});D||((w=h(function(e,r){o(e,w,d);var t=f(new v,e,w);return null!=r&&u(r,p,t[y],t),t})).prototype=x,x.constructor=w),(C||F)&&(b("delete"),b("has"),p&&b("get")),(F||j)&&b(y),g&&x.clear&&delete x.clear}else w=_.getConstructor(h,d,p,y),n(w.prototype,q),i.NEED=!0;return l(w,d),E[d]=w,r(r.G+r.W+r.F*(w!=v),E),g||_.setStrong(w,d,p),w};
},{"./_global":"qf4T","./_export":"izCb","./_redefine":"PHot","./_redefine-all":"J0Tl","./_meta":"AoVy","./_for-of":"Abke","./_an-instance":"yJTF","./_is-object":"M7z6","./_fails":"BXiR","./_iter-detect":"md62","./_set-to-string-tag":"rq3q","./_inherit-if-required":"ogxf"}],"ioKM":[function(require,module,exports) {
"use strict";var t=require("./_collection-strong"),e=require("./_validate-collection"),r="Map";module.exports=require("./_collection")(r,function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}},{get:function(n){var i=t.getEntry(e(this,r),n);return i&&i.v},set:function(n,i){return t.def(e(this,r),0===n?0:n,i)}},t,!0);
},{"./_collection-strong":"aIiY","./_validate-collection":"FW4z","./_collection":"hWYB"}],"mhol":[function(require,module,exports) {
"use strict";module.exports=require("./_library")||!require("./_fails")(function(){var e=Math.random();__defineSetter__.call(null,e,function(){}),delete require("./_global")[e]});
},{"./_library":"H21C","./_fails":"BXiR","./_global":"qf4T"}],"guoQ":[function(require,module,exports) {
"use strict";var e=require("./_export"),r=require("./_to-object"),t=require("./_a-function"),i=require("./_object-dp");require("./_descriptors")&&e(e.P+require("./_object-forced-pam"),"Object",{__defineGetter__:function(e,u){i.f(r(this),e,{get:t(u),enumerable:!0,configurable:!0})}});
},{"./_export":"izCb","./_to-object":"rfVX","./_a-function":"kYjc","./_object-dp":"nw8e","./_descriptors":"P9Ib","./_object-forced-pam":"mhol"}],"HMp9":[function(require,module,exports) {
"use strict";var e=require("./_export"),r=require("./_to-object"),t=require("./_a-function"),i=require("./_object-dp");require("./_descriptors")&&e(e.P+require("./_object-forced-pam"),"Object",{__defineSetter__:function(e,u){i.f(r(this),e,{set:t(u),enumerable:!0,configurable:!0})}});
},{"./_export":"izCb","./_to-object":"rfVX","./_a-function":"kYjc","./_object-dp":"nw8e","./_descriptors":"P9Ib","./_object-forced-pam":"mhol"}],"ljQU":[function(require,module,exports) {
var e=require("./_descriptors"),r=require("./_object-keys"),t=require("./_to-iobject"),o=require("./_object-pie").f;module.exports=function(u){return function(i){for(var c,n=t(i),s=r(n),f=s.length,l=0,p=[];f>l;)c=s[l++],e&&!o.call(n,c)||p.push(u?[c,n[c]]:n[c]);return p}};
},{"./_descriptors":"P9Ib","./_object-keys":"U9a7","./_to-iobject":"g6sb","./_object-pie":"vjRp"}],"gxEP":[function(require,module,exports) {
var r=require("./_export"),e=require("./_object-to-array")(!0);r(r.S,"Object",{entries:function(r){return e(r)}});
},{"./_export":"izCb","./_object-to-array":"ljQU"}],"Vzm0":[function(require,module,exports) {
var e=require("./_object-keys-internal"),r=require("./_enum-bug-keys").concat("length","prototype");exports.f=Object.getOwnPropertyNames||function(t){return e(t,r)};
},{"./_object-keys-internal":"vL0Z","./_enum-bug-keys":"bbv4"}],"EWMd":[function(require,module,exports) {
exports.f=Object.getOwnPropertySymbols;
},{}],"kABk":[function(require,module,exports) {
var e=require("./_object-gopn"),r=require("./_object-gops"),o=require("./_an-object"),t=require("./_global").Reflect;module.exports=t&&t.ownKeys||function(t){var c=e.f(o(t)),n=r.f;return n?c.concat(n(t)):c};
},{"./_object-gopn":"Vzm0","./_object-gops":"EWMd","./_an-object":"eT53","./_global":"qf4T"}],"BQD8":[function(require,module,exports) {
var e=require("./_export"),r=require("./_own-keys"),t=require("./_to-iobject"),o=require("./_object-gopd"),i=require("./_create-property");e(e.S,"Object",{getOwnPropertyDescriptors:function(e){for(var u,c,n=t(e),p=o.f,q=r(n),_={},a=0;q.length>a;)void 0!==(c=p(n,u=q[a++]))&&i(_,u,c);return _}});
},{"./_export":"izCb","./_own-keys":"kABk","./_to-iobject":"g6sb","./_object-gopd":"uIjZ","./_create-property":"JCwR"}],"HB2g":[function(require,module,exports) {
"use strict";var e=require("./_export"),r=require("./_to-object"),t=require("./_to-primitive"),i=require("./_object-gpo"),o=require("./_object-gopd").f;require("./_descriptors")&&e(e.P+require("./_object-forced-pam"),"Object",{__lookupGetter__:function(e){var u,_=r(this),c=t(e,!0);do{if(u=o(_,c))return u.get}while(_=i(_))}});
},{"./_export":"izCb","./_to-object":"rfVX","./_to-primitive":"y37I","./_object-gpo":"q6yw","./_object-gopd":"uIjZ","./_descriptors":"P9Ib","./_object-forced-pam":"mhol"}],"QF5J":[function(require,module,exports) {
"use strict";var e=require("./_export"),r=require("./_to-object"),t=require("./_to-primitive"),i=require("./_object-gpo"),o=require("./_object-gopd").f;require("./_descriptors")&&e(e.P+require("./_object-forced-pam"),"Object",{__lookupSetter__:function(e){var u,_=r(this),c=t(e,!0);do{if(u=o(_,c))return u.set}while(_=i(_))}});
},{"./_export":"izCb","./_to-object":"rfVX","./_to-primitive":"y37I","./_object-gpo":"q6yw","./_object-gopd":"uIjZ","./_descriptors":"P9Ib","./_object-forced-pam":"mhol"}],"zTK3":[function(require,module,exports) {
"use strict";var e=require("./_classof"),r={};r[require("./_wks")("toStringTag")]="z",r+""!="[object z]"&&require("./_redefine")(Object.prototype,"toString",function(){return"[object "+e(this)+"]"},!0);
},{"./_classof":"GM7B","./_wks":"AIP1","./_redefine":"PHot"}],"Ltmz":[function(require,module,exports) {
var r=require("./_export"),e=require("./_object-to-array")(!1);r(r.S,"Object",{values:function(r){return e(r)}});
},{"./_export":"izCb","./_object-to-array":"ljQU"}],"ExG3":[function(require,module,exports) {
var r=require("./_an-object"),e=require("./_a-function"),u=require("./_wks")("species");module.exports=function(n,o){var i,t=r(n).constructor;return void 0===t||null==(i=r(t)[u])?o:e(i)};
},{"./_an-object":"eT53","./_a-function":"kYjc","./_wks":"AIP1"}],"xcbV":[function(require,module,exports) {
module.exports=function(e,r,l){var a=void 0===l;switch(r.length){case 0:return a?e():e.call(l);case 1:return a?e(r[0]):e.call(l,r[0]);case 2:return a?e(r[0],r[1]):e.call(l,r[0],r[1]);case 3:return a?e(r[0],r[1],r[2]):e.call(l,r[0],r[1],r[2]);case 4:return a?e(r[0],r[1],r[2],r[3]):e.call(l,r[0],r[1],r[2],r[3])}return e.apply(l,r)};
},{}],"KY9y":[function(require,module,exports) {


var e,t,n,i=require("./_ctx"),o=require("./_invoke"),r=require("./_html"),s=require("./_dom-create"),a=require("./_global"),c=a.process,u=a.setImmediate,p=a.clearImmediate,f=a.MessageChannel,l=a.Dispatch,d=0,m={},h="onreadystatechange",g=function(){var e=+this;if(m.hasOwnProperty(e)){var t=m[e];delete m[e],t()}},v=function(e){g.call(e.data)};u&&p||(u=function(t){for(var n=[],i=1;arguments.length>i;)n.push(arguments[i++]);return m[++d]=function(){o("function"==typeof t?t:Function(t),n)},e(d),d},p=function(e){delete m[e]},"process"==require("./_cof")(c)?e=function(e){c.nextTick(i(g,e,1))}:l&&l.now?e=function(e){l.now(i(g,e,1))}:f?(n=(t=new f).port2,t.port1.onmessage=v,e=i(n.postMessage,n,1)):a.addEventListener&&"function"==typeof postMessage&&!a.importScripts?(e=function(e){a.postMessage(e+"","*")},a.addEventListener("message",v,!1)):e=h in s("script")?function(e){r.appendChild(s("script"))[h]=function(){r.removeChild(this),g.call(e)}}:function(e){setTimeout(i(g,e,1),0)}),module.exports={set:u,clear:p};
},{"./_ctx":"E3Kh","./_invoke":"xcbV","./_html":"xjB1","./_dom-create":"vZ6E","./_global":"qf4T","./_cof":"Z5df"}],"sFAp":[function(require,module,exports) {


var e=require("./_global"),t=require("./_task").set,r=e.MutationObserver||e.WebKitMutationObserver,n=e.process,o=e.Promise,a="process"==require("./_cof")(n);module.exports=function(){var i,c,s,v=function(){var e,t;for(a&&(e=n.domain)&&e.exit();i;){t=i.fn,i=i.next;try{t()}catch(r){throw i?s():c=void 0,r}}c=void 0,e&&e.enter()};if(a)s=function(){n.nextTick(v)};else if(!r||e.navigator&&e.navigator.standalone)if(o&&o.resolve){var u=o.resolve(void 0);s=function(){u.then(v)}}else s=function(){t.call(e,v)};else{var f=!0,l=document.createTextNode("");new r(v).observe(l,{characterData:!0}),s=function(){l.data=f=!f}}return function(e){var t={fn:e,next:void 0};c&&(c.next=t),i||(i=t,s()),c=t}};
},{"./_global":"qf4T","./_task":"KY9y","./_cof":"Z5df"}],"L7XN":[function(require,module,exports) {
"use strict";var r=require("./_a-function");function e(e){var o,t;this.promise=new e(function(r,e){if(void 0!==o||void 0!==t)throw TypeError("Bad Promise constructor");o=r,t=e}),this.resolve=r(o),this.reject=r(t)}module.exports.f=function(r){return new e(r)};
},{"./_a-function":"kYjc"}],"tyG8":[function(require,module,exports) {
module.exports=function(e){try{return{e:!1,v:e()}}catch(r){return{e:!0,v:r}}};
},{}],"O5uh":[function(require,module,exports) {

var e=require("./_global"),r=e.navigator;module.exports=r&&r.userAgent||"";
},{"./_global":"qf4T"}],"cNG8":[function(require,module,exports) {
var r=require("./_an-object"),e=require("./_is-object"),i=require("./_new-promise-capability");module.exports=function(o,t){if(r(o),e(t)&&t.constructor===o)return t;var u=i.f(o);return(0,u.resolve)(t),u.promise};
},{"./_an-object":"eT53","./_is-object":"M7z6","./_new-promise-capability":"L7XN"}],"Pjta":[function(require,module,exports) {


"use strict";var e,r,t,i,n=require("./_library"),o=require("./_global"),c=require("./_ctx"),s=require("./_classof"),u=require("./_export"),a=require("./_is-object"),_=require("./_a-function"),h=require("./_an-instance"),f=require("./_for-of"),l=require("./_species-constructor"),v=require("./_task").set,d=require("./_microtask")(),p=require("./_new-promise-capability"),m=require("./_perform"),q=require("./_user-agent"),y=require("./_promise-resolve"),j="Promise",w=o.TypeError,g=o.process,x=g&&g.versions,b=x&&x.v8||"",k=o[j],P="process"==s(g),F=function(){},S=r=p.f,E=!!function(){try{var e=k.resolve(1),r=(e.constructor={})[require("./_wks")("species")]=function(e){e(F,F)};return(P||"function"==typeof PromiseRejectionEvent)&&e.then(F)instanceof r&&0!==b.indexOf("6.6")&&-1===q.indexOf("Chrome/66")}catch(t){}}(),O=function(e){var r;return!(!a(e)||"function"!=typeof(r=e.then))&&r},R=function(e,r){if(!e._n){e._n=!0;var t=e._c;d(function(){for(var i=e._v,n=1==e._s,o=0,c=function(r){var t,o,c,s=n?r.ok:r.fail,u=r.resolve,a=r.reject,_=r.domain;try{s?(n||(2==e._h&&H(e),e._h=1),!0===s?t=i:(_&&_.enter(),t=s(i),_&&(_.exit(),c=!0)),t===r.promise?a(w("Promise-chain cycle")):(o=O(t))?o.call(t,u,a):u(t)):a(i)}catch(h){_&&!c&&_.exit(),a(h)}};t.length>o;)c(t[o++]);e._c=[],e._n=!1,r&&!e._h&&C(e)})}},C=function(e){v.call(o,function(){var r,t,i,n=e._v,c=G(e);if(c&&(r=m(function(){P?g.emit("unhandledRejection",n,e):(t=o.onunhandledrejection)?t({promise:e,reason:n}):(i=o.console)&&i.error&&i.error("Unhandled promise rejection",n)}),e._h=P||G(e)?2:1),e._a=void 0,c&&r.e)throw r.v})},G=function(e){return 1!==e._h&&0===(e._a||e._c).length},H=function(e){v.call(o,function(){var r;P?g.emit("rejectionHandled",e):(r=o.onrejectionhandled)&&r({promise:e,reason:e._v})})},T=function(e){var r=this;r._d||(r._d=!0,(r=r._w||r)._v=e,r._s=2,r._a||(r._a=r._c.slice()),R(r,!0))},U=function(e){var r,t=this;if(!t._d){t._d=!0,t=t._w||t;try{if(t===e)throw w("Promise can't be resolved itself");(r=O(e))?d(function(){var i={_w:t,_d:!1};try{r.call(e,c(U,i,1),c(T,i,1))}catch(n){T.call(i,n)}}):(t._v=e,t._s=1,R(t,!1))}catch(i){T.call({_w:t,_d:!1},i)}}};E||(k=function(r){h(this,k,j,"_h"),_(r),e.call(this);try{r(c(U,this,1),c(T,this,1))}catch(t){T.call(this,t)}},(e=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=require("./_redefine-all")(k.prototype,{then:function(e,r){var t=S(l(this,k));return t.ok="function"!=typeof e||e,t.fail="function"==typeof r&&r,t.domain=P?g.domain:void 0,this._c.push(t),this._a&&this._a.push(t),this._s&&R(this,!1),t.promise},catch:function(e){return this.then(void 0,e)}}),t=function(){var r=new e;this.promise=r,this.resolve=c(U,r,1),this.reject=c(T,r,1)},p.f=S=function(e){return e===k||e===i?new t(e):r(e)}),u(u.G+u.W+u.F*!E,{Promise:k}),require("./_set-to-string-tag")(k,j),require("./_set-species")(j),i=require("./_core")[j],u(u.S+u.F*!E,j,{reject:function(e){var r=S(this);return(0,r.reject)(e),r.promise}}),u(u.S+u.F*(n||!E),j,{resolve:function(e){return y(n&&this===i?k:this,e)}}),u(u.S+u.F*!(E&&require("./_iter-detect")(function(e){k.all(e).catch(F)})),j,{all:function(e){var r=this,t=S(r),i=t.resolve,n=t.reject,o=m(function(){var t=[],o=0,c=1;f(e,!1,function(e){var s=o++,u=!1;t.push(void 0),c++,r.resolve(e).then(function(e){u||(u=!0,t[s]=e,--c||i(t))},n)}),--c||i(t)});return o.e&&n(o.v),t.promise},race:function(e){var r=this,t=S(r),i=t.reject,n=m(function(){f(e,!1,function(e){r.resolve(e).then(t.resolve,i)})});return n.e&&i(n.v),t.promise}});
},{"./_library":"H21C","./_global":"qf4T","./_ctx":"E3Kh","./_classof":"GM7B","./_export":"izCb","./_is-object":"M7z6","./_a-function":"kYjc","./_an-instance":"yJTF","./_for-of":"Abke","./_species-constructor":"ExG3","./_task":"KY9y","./_microtask":"sFAp","./_new-promise-capability":"L7XN","./_perform":"tyG8","./_user-agent":"O5uh","./_promise-resolve":"cNG8","./_wks":"AIP1","./_redefine-all":"J0Tl","./_set-to-string-tag":"rq3q","./_set-species":"h4dH","./_core":"ss9A","./_iter-detect":"md62"}],"l1j0":[function(require,module,exports) {

"use strict";var r=require("./_export"),e=require("./_core"),t=require("./_global"),n=require("./_species-constructor"),i=require("./_promise-resolve");r(r.P+r.R,"Promise",{finally:function(r){var o=n(this,e.Promise||t.Promise),u="function"==typeof r;return this.then(u?function(e){return i(o,r()).then(function(){return e})}:r,u?function(e){return i(o,r()).then(function(){throw e})}:r)}});
},{"./_export":"izCb","./_core":"ss9A","./_global":"qf4T","./_species-constructor":"ExG3","./_promise-resolve":"cNG8"}],"WEVF":[function(require,module,exports) {
var e=require("./_is-object"),r=require("./_cof"),i=require("./_wks")("match");module.exports=function(o){var u;return e(o)&&(void 0!==(u=o[i])?!!u:"RegExp"==r(o))};
},{"./_is-object":"M7z6","./_cof":"Z5df","./_wks":"AIP1"}],"hgks":[function(require,module,exports) {
"use strict";var e=require("./_an-object");module.exports=function(){var i=e(this),r="";return i.global&&(r+="g"),i.ignoreCase&&(r+="i"),i.multiline&&(r+="m"),i.unicode&&(r+="u"),i.sticky&&(r+="y"),r};
},{"./_an-object":"eT53"}],"BenF":[function(require,module,exports) {

var e=require("./_global"),r=require("./_inherit-if-required"),i=require("./_object-dp").f,t=require("./_object-gopn").f,n=require("./_is-regexp"),o=require("./_flags"),u=e.RegExp,c=u,s=u.prototype,f=/a/g,a=/a/g,g=new u(f)!==f;if(require("./_descriptors")&&(!g||require("./_fails")(function(){return a[require("./_wks")("match")]=!1,u(f)!=f||u(a)==a||"/a/i"!=u(f,"i")}))){u=function(e,i){var t=this instanceof u,f=n(e),a=void 0===i;return!t&&f&&e.constructor===u&&a?e:r(g?new c(f&&!a?e.source:e,i):c((f=e instanceof u)?e.source:e,f&&a?o.call(e):i),t?this:s,u)};for(var p=function(e){e in u||i(u,e,{configurable:!0,get:function(){return c[e]},set:function(r){c[e]=r}})},q=t(c),_=0;q.length>_;)p(q[_++]);s.constructor=u,u.prototype=s,require("./_redefine")(e,"RegExp",u)}require("./_set-species")("RegExp");
},{"./_global":"qf4T","./_inherit-if-required":"ogxf","./_object-dp":"nw8e","./_object-gopn":"Vzm0","./_is-regexp":"WEVF","./_flags":"hgks","./_descriptors":"P9Ib","./_fails":"BXiR","./_wks":"AIP1","./_redefine":"PHot","./_set-species":"h4dH"}],"x5yM":[function(require,module,exports) {
var e=require("./_to-integer"),r=require("./_defined");module.exports=function(t){return function(n,i){var o,u,c=String(r(n)),d=e(i),a=c.length;return d<0||d>=a?t?"":void 0:(o=c.charCodeAt(d))<55296||o>56319||d+1===a||(u=c.charCodeAt(d+1))<56320||u>57343?t?c.charAt(d):o:t?c.slice(d,d+2):u-56320+(o-55296<<10)+65536}};
},{"./_to-integer":"yjVO","./_defined":"BjjL"}],"t3as":[function(require,module,exports) {
"use strict";var r=require("./_string-at")(!0);module.exports=function(t,e,n){return e+(n?r(t,e).length:1)};
},{"./_string-at":"x5yM"}],"sNFG":[function(require,module,exports) {
"use strict";var e=require("./_classof"),r=RegExp.prototype.exec;module.exports=function(t,o){var c=t.exec;if("function"==typeof c){var n=c.call(t,o);if("object"!=typeof n)throw new TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==e(t))throw new TypeError("RegExp#exec called on incompatible receiver");return r.call(t,o)};
},{"./_classof":"GM7B"}],"ZcPD":[function(require,module,exports) {
"use strict";var e=require("./_flags"),l=RegExp.prototype.exec,t=String.prototype.replace,r=l,a="lastIndex",n=function(){var e=/a/,t=/b*/g;return l.call(e,"a"),l.call(t,"a"),0!==e[a]||0!==t[a]}(),o=void 0!==/()??/.exec("")[1],c=n||o;c&&(r=function(r){var c,i,g,u,p=this;return o&&(i=new RegExp("^"+p.source+"$(?!\\s)",e.call(p))),n&&(c=p[a]),g=l.call(p,r),n&&g&&(p[a]=p.global?g.index+g[0].length:c),o&&g&&g.length>1&&t.call(g[0],i,function(){for(u=1;u<arguments.length-2;u++)void 0===arguments[u]&&(g[u]=void 0)}),g}),module.exports=r;
},{"./_flags":"hgks"}],"S07n":[function(require,module,exports) {
"use strict";var e=require("./_regexp-exec");require("./_export")({target:"RegExp",proto:!0,forced:e!==/./.exec},{exec:e});
},{"./_regexp-exec":"ZcPD","./_export":"izCb"}],"LmBS":[function(require,module,exports) {
"use strict";require("./es6.regexp.exec");var e=require("./_redefine"),r=require("./_hide"),n=require("./_fails"),t=require("./_defined"),u=require("./_wks"),i=require("./_regexp-exec"),c=u("species"),o=!n(function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}),a=function(){var e=/(?:)/,r=e.exec;e.exec=function(){return r.apply(this,arguments)};var n="ab".split(e);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();module.exports=function(l,f,p){var s=u(l),v=!n(function(){var e={};return e[s]=function(){return 7},7!=""[l](e)}),x=v?!n(function(){var e=!1,r=/a/;return r.exec=function(){return e=!0,null},"split"===l&&(r.constructor={},r.constructor[c]=function(){return r}),r[s](""),!e}):void 0;if(!v||!x||"replace"===l&&!o||"split"===l&&!a){var d=/./[s],q=p(t,s,""[l],function(e,r,n,t,u){return r.exec===i?v&&!u?{done:!0,value:d.call(r,n,t)}:{done:!0,value:e.call(n,r,t)}:{done:!1}}),g=q[0],_=q[1];e(String.prototype,l,g),r(RegExp.prototype,s,2==f?function(e,r){return _.call(e,this,r)}:function(e){return _.call(e,this)})}};
},{"./es6.regexp.exec":"S07n","./_redefine":"PHot","./_hide":"NXbe","./_fails":"BXiR","./_defined":"BjjL","./_wks":"AIP1","./_regexp-exec":"ZcPD"}],"RTfC":[function(require,module,exports) {
"use strict";var r=require("./_an-object"),e=require("./_to-length"),n=require("./_advance-string-index"),t=require("./_regexp-exec-abstract");require("./_fix-re-wks")("match",1,function(i,a,u,l){return[function(r){var e=i(this),n=null==r?void 0:r[a];return void 0!==n?n.call(r,e):new RegExp(r)[a](String(e))},function(i){var a=l(u,i,this);if(a.done)return a.value;var c=r(i),o=String(this);if(!c.global)return t(c,o);var s=c.unicode;c.lastIndex=0;for(var v,d=[],g=0;null!==(v=t(c,o));){var x=String(v[0]);d[g]=x,""===x&&(c.lastIndex=n(o,e(c.lastIndex),s)),g++}return 0===g?null:d}]});
},{"./_an-object":"eT53","./_to-length":"dJBs","./_advance-string-index":"t3as","./_regexp-exec-abstract":"sNFG","./_fix-re-wks":"LmBS"}],"KGao":[function(require,module,exports) {
var global = arguments[3];
var r=arguments[3],e=require("./_an-object"),t=require("./_to-object"),n=require("./_to-length"),i=require("./_to-integer"),a=require("./_advance-string-index"),u=require("./_regexp-exec-abstract"),c=Math.max,l=Math.min,o=Math.floor,v=/\$([$&`']|\d\d?|<[^>]*>)/g,s=/\$([$&`']|\d\d?)/g,g=function(r){return void 0===r?r:String(r)};require("./_fix-re-wks")("replace",2,function(r,d,f,h){return[function(e,t){var n=r(this),i=null==e?void 0:e[d];return void 0!==i?i.call(e,n,t):f.call(String(n),e,t)},function(r,t){var o=h(f,r,this,t);if(o.done)return o.value;var v=e(r),s=String(this),d="function"==typeof t;d||(t=String(t));var x=v.global;if(x){var b=v.unicode;v.lastIndex=0}for(var q=[];;){var S=u(v,s);if(null===S)break;if(q.push(S),!x)break;""===String(S[0])&&(v.lastIndex=a(s,n(v.lastIndex),b))}for(var _="",$=0,k=0;k<q.length;k++){S=q[k];for(var m=String(S[0]),A=c(l(i(S.index),s.length),0),I=[],M=1;M<S.length;M++)I.push(g(S[M]));var j=S.groups;if(d){var w=[m].concat(I,A,s);void 0!==j&&w.push(j);var y=String(t.apply(void 0,w))}else y=p(m,s,A,I,j,t);A>=$&&(_+=s.slice($,A)+y,$=A+m.length)}return _+s.slice($)}];function p(r,e,n,i,a,u){var c=n+r.length,l=i.length,g=s;return void 0!==a&&(a=t(a),g=v),f.call(u,g,function(t,u){var v;switch(u.charAt(0)){case"$":return"$";case"&":return r;case"`":return e.slice(0,n);case"'":return e.slice(c);case"<":v=a[u.slice(1,-1)];break;default:var s=+u;if(0===s)return t;if(s>l){var g=o(s/10);return 0===g?t:g<=l?void 0===i[g-1]?u.charAt(1):i[g-1]+u.charAt(1):t}v=i[s-1]}return void 0===v?"":v})}});
},{"./_an-object":"eT53","./_to-object":"rfVX","./_to-length":"dJBs","./_to-integer":"yjVO","./_advance-string-index":"t3as","./_regexp-exec-abstract":"sNFG","./_fix-re-wks":"LmBS"}],"aOHf":[function(require,module,exports) {
"use strict";var e=require("./_is-regexp"),r=require("./_an-object"),i=require("./_species-constructor"),n=require("./_advance-string-index"),t=require("./_to-length"),u=require("./_regexp-exec-abstract"),l=require("./_regexp-exec"),s=require("./_fails"),c=Math.min,a=[].push,o="split",g="length",h="lastIndex",d=4294967295,f=!s(function(){RegExp(d,"y")});require("./_fix-re-wks")("split",2,function(s,v,p,x){var q;return q="c"=="abbc"[o](/(b)*/)[1]||4!="test"[o](/(?:)/,-1)[g]||2!="ab"[o](/(?:ab)*/)[g]||4!="."[o](/(.?)(.?)/)[g]||"."[o](/()()/)[g]>1||""[o](/.?/)[g]?function(r,i){var n=String(this);if(void 0===r&&0===i)return[];if(!e(r))return p.call(n,r,i);for(var t,u,s,c=[],o=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(r.sticky?"y":""),f=0,v=void 0===i?d:i>>>0,x=new RegExp(r.source,o+"g");(t=l.call(x,n))&&!((u=x[h])>f&&(c.push(n.slice(f,t.index)),t[g]>1&&t.index<n[g]&&a.apply(c,t.slice(1)),s=t[0][g],f=u,c[g]>=v));)x[h]===t.index&&x[h]++;return f===n[g]?!s&&x.test("")||c.push(""):c.push(n.slice(f)),c[g]>v?c.slice(0,v):c}:"0"[o](void 0,0)[g]?function(e,r){return void 0===e&&0===r?[]:p.call(this,e,r)}:p,[function(e,r){var i=s(this),n=null==e?void 0:e[v];return void 0!==n?n.call(e,i,r):q.call(String(i),e,r)},function(e,l){var s=x(q,e,this,l,q!==p);if(s.done)return s.value;var a=r(e),o=String(this),g=i(a,RegExp),h=a.unicode,v=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(f?"y":"g"),_=new g(f?a:"^(?:"+a.source+")",v),b=void 0===l?d:l>>>0;if(0===b)return[];if(0===o.length)return null===u(_,o)?[o]:[];for(var m=0,y=0,w=[];y<o.length;){_.lastIndex=f?y:0;var E,I=u(_,f?o:o.slice(y));if(null===I||(E=c(t(_.lastIndex+(f?0:y)),o.length))===m)y=n(o,y,h);else{if(w.push(o.slice(m,y)),w.length===b)return w;for(var R=1;R<=I.length-1;R++)if(w.push(I[R]),w.length===b)return w;y=m=E}}return w.push(o.slice(m)),w}]});
},{"./_is-regexp":"WEVF","./_an-object":"eT53","./_species-constructor":"ExG3","./_advance-string-index":"t3as","./_to-length":"dJBs","./_regexp-exec-abstract":"sNFG","./_regexp-exec":"ZcPD","./_fails":"BXiR","./_fix-re-wks":"LmBS"}],"zutv":[function(require,module,exports) {
module.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};
},{}],"zOab":[function(require,module,exports) {
"use strict";var e=require("./_an-object"),r=require("./_same-value"),n=require("./_regexp-exec-abstract");require("./_fix-re-wks")("search",1,function(t,i,a,u){return[function(e){var r=t(this),n=null==e?void 0:e[i];return void 0!==n?n.call(e,r):new RegExp(e)[i](String(r))},function(t){var i=u(a,t,this);if(i.done)return i.value;var s=e(t),l=String(this),c=s.lastIndex;r(c,0)||(s.lastIndex=0);var v=n(s,l);return r(s.lastIndex,c)||(s.lastIndex=c),null===v?-1:v.index}]});
},{"./_an-object":"eT53","./_same-value":"zutv","./_regexp-exec-abstract":"sNFG","./_fix-re-wks":"LmBS"}],"pDhD":[function(require,module,exports) {
require("./_descriptors")&&"g"!=/./g.flags&&require("./_object-dp").f(RegExp.prototype,"flags",{configurable:!0,get:require("./_flags")});
},{"./_descriptors":"P9Ib","./_object-dp":"nw8e","./_flags":"hgks"}],"iflU":[function(require,module,exports) {

"use strict";require("./es6.regexp.flags");var e=require("./_an-object"),r=require("./_flags"),i=require("./_descriptors"),n="toString",t=/./[n],a=function(e){require("./_redefine")(RegExp.prototype,n,e,!0)};require("./_fails")(function(){return"/a/b"!=t.call({source:"a",flags:"b"})})?a(function(){var n=e(this);return"/".concat(n.source,"/","flags"in n?n.flags:!i&&n instanceof RegExp?r.call(n):void 0)}):t.name!=n&&a(function(){return t.call(this)});
},{"./es6.regexp.flags":"pDhD","./_an-object":"eT53","./_flags":"hgks","./_descriptors":"P9Ib","./_redefine":"PHot","./_fails":"BXiR"}],"coyu":[function(require,module,exports) {
"use strict";var e=require("./_collection-strong"),t=require("./_validate-collection"),r="Set";module.exports=require("./_collection")(r,function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(i){return e.def(t(this,r),i=0===i?0:i,i)}},e);
},{"./_collection-strong":"aIiY","./_validate-collection":"FW4z","./_collection":"hWYB"}],"AuE7":[function(require,module,exports) {
exports.f=require("./_wks");
},{"./_wks":"AIP1"}],"r4vV":[function(require,module,exports) {

var r=require("./_global"),e=require("./_core"),o=require("./_library"),i=require("./_wks-ext"),l=require("./_object-dp").f;module.exports=function(u){var a=e.Symbol||(e.Symbol=o?{}:r.Symbol||{});"_"==u.charAt(0)||u in a||l(a,u,{value:i.f(u)})};
},{"./_global":"qf4T","./_core":"ss9A","./_library":"H21C","./_wks-ext":"AuE7","./_object-dp":"nw8e"}],"jjwB":[function(require,module,exports) {
var e=require("./_object-keys"),r=require("./_object-gops"),o=require("./_object-pie");module.exports=function(t){var u=e(t),i=r.f;if(i)for(var c,f=i(t),a=o.f,l=0;f.length>l;)a.call(t,c=f[l++])&&u.push(c);return u};
},{"./_object-keys":"U9a7","./_object-gops":"EWMd","./_object-pie":"vjRp"}],"dvol":[function(require,module,exports) {
var e=require("./_to-iobject"),t=require("./_object-gopn").f,o={}.toString,r="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],n=function(e){try{return t(e)}catch(o){return r.slice()}};module.exports.f=function(c){return r&&"[object Window]"==o.call(c)?n(c):t(e(c))};
},{"./_to-iobject":"g6sb","./_object-gopn":"Vzm0"}],"uVn9":[function(require,module,exports) {

"use strict";var e=require("./_global"),r=require("./_has"),t=require("./_descriptors"),i=require("./_export"),n=require("./_redefine"),o=require("./_meta").KEY,u=require("./_fails"),s=require("./_shared"),f=require("./_set-to-string-tag"),c=require("./_uid"),a=require("./_wks"),l=require("./_wks-ext"),p=require("./_wks-define"),b=require("./_enum-keys"),y=require("./_is-array"),h=require("./_an-object"),_=require("./_is-object"),q=require("./_to-object"),g=require("./_to-iobject"),m=require("./_to-primitive"),v=require("./_property-desc"),d=require("./_object-create"),S=require("./_object-gopn-ext"),j=require("./_object-gopd"),O=require("./_object-gops"),w=require("./_object-dp"),k=require("./_object-keys"),P=j.f,F=w.f,E=S.f,N=e.Symbol,J=e.JSON,x=J&&J.stringify,I="prototype",T=a("_hidden"),C=a("toPrimitive"),M={}.propertyIsEnumerable,D=s("symbol-registry"),G=s("symbols"),K=s("op-symbols"),Q=Object[I],W="function"==typeof N&&!!O.f,Y=e.QObject,z=!Y||!Y[I]||!Y[I].findChild,A=t&&u(function(){return 7!=d(F({},"a",{get:function(){return F(this,"a",{value:7}).a}})).a})?function(e,r,t){var i=P(Q,r);i&&delete Q[r],F(e,r,t),i&&e!==Q&&F(Q,r,i)}:F,B=function(e){var r=G[e]=d(N[I]);return r._k=e,r},H=W&&"symbol"==typeof N.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof N},L=function(e,t,i){return e===Q&&L(K,t,i),h(e),t=m(t,!0),h(i),r(G,t)?(i.enumerable?(r(e,T)&&e[T][t]&&(e[T][t]=!1),i=d(i,{enumerable:v(0,!1)})):(r(e,T)||F(e,T,v(1,{})),e[T][t]=!0),A(e,t,i)):F(e,t,i)},R=function(e,r){h(e);for(var t,i=b(r=g(r)),n=0,o=i.length;o>n;)L(e,t=i[n++],r[t]);return e},U=function(e,r){return void 0===r?d(e):R(d(e),r)},V=function(e){var t=M.call(this,e=m(e,!0));return!(this===Q&&r(G,e)&&!r(K,e))&&(!(t||!r(this,e)||!r(G,e)||r(this,T)&&this[T][e])||t)},X=function(e,t){if(e=g(e),t=m(t,!0),e!==Q||!r(G,t)||r(K,t)){var i=P(e,t);return!i||!r(G,t)||r(e,T)&&e[T][t]||(i.enumerable=!0),i}},Z=function(e){for(var t,i=E(g(e)),n=[],u=0;i.length>u;)r(G,t=i[u++])||t==T||t==o||n.push(t);return n},$=function(e){for(var t,i=e===Q,n=E(i?K:g(e)),o=[],u=0;n.length>u;)!r(G,t=n[u++])||i&&!r(Q,t)||o.push(G[t]);return o};W||(n((N=function(){if(this instanceof N)throw TypeError("Symbol is not a constructor!");var e=c(arguments.length>0?arguments[0]:void 0),i=function(t){this===Q&&i.call(K,t),r(this,T)&&r(this[T],e)&&(this[T][e]=!1),A(this,e,v(1,t))};return t&&z&&A(Q,e,{configurable:!0,set:i}),B(e)})[I],"toString",function(){return this._k}),j.f=X,w.f=L,require("./_object-gopn").f=S.f=Z,require("./_object-pie").f=V,O.f=$,t&&!require("./_library")&&n(Q,"propertyIsEnumerable",V,!0),l.f=function(e){return B(a(e))}),i(i.G+i.W+i.F*!W,{Symbol:N});for(var ee="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),re=0;ee.length>re;)a(ee[re++]);for(var te=k(a.store),ie=0;te.length>ie;)p(te[ie++]);i(i.S+i.F*!W,"Symbol",{for:function(e){return r(D,e+="")?D[e]:D[e]=N(e)},keyFor:function(e){if(!H(e))throw TypeError(e+" is not a symbol!");for(var r in D)if(D[r]===e)return r},useSetter:function(){z=!0},useSimple:function(){z=!1}}),i(i.S+i.F*!W,"Object",{create:U,defineProperty:L,defineProperties:R,getOwnPropertyDescriptor:X,getOwnPropertyNames:Z,getOwnPropertySymbols:$});var ne=u(function(){O.f(1)});i(i.S+i.F*ne,"Object",{getOwnPropertySymbols:function(e){return O.f(q(e))}}),J&&i(i.S+i.F*(!W||u(function(){var e=N();return"[null]"!=x([e])||"{}"!=x({a:e})||"{}"!=x(Object(e))})),"JSON",{stringify:function(e){for(var r,t,i=[e],n=1;arguments.length>n;)i.push(arguments[n++]);if(t=r=i[1],(_(r)||void 0!==e)&&!H(e))return y(r)||(r=function(e,r){if("function"==typeof t&&(r=t.call(this,e,r)),!H(r))return r}),i[1]=r,x.apply(J,i)}}),N[I][C]||require("./_hide")(N[I],C,N[I].valueOf),f(N,"Symbol"),f(Math,"Math",!0),f(e.JSON,"JSON",!0);
},{"./_global":"qf4T","./_has":"uHgd","./_descriptors":"P9Ib","./_export":"izCb","./_redefine":"PHot","./_meta":"AoVy","./_fails":"BXiR","./_shared":"zGcK","./_set-to-string-tag":"rq3q","./_uid":"U49f","./_wks":"AIP1","./_wks-ext":"AuE7","./_wks-define":"r4vV","./_enum-keys":"jjwB","./_is-array":"JTrm","./_an-object":"eT53","./_is-object":"M7z6","./_to-object":"rfVX","./_to-iobject":"g6sb","./_to-primitive":"y37I","./_property-desc":"uJ6d","./_object-create":"sYaK","./_object-gopn-ext":"dvol","./_object-gopd":"uIjZ","./_object-gops":"EWMd","./_object-dp":"nw8e","./_object-keys":"U9a7","./_object-gopn":"Vzm0","./_object-pie":"vjRp","./_library":"H21C","./_hide":"NXbe"}],"DlMC":[function(require,module,exports) {
require("./_wks-define")("asyncIterator");
},{"./_wks-define":"r4vV"}],"UH4U":[function(require,module,exports) {
"use strict";var r=require("./_to-integer"),e=require("./_defined");module.exports=function(t){var i=String(e(this)),n="",o=r(t);if(o<0||o==1/0)throw RangeError("Count can't be negative");for(;o>0;(o>>>=1)&&(i+=i))1&o&&(n+=i);return n};
},{"./_to-integer":"yjVO","./_defined":"BjjL"}],"lQnl":[function(require,module,exports) {
var e=require("./_to-length"),r=require("./_string-repeat"),t=require("./_defined");module.exports=function(i,n,l,g){var u=String(t(i)),a=u.length,h=void 0===l?" ":String(l),o=e(n);if(o<=a||""==h)return u;var c=o-a,d=r.call(h,Math.ceil(c/h.length));return d.length>c&&(d=d.slice(0,c)),g?d+u:u+d};
},{"./_to-length":"dJBs","./_string-repeat":"UH4U","./_defined":"BjjL"}],"SWNE":[function(require,module,exports) {
"use strict";var r=require("./_export"),e=require("./_string-pad"),t=require("./_user-agent"),i=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(t);r(r.P+r.F*i,"String",{padStart:function(r){return e(this,r,arguments.length>1?arguments[1]:void 0,!0)}});
},{"./_export":"izCb","./_string-pad":"lQnl","./_user-agent":"O5uh"}],"n20m":[function(require,module,exports) {
"use strict";var r=require("./_export"),e=require("./_string-pad"),i=require("./_user-agent"),t=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(i);r(r.P+r.F*t,"String",{padEnd:function(r){return e(this,r,arguments.length>1?arguments[1]:void 0,!1)}});
},{"./_export":"izCb","./_string-pad":"lQnl","./_user-agent":"O5uh"}],"ECro":[function(require,module,exports) {
module.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff";
},{}],"y5m2":[function(require,module,exports) {
var r=require("./_export"),e=require("./_defined"),i=require("./_fails"),n=require("./_string-ws"),t="["+n+"]",u="​",o=RegExp("^"+t+t+"*"),p=RegExp(t+t+"*$"),a=function(e,t,o){var p={},a=i(function(){return!!n[e]()||u[e]()!=u}),f=p[e]=a?t(c):n[e];o&&(p[o]=f),r(r.P+r.F*a,"String",p)},c=a.trim=function(r,i){return r=String(e(r)),1&i&&(r=r.replace(o,"")),2&i&&(r=r.replace(p,"")),r};module.exports=a;
},{"./_export":"izCb","./_defined":"BjjL","./_fails":"BXiR","./_string-ws":"ECro"}],"ppxd":[function(require,module,exports) {
"use strict";require("./_string-trim")("trimLeft",function(t){return function(){return t(this,1)}},"trimStart");
},{"./_string-trim":"y5m2"}],"hxx1":[function(require,module,exports) {
"use strict";require("./_string-trim")("trimRight",function(t){return function(){return t(this,2)}},"trimEnd");
},{"./_string-trim":"y5m2"}],"fero":[function(require,module,exports) {

for(var r,a=require("./_global"),t=require("./_hide"),e=require("./_uid"),y=e("typed_array"),i=e("view"),A=!(!a.ArrayBuffer||!a.DataView),o=A,p=0,l=9,n="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");p<l;)(r=a[n[p++]])?(t(r.prototype,y,!0),t(r.prototype,i,!0)):o=!1;module.exports={ABV:A,CONSTR:o,TYPED:y,VIEW:i};
},{"./_global":"qf4T","./_hide":"NXbe","./_uid":"U49f"}],"zj2i":[function(require,module,exports) {
var r=require("./_to-integer"),e=require("./_to-length");module.exports=function(t){if(void 0===t)return 0;var n=r(t),o=e(n);if(n!==o)throw RangeError("Wrong length!");return o};
},{"./_to-integer":"yjVO","./_to-length":"dJBs"}],"hphS":[function(require,module,exports) {
"use strict";var e=require("./_to-object"),t=require("./_to-absolute-index"),r=require("./_to-length");module.exports=function(o){for(var i=e(this),u=r(i.length),n=arguments.length,d=t(n>1?arguments[1]:void 0,u),l=n>2?arguments[2]:void 0,s=void 0===l?u:t(l,u);s>d;)i[d++]=o;return i};
},{"./_to-object":"rfVX","./_to-absolute-index":"vfEH","./_to-length":"dJBs"}],"Ujpk":[function(require,module,exports) {

"use strict";var t=require("./_global"),n=require("./_descriptors"),r=require("./_library"),e=require("./_typed"),i=require("./_hide"),o=require("./_redefine-all"),u=require("./_fails"),f=require("./_an-instance"),s=require("./_to-integer"),c=require("./_to-length"),a=require("./_to-index"),h=require("./_object-gopn").f,l=require("./_object-dp").f,g=require("./_array-fill"),_=require("./_set-to-string-tag"),q="ArrayBuffer",v="DataView",w="prototype",I="Wrong length!",b="Wrong index!",y=t[q],p=t[v],d=t.Math,U=t.RangeError,N=t.Infinity,x=y,A=d.abs,F=d.pow,W=d.floor,V=d.log,j=d.LN2,B="buffer",E="byteLength",L="byteOffset",m=n?"_b":B,D=n?"_l":E,M=n?"_o":L;function O(t,n,r){var e,i,o,u=new Array(r),f=8*r-n-1,s=(1<<f)-1,c=s>>1,a=23===n?F(2,-24)-F(2,-77):0,h=0,l=t<0||0===t&&1/t<0?1:0;for((t=A(t))!=t||t===N?(i=t!=t?1:0,e=s):(e=W(V(t)/j),t*(o=F(2,-e))<1&&(e--,o*=2),(t+=e+c>=1?a/o:a*F(2,1-c))*o>=2&&(e++,o/=2),e+c>=s?(i=0,e=s):e+c>=1?(i=(t*o-1)*F(2,n),e+=c):(i=t*F(2,c-1)*F(2,n),e=0));n>=8;u[h++]=255&i,i/=256,n-=8);for(e=e<<n|i,f+=n;f>0;u[h++]=255&e,e/=256,f-=8);return u[--h]|=128*l,u}function R(t,n,r){var e,i=8*r-n-1,o=(1<<i)-1,u=o>>1,f=i-7,s=r-1,c=t[s--],a=127&c;for(c>>=7;f>0;a=256*a+t[s],s--,f-=8);for(e=a&(1<<-f)-1,a>>=-f,f+=n;f>0;e=256*e+t[s],s--,f-=8);if(0===a)a=1-u;else{if(a===o)return e?NaN:c?-N:N;e+=F(2,n),a-=u}return(c?-1:1)*e*F(2,a-n)}function k(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]}function z(t){return[255&t]}function C(t){return[255&t,t>>8&255]}function G(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function H(t){return O(t,52,8)}function J(t){return O(t,23,4)}function K(t,n,r){l(t[w],n,{get:function(){return this[r]}})}function P(t,n,r,e){var i=a(+r);if(i+n>t[D])throw U(b);var o=t[m]._b,u=i+t[M],f=o.slice(u,u+n);return e?f:f.reverse()}function Q(t,n,r,e,i,o){var u=a(+r);if(u+n>t[D])throw U(b);for(var f=t[m]._b,s=u+t[M],c=e(+i),h=0;h<n;h++)f[s+h]=c[o?h:n-h-1]}if(e.ABV){if(!u(function(){y(1)})||!u(function(){new y(-1)})||u(function(){return new y,new y(1.5),new y(NaN),y.name!=q})){for(var S,T=(y=function(t){return f(this,y),new x(a(t))})[w]=x[w],X=h(x),Y=0;X.length>Y;)(S=X[Y++])in y||i(y,S,x[S]);r||(T.constructor=y)}var Z=new p(new y(2)),$=p[w].setInt8;Z.setInt8(0,2147483648),Z.setInt8(1,2147483649),!Z.getInt8(0)&&Z.getInt8(1)||o(p[w],{setInt8:function(t,n){$.call(this,t,n<<24>>24)},setUint8:function(t,n){$.call(this,t,n<<24>>24)}},!0)}else y=function(t){f(this,y,q);var n=a(t);this._b=g.call(new Array(n),0),this[D]=n},p=function(t,n,r){f(this,p,v),f(t,y,v);var e=t[D],i=s(n);if(i<0||i>e)throw U("Wrong offset!");if(i+(r=void 0===r?e-i:c(r))>e)throw U(I);this[m]=t,this[M]=i,this[D]=r},n&&(K(y,E,"_l"),K(p,B,"_b"),K(p,E,"_l"),K(p,L,"_o")),o(p[w],{getInt8:function(t){return P(this,1,t)[0]<<24>>24},getUint8:function(t){return P(this,1,t)[0]},getInt16:function(t){var n=P(this,2,t,arguments[1]);return(n[1]<<8|n[0])<<16>>16},getUint16:function(t){var n=P(this,2,t,arguments[1]);return n[1]<<8|n[0]},getInt32:function(t){return k(P(this,4,t,arguments[1]))},getUint32:function(t){return k(P(this,4,t,arguments[1]))>>>0},getFloat32:function(t){return R(P(this,4,t,arguments[1]),23,4)},getFloat64:function(t){return R(P(this,8,t,arguments[1]),52,8)},setInt8:function(t,n){Q(this,1,t,z,n)},setUint8:function(t,n){Q(this,1,t,z,n)},setInt16:function(t,n){Q(this,2,t,C,n,arguments[2])},setUint16:function(t,n){Q(this,2,t,C,n,arguments[2])},setInt32:function(t,n){Q(this,4,t,G,n,arguments[2])},setUint32:function(t,n){Q(this,4,t,G,n,arguments[2])},setFloat32:function(t,n){Q(this,4,t,J,n,arguments[2])},setFloat64:function(t,n){Q(this,8,t,H,n,arguments[2])}});_(y,q),_(p,v),i(p[w],e.VIEW,!0),exports[q]=y,exports[v]=p;
},{"./_global":"qf4T","./_descriptors":"P9Ib","./_library":"H21C","./_typed":"fero","./_hide":"NXbe","./_redefine-all":"J0Tl","./_fails":"BXiR","./_an-instance":"yJTF","./_to-integer":"yjVO","./_to-length":"dJBs","./_to-index":"zj2i","./_object-gopn":"Vzm0","./_object-dp":"nw8e","./_array-fill":"hphS","./_set-to-string-tag":"rq3q"}],"NJ0a":[function(require,module,exports) {
"use strict";var e=require("./_export"),r=require("./_typed"),i=require("./_typed-buffer"),t=require("./_an-object"),u=require("./_to-absolute-index"),n=require("./_to-length"),s=require("./_is-object"),o=require("./_global").ArrayBuffer,f=require("./_species-constructor"),c=i.ArrayBuffer,a=i.DataView,q=r.ABV&&o.isView,_=c.prototype.slice,l=r.VIEW,y="ArrayBuffer";e(e.G+e.W+e.F*(o!==c),{ArrayBuffer:c}),e(e.S+e.F*!r.CONSTR,y,{isView:function(e){return q&&q(e)||s(e)&&l in e}}),e(e.P+e.U+e.F*require("./_fails")(function(){return!new c(2).slice(1,void 0).byteLength}),y,{slice:function(e,r){if(void 0!==_&&void 0===r)return _.call(t(this),e);for(var i=t(this).byteLength,s=u(e,i),o=u(void 0===r?i:r,i),q=new(f(this,c))(n(o-s)),l=new a(this),y=new a(q),b=0;s<o;)y.setUint8(b++,l.getUint8(s++));return q}}),require("./_set-species")(y);
},{"./_export":"izCb","./_typed":"fero","./_typed-buffer":"Ujpk","./_an-object":"eT53","./_to-absolute-index":"vfEH","./_to-length":"dJBs","./_is-object":"M7z6","./_global":"qf4T","./_species-constructor":"ExG3","./_fails":"BXiR","./_set-species":"h4dH"}],"AuPh":[function(require,module,exports) {
var e=require("./_ctx"),r=require("./_iobject"),t=require("./_to-object"),i=require("./_to-length"),u=require("./_array-species-create");module.exports=function(n,c){var s=1==n,a=2==n,o=3==n,f=4==n,l=6==n,q=5==n||l,_=c||u;return function(u,c,h){for(var v,p,b=t(u),d=r(b),g=e(c,h,3),j=i(d.length),x=0,m=s?_(u,j):a?_(u,0):void 0;j>x;x++)if((q||x in d)&&(p=g(v=d[x],x,b),n))if(s)m[x]=p;else if(p)switch(n){case 3:return!0;case 5:return v;case 6:return x;case 2:m.push(v)}else if(f)return!1;return l?-1:o||f?f:m}};
},{"./_ctx":"E3Kh","./_iobject":"nGau","./_to-object":"rfVX","./_to-length":"dJBs","./_array-species-create":"igas"}],"wVEN":[function(require,module,exports) {
"use strict";var e=require("./_add-to-unscopables"),r=require("./_iter-step"),t=require("./_iterators"),i=require("./_to-iobject");module.exports=require("./_iter-define")(Array,"Array",function(e,r){this._t=i(e),this._i=0,this._k=r},function(){var e=this._t,t=this._k,i=this._i++;return!e||i>=e.length?(this._t=void 0,r(1)):r(0,"keys"==t?i:"values"==t?e[i]:[i,e[i]])},"values"),t.Arguments=t.Array,e("keys"),e("values"),e("entries");
},{"./_add-to-unscopables":"Z7eD","./_iter-step":"x8b3","./_iterators":"JO4d","./_to-iobject":"g6sb","./_iter-define":"mH0U"}],"Oppn":[function(require,module,exports) {
"use strict";var e=require("./_to-object"),t=require("./_to-absolute-index"),i=require("./_to-length");module.exports=[].copyWithin||function(r,o){var n=e(this),u=i(n.length),h=t(r,u),l=t(o,u),d=arguments.length>2?arguments[2]:void 0,s=Math.min((void 0===d?u:t(d,u))-l,u-h),a=1;for(l<h&&h<l+s&&(a=-1,l+=s-1,h+=s-1);s-- >0;)l in n?n[h]=n[l]:delete n[h],h+=a,l+=a;return n};
},{"./_to-object":"rfVX","./_to-absolute-index":"vfEH","./_to-length":"dJBs"}],"fd04":[function(require,module,exports) {
var global = arguments[3];
var e=arguments[3];if(require("./_descriptors")){var r=require("./_library"),t=(e=require("./_global"),require("./_fails")),n=require("./_export"),i=require("./_typed"),o=require("./_typed-buffer"),u=require("./_ctx"),c=require("./_an-instance"),f=require("./_property-desc"),a=require("./_hide"),l=require("./_redefine-all"),s=require("./_to-integer"),h=require("./_to-length"),d=require("./_to-index"),g=require("./_to-absolute-index"),_=require("./_to-primitive"),v=require("./_has"),p=require("./_classof"),y=require("./_is-object"),q=require("./_to-object"),w=require("./_is-array-iter"),b=require("./_object-create"),S=require("./_object-gpo"),E=require("./_object-gopn").f,m=require("./core.get-iterator-method"),x=require("./_uid"),L=require("./_wks"),P=require("./_array-methods"),j=require("./_array-includes"),T=require("./_species-constructor"),F=require("./es6.array.iterator"),O=require("./_iterators"),A=require("./_iter-detect"),R=require("./_set-species"),B=require("./_array-fill"),I=require("./_array-copy-within"),M=require("./_object-dp"),W=require("./_object-gopd"),N=M.f,Y=W.f,k=e.RangeError,D=e.TypeError,V=e.Uint8Array,C="ArrayBuffer",U="Shared"+C,G="BYTES_PER_ELEMENT",z="prototype",H=Array[z],J=o.ArrayBuffer,K=o.DataView,Q=P(0),X=P(2),Z=P(3),$=P(4),ee=P(5),re=P(6),te=j(!0),ne=j(!1),ie=F.values,oe=F.keys,ue=F.entries,ce=H.lastIndexOf,fe=H.reduce,ae=H.reduceRight,le=H.join,se=H.sort,he=H.slice,de=H.toString,ge=H.toLocaleString,_e=L("iterator"),ve=L("toStringTag"),pe=x("typed_constructor"),ye=x("def_constructor"),qe=i.CONSTR,we=i.TYPED,be=i.VIEW,Se="Wrong length!",Ee=P(1,function(e,r){return je(T(e,e[ye]),r)}),me=t(function(){return 1===new V(new Uint16Array([1]).buffer)[0]}),xe=!!V&&!!V[z].set&&t(function(){new V(1).set({})}),Le=function(e,r){var t=s(e);if(t<0||t%r)throw k("Wrong offset!");return t},Pe=function(e){if(y(e)&&we in e)return e;throw D(e+" is not a typed array!")},je=function(e,r){if(!(y(e)&&pe in e))throw D("It is not a typed array constructor!");return new e(r)},Te=function(e,r){return Fe(T(e,e[ye]),r)},Fe=function(e,r){for(var t=0,n=r.length,i=je(e,n);n>t;)i[t]=r[t++];return i},Oe=function(e,r,t){N(e,r,{get:function(){return this._d[t]}})},Ae=function(e){var r,t,n,i,o,c,f=q(e),a=arguments.length,l=a>1?arguments[1]:void 0,s=void 0!==l,d=m(f);if(null!=d&&!w(d)){for(c=d.call(f),n=[],r=0;!(o=c.next()).done;r++)n.push(o.value);f=n}for(s&&a>2&&(l=u(l,arguments[2],2)),r=0,t=h(f.length),i=je(this,t);t>r;r++)i[r]=s?l(f[r],r):f[r];return i},Re=function(){for(var e=0,r=arguments.length,t=je(this,r);r>e;)t[e]=arguments[e++];return t},Be=!!V&&t(function(){ge.call(new V(1))}),Ie=function(){return ge.apply(Be?he.call(Pe(this)):Pe(this),arguments)},Me={copyWithin:function(e,r){return I.call(Pe(this),e,r,arguments.length>2?arguments[2]:void 0)},every:function(e){return $(Pe(this),e,arguments.length>1?arguments[1]:void 0)},fill:function(e){return B.apply(Pe(this),arguments)},filter:function(e){return Te(this,X(Pe(this),e,arguments.length>1?arguments[1]:void 0))},find:function(e){return ee(Pe(this),e,arguments.length>1?arguments[1]:void 0)},findIndex:function(e){return re(Pe(this),e,arguments.length>1?arguments[1]:void 0)},forEach:function(e){Q(Pe(this),e,arguments.length>1?arguments[1]:void 0)},indexOf:function(e){return ne(Pe(this),e,arguments.length>1?arguments[1]:void 0)},includes:function(e){return te(Pe(this),e,arguments.length>1?arguments[1]:void 0)},join:function(e){return le.apply(Pe(this),arguments)},lastIndexOf:function(e){return ce.apply(Pe(this),arguments)},map:function(e){return Ee(Pe(this),e,arguments.length>1?arguments[1]:void 0)},reduce:function(e){return fe.apply(Pe(this),arguments)},reduceRight:function(e){return ae.apply(Pe(this),arguments)},reverse:function(){for(var e,r=Pe(this).length,t=Math.floor(r/2),n=0;n<t;)e=this[n],this[n++]=this[--r],this[r]=e;return this},some:function(e){return Z(Pe(this),e,arguments.length>1?arguments[1]:void 0)},sort:function(e){return se.call(Pe(this),e)},subarray:function(e,r){var t=Pe(this),n=t.length,i=g(e,n);return new(T(t,t[ye]))(t.buffer,t.byteOffset+i*t.BYTES_PER_ELEMENT,h((void 0===r?n:g(r,n))-i))}},We=function(e,r){return Te(this,he.call(Pe(this),e,r))},Ne=function(e){Pe(this);var r=Le(arguments[1],1),t=this.length,n=q(e),i=h(n.length),o=0;if(i+r>t)throw k(Se);for(;o<i;)this[r+o]=n[o++]},Ye={entries:function(){return ue.call(Pe(this))},keys:function(){return oe.call(Pe(this))},values:function(){return ie.call(Pe(this))}},ke=function(e,r){return y(e)&&e[we]&&"symbol"!=typeof r&&r in e&&String(+r)==String(r)},De=function(e,r){return ke(e,r=_(r,!0))?f(2,e[r]):Y(e,r)},Ve=function(e,r,t){return!(ke(e,r=_(r,!0))&&y(t)&&v(t,"value"))||v(t,"get")||v(t,"set")||t.configurable||v(t,"writable")&&!t.writable||v(t,"enumerable")&&!t.enumerable?N(e,r,t):(e[r]=t.value,e)};qe||(W.f=De,M.f=Ve),n(n.S+n.F*!qe,"Object",{getOwnPropertyDescriptor:De,defineProperty:Ve}),t(function(){de.call({})})&&(de=ge=function(){return le.call(this)});var Ce=l({},Me);l(Ce,Ye),a(Ce,_e,Ye.values),l(Ce,{slice:We,set:Ne,constructor:function(){},toString:de,toLocaleString:Ie}),Oe(Ce,"buffer","b"),Oe(Ce,"byteOffset","o"),Oe(Ce,"byteLength","l"),Oe(Ce,"length","e"),N(Ce,ve,{get:function(){return this[we]}}),module.exports=function(o,u,f,l){var s=o+((l=!!l)?"Clamped":"")+"Array",g="get"+o,_="set"+o,v=e[s],q=v||{},w=v&&S(v),m=!v||!i.ABV,x={},L=v&&v[z],P=function(e,r){N(e,r,{get:function(){return function(e,r){var t=e._d;return t.v[g](r*u+t.o,me)}(this,r)},set:function(e){return function(e,r,t){var n=e._d;l&&(t=(t=Math.round(t))<0?0:t>255?255:255&t),n.v[_](r*u+n.o,t,me)}(this,r,e)},enumerable:!0})};m?(v=f(function(e,r,t,n){c(e,v,s,"_d");var i,o,f,l,g=0,_=0;if(y(r)){if(!(r instanceof J||(l=p(r))==C||l==U))return we in r?Fe(v,r):Ae.call(v,r);i=r,_=Le(t,u);var q=r.byteLength;if(void 0===n){if(q%u)throw k(Se);if((o=q-_)<0)throw k(Se)}else if((o=h(n)*u)+_>q)throw k(Se);f=o/u}else f=d(r),i=new J(o=f*u);for(a(e,"_d",{b:i,o:_,l:o,e:f,v:new K(i)});g<f;)P(e,g++)}),L=v[z]=b(Ce),a(L,"constructor",v)):t(function(){v(1)})&&t(function(){new v(-1)})&&A(function(e){new v,new v(null),new v(1.5),new v(e)},!0)||(v=f(function(e,r,t,n){var i;return c(e,v,s),y(r)?r instanceof J||(i=p(r))==C||i==U?void 0!==n?new q(r,Le(t,u),n):void 0!==t?new q(r,Le(t,u)):new q(r):we in r?Fe(v,r):Ae.call(v,r):new q(d(r))}),Q(w!==Function.prototype?E(q).concat(E(w)):E(q),function(e){e in v||a(v,e,q[e])}),v[z]=L,r||(L.constructor=v));var j=L[_e],T=!!j&&("values"==j.name||null==j.name),F=Ye.values;a(v,pe,!0),a(L,we,s),a(L,be,!0),a(L,ye,v),(l?new v(1)[ve]==s:ve in L)||N(L,ve,{get:function(){return s}}),x[s]=v,n(n.G+n.W+n.F*(v!=q),x),n(n.S,s,{BYTES_PER_ELEMENT:u}),n(n.S+n.F*t(function(){q.of.call(v,1)}),s,{from:Ae,of:Re}),G in L||a(L,G,u),n(n.P,s,Me),R(s),n(n.P+n.F*xe,s,{set:Ne}),n(n.P+n.F*!T,s,Ye),r||L.toString==de||(L.toString=de),n(n.P+n.F*t(function(){new v(1).slice()}),s,{slice:We}),n(n.P+n.F*(t(function(){return[1,2].toLocaleString()!=new v([1,2]).toLocaleString()})||!t(function(){L.toLocaleString.call([1,2])})),s,{toLocaleString:Ie}),O[s]=T?j:F,r||T||a(L,_e,F)}}else module.exports=function(){};
},{"./_descriptors":"P9Ib","./_library":"H21C","./_global":"qf4T","./_fails":"BXiR","./_export":"izCb","./_typed":"fero","./_typed-buffer":"Ujpk","./_ctx":"E3Kh","./_an-instance":"yJTF","./_property-desc":"uJ6d","./_hide":"NXbe","./_redefine-all":"J0Tl","./_to-integer":"yjVO","./_to-length":"dJBs","./_to-index":"zj2i","./_to-absolute-index":"vfEH","./_to-primitive":"y37I","./_has":"uHgd","./_classof":"GM7B","./_is-object":"M7z6","./_to-object":"rfVX","./_is-array-iter":"B0pB","./_object-create":"sYaK","./_object-gpo":"q6yw","./_object-gopn":"Vzm0","./core.get-iterator-method":"ia42","./_uid":"U49f","./_wks":"AIP1","./_array-methods":"AuPh","./_array-includes":"Ca7J","./_species-constructor":"ExG3","./es6.array.iterator":"wVEN","./_iterators":"JO4d","./_iter-detect":"md62","./_set-species":"h4dH","./_array-fill":"hphS","./_array-copy-within":"Oppn","./_object-dp":"nw8e","./_object-gopd":"uIjZ"}],"wqMZ":[function(require,module,exports) {
require("./_typed-array")("Int8",1,function(r){return function(n,t,e){return r(this,n,t,e)}});
},{"./_typed-array":"fd04"}],"QTtY":[function(require,module,exports) {
require("./_typed-array")("Uint8",1,function(r){return function(n,t,e){return r(this,n,t,e)}});
},{"./_typed-array":"fd04"}],"Kqgs":[function(require,module,exports) {
require("./_typed-array")("Uint8",1,function(r){return function(n,t,e){return r(this,n,t,e)}},!0);
},{"./_typed-array":"fd04"}],"fEGw":[function(require,module,exports) {
require("./_typed-array")("Int16",2,function(r){return function(n,t,e){return r(this,n,t,e)}});
},{"./_typed-array":"fd04"}],"xyd6":[function(require,module,exports) {
require("./_typed-array")("Uint16",2,function(r){return function(n,t,e){return r(this,n,t,e)}});
},{"./_typed-array":"fd04"}],"hIko":[function(require,module,exports) {
require("./_typed-array")("Int32",4,function(r){return function(n,t,e){return r(this,n,t,e)}});
},{"./_typed-array":"fd04"}],"tNPN":[function(require,module,exports) {
require("./_typed-array")("Uint32",4,function(r){return function(n,t,e){return r(this,n,t,e)}});
},{"./_typed-array":"fd04"}],"wisA":[function(require,module,exports) {
require("./_typed-array")("Float32",4,function(r){return function(t,n,e){return r(this,t,n,e)}});
},{"./_typed-array":"fd04"}],"mbTX":[function(require,module,exports) {
require("./_typed-array")("Float64",8,function(r){return function(t,n,e){return r(this,t,n,e)}});
},{"./_typed-array":"fd04"}],"e3Bp":[function(require,module,exports) {
"use strict";var e=require("./_descriptors"),r=require("./_object-keys"),t=require("./_object-gops"),o=require("./_object-pie"),i=require("./_to-object"),c=require("./_iobject"),n=Object.assign;module.exports=!n||require("./_fails")(function(){var e={},r={},t=Symbol(),o="abcdefghijklmnopqrst";return e[t]=7,o.split("").forEach(function(e){r[e]=e}),7!=n({},e)[t]||Object.keys(n({},r)).join("")!=o})?function(n,u){for(var s=i(n),a=arguments.length,f=1,b=t.f,j=o.f;a>f;)for(var l,q=c(arguments[f++]),_=b?r(q).concat(b(q)):r(q),p=_.length,g=0;p>g;)l=_[g++],e&&!j.call(q,l)||(s[l]=q[l]);return s}:n;
},{"./_descriptors":"P9Ib","./_object-keys":"U9a7","./_object-gops":"EWMd","./_object-pie":"vjRp","./_to-object":"rfVX","./_iobject":"nGau","./_fails":"BXiR"}],"BNoi":[function(require,module,exports) {
"use strict";var e=require("./_redefine-all"),t=require("./_meta").getWeak,r=require("./_an-object"),i=require("./_is-object"),n=require("./_an-instance"),u=require("./_for-of"),o=require("./_array-methods"),s=require("./_has"),a=require("./_validate-collection"),c=o(5),f=o(6),_=0,h=function(e){return e._l||(e._l=new l)},l=function(){this.a=[]},d=function(e,t){return c(e.a,function(e){return e[0]===t})};l.prototype={get:function(e){var t=d(this,e);if(t)return t[1]},has:function(e){return!!d(this,e)},set:function(e,t){var r=d(this,e);r?r[1]=t:this.a.push([e,t])},delete:function(e){var t=f(this.a,function(t){return t[0]===e});return~t&&this.a.splice(t,1),!!~t}},module.exports={getConstructor:function(r,o,c,f){var l=r(function(e,t){n(e,l,o,"_i"),e._t=o,e._i=_++,e._l=void 0,null!=t&&u(t,c,e[f],e)});return e(l.prototype,{delete:function(e){if(!i(e))return!1;var r=t(e);return!0===r?h(a(this,o)).delete(e):r&&s(r,this._i)&&delete r[this._i]},has:function(e){if(!i(e))return!1;var r=t(e);return!0===r?h(a(this,o)).has(e):r&&s(r,this._i)}}),l},def:function(e,i,n){var u=t(r(i),!0);return!0===u?h(e).set(i,n):u[e._i]=n,e},ufstore:h};
},{"./_redefine-all":"J0Tl","./_meta":"AoVy","./_an-object":"eT53","./_is-object":"M7z6","./_an-instance":"yJTF","./_for-of":"Abke","./_array-methods":"AuPh","./_has":"uHgd","./_validate-collection":"FW4z"}],"D6DP":[function(require,module,exports) {

"use strict";var e,t=require("./_global"),r=require("./_array-methods")(0),i=require("./_redefine"),n=require("./_meta"),o=require("./_object-assign"),u=require("./_collection-weak"),c=require("./_is-object"),s=require("./_validate-collection"),a=require("./_validate-collection"),l=!t.ActiveXObject&&"ActiveXObject"in t,f="WeakMap",_=n.getWeak,h=Object.isExtensible,q=u.ufstore,v=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},d={get:function(e){if(c(e)){var t=_(e);return!0===t?q(s(this,f)).get(e):t?t[this._i]:void 0}},set:function(e,t){return u.def(s(this,f),e,t)}},g=module.exports=require("./_collection")(f,v,d,u,!0,!0);a&&l&&(o((e=u.getConstructor(v,f)).prototype,d),n.NEED=!0,r(["delete","has","get","set"],function(t){var r=g.prototype,n=r[t];i(r,t,function(r,i){if(c(r)&&!h(r)){this._f||(this._f=new e);var o=this._f[t](r,i);return"set"==t?this:o}return n.call(this,r,i)})}));
},{"./_global":"qf4T","./_array-methods":"AuPh","./_redefine":"PHot","./_meta":"AoVy","./_object-assign":"e3Bp","./_collection-weak":"BNoi","./_is-object":"M7z6","./_validate-collection":"FW4z","./_collection":"hWYB"}],"bRUR":[function(require,module,exports) {
"use strict";var e=require("./_collection-weak"),t=require("./_validate-collection"),i="WeakSet";require("./_collection")(i,function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(r){return e.def(t(this,i),r,!0)}},e,!1,!0);
},{"./_collection-weak":"BNoi","./_validate-collection":"FW4z","./_collection":"hWYB"}],"OTsy":[function(require,module,exports) {

var e=require("./_global"),t=require("./_export"),n=require("./_user-agent"),r=[].slice,u=/MSIE .\./.test(n),i=function(e){return function(t,n){var u=arguments.length>2,i=!!u&&r.call(arguments,2);return e(u?function(){("function"==typeof t?t:Function(t)).apply(this,i)}:t,n)}};t(t.G+t.B+t.F*u,{setTimeout:i(e.setTimeout),setInterval:i(e.setInterval)});
},{"./_global":"qf4T","./_export":"izCb","./_user-agent":"O5uh"}],"hZLH":[function(require,module,exports) {
var e=require("./_export"),r=require("./_task");e(e.G+e.B,{setImmediate:r.set,clearImmediate:r.clear});
},{"./_export":"izCb","./_task":"KY9y"}],"v6Aj":[function(require,module,exports) {

for(var e=require("./es6.array.iterator"),t=require("./_object-keys"),i=require("./_redefine"),r=require("./_global"),s=require("./_hide"),L=require("./_iterators"),a=require("./_wks"),o=a("iterator"),l=a("toStringTag"),S=L.Array,n={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},u=t(n),T=0;T<u.length;T++){var c,g=u[T],M=n[g],y=r[g],f=y&&y.prototype;if(f&&(f[o]||s(f,o,S),f[l]||s(f,l,g),L[g]=S,M))for(c in e)f[c]||i(f,c,e[c],!0)}
},{"./es6.array.iterator":"wVEN","./_object-keys":"U9a7","./_redefine":"PHot","./_global":"qf4T","./_hide":"NXbe","./_iterators":"JO4d","./_wks":"AIP1"}],"QVnC":[function(require,module,exports) {
var define;
var t,r=function(t){"use strict";var r,e=Object.prototype,n=e.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{u({},"")}catch(P){u=function(t,r,e){return t[r]=e}}function h(t,r,e,n){var o=r&&r.prototype instanceof d?r:d,i=Object.create(o.prototype),a=new G(n||[]);return i._invoke=function(t,r,e){var n=l;return function(o,i){if(n===p)throw new Error("Generator is already running");if(n===y){if("throw"===o)throw i;return F()}for(e.method=o,e.arg=i;;){var a=e.delegate;if(a){var c=j(a,e);if(c){if(c===v)continue;return c}}if("next"===e.method)e.sent=e._sent=e.arg;else if("throw"===e.method){if(n===l)throw n=y,e.arg;e.dispatchException(e.arg)}else"return"===e.method&&e.abrupt("return",e.arg);n=p;var u=f(t,r,e);if("normal"===u.type){if(n=e.done?y:s,u.arg===v)continue;return{value:u.arg,done:e.done}}"throw"===u.type&&(n=y,e.method="throw",e.arg=u.arg)}}}(t,e,a),i}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(P){return{type:"throw",arg:P}}}t.wrap=h;var l="suspendedStart",s="suspendedYield",p="executing",y="completed",v={};function d(){}function g(){}function m(){}var w={};w[i]=function(){return this};var L=Object.getPrototypeOf,x=L&&L(L(N([])));x&&x!==e&&n.call(x,i)&&(w=x);var b=m.prototype=d.prototype=Object.create(w);function E(t){["next","throw","return"].forEach(function(r){u(t,r,function(t){return this._invoke(r,t)})})}function _(t,r){var e;this._invoke=function(o,i){function a(){return new r(function(e,a){!function e(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var h=u.arg,l=h.value;return l&&"object"==typeof l&&n.call(l,"__await")?r.resolve(l.__await).then(function(t){e("next",t,a,c)},function(t){e("throw",t,a,c)}):r.resolve(l).then(function(t){h.value=t,a(h)},function(t){return e("throw",t,a,c)})}c(u.arg)}(o,i,e,a)})}return e=e?e.then(a,a):a()}}function j(t,e){var n=t.iterator[e.method];if(n===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=r,j(t,e),"throw"===e.method))return v;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}var o=f(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,v;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,v):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function O(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function G(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function N(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=r,e.done=!0,e};return a.next=a}}return{next:F}}function F(){return{value:r,done:!0}}return g.prototype=b.constructor=m,m.constructor=g,g.displayName=u(m,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===g||"GeneratorFunction"===(r.displayName||r.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,c,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},E(_.prototype),_.prototype[a]=function(){return this},t.AsyncIterator=_,t.async=function(r,e,n,o,i){void 0===i&&(i=Promise);var a=new _(h(r,e,n,o),i);return t.isGeneratorFunction(e)?a:a.next().then(function(t){return t.done?t.value:a.next()})},E(b),u(b,c,"Generator"),b[i]=function(){return this},b.toString=function(){return"[object Generator]"},t.keys=function(t){var r=[];for(var e in t)r.push(e);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,G.prototype={constructor:G,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(k),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return c.type="throw",c.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),h=n.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!h)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:N(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),v}},t}("object"==typeof module?module.exports:{});try{regeneratorRuntime=r}catch(e){Function("r","regeneratorRuntime = r")(r)}
},{}],"eqBM":[function(require,module,exports) {
"use strict";function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function t(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}function n(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function r(e){return(r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function i(e,t){return(i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function o(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}function a(e,t,n){return(a=o()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&i(o,n.prototype),o}).apply(null,arguments)}function u(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function s(e){var t="function"==typeof Map?new Map:void 0;return(s=function(e){if(null===e||!u(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return a(e,arguments,r(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),i(n,e)})(e)}function c(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}function l(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function d(e){var t=0;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=l(e)))return function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(t=e[Symbol.iterator]()).next.bind(t)}Object.defineProperty(exports,"__esModule",{value:!0});var h=function(e){function t(){return e.apply(this,arguments)||this}return n(t,e),t}(s(Error)),m=function(e){function t(t){return e.call(this,"Invalid DateTime: "+t.toMessage())||this}return n(t,e),t}(h),y=function(e){function t(t){return e.call(this,"Invalid Interval: "+t.toMessage())||this}return n(t,e),t}(h),v=function(e){function t(t){return e.call(this,"Invalid Duration: "+t.toMessage())||this}return n(t,e),t}(h),g=function(e){function t(){return e.apply(this,arguments)||this}return n(t,e),t}(h),p=function(e){function t(t){return e.call(this,"Invalid unit "+t)||this}return n(t,e),t}(h),w=function(e){function t(){return e.apply(this,arguments)||this}return n(t,e),t}(h),k=function(e){function t(){return e.call(this,"Zone is an abstract class")||this}return n(t,e),t}(h),b="numeric",S="short",O="long",T={year:b,month:b,day:b},M={year:b,month:S,day:b},N={year:b,month:S,day:b,weekday:S},E={year:b,month:O,day:b},D={year:b,month:O,day:b,weekday:O},I={hour:b,minute:b},V={hour:b,minute:b,second:b},L={hour:b,minute:b,second:b,timeZoneName:S},x={hour:b,minute:b,second:b,timeZoneName:O},F={hour:b,minute:b,hour12:!1},C={hour:b,minute:b,second:b,hour12:!1},Z={hour:b,minute:b,second:b,hour12:!1,timeZoneName:S},j={hour:b,minute:b,second:b,hour12:!1,timeZoneName:O},A={year:b,month:b,day:b,hour:b,minute:b},z={year:b,month:b,day:b,hour:b,minute:b,second:b},_={year:b,month:S,day:b,hour:b,minute:b},q={year:b,month:S,day:b,hour:b,minute:b,second:b},H={year:b,month:S,day:b,weekday:S,hour:b,minute:b},U={year:b,month:O,day:b,hour:b,minute:b,timeZoneName:S},R={year:b,month:O,day:b,hour:b,minute:b,second:b,timeZoneName:S},P={year:b,month:O,day:b,weekday:O,hour:b,minute:b,timeZoneName:O},W={year:b,month:O,day:b,weekday:O,hour:b,minute:b,second:b,timeZoneName:O};function J(e){return void 0===e}function Y(e){return"number"==typeof e}function G(e){return"number"==typeof e&&e%1==0}function $(e){return"string"==typeof e}function B(e){return"[object Date]"===Object.prototype.toString.call(e)}function Q(){try{return"undefined"!=typeof Intl&&Intl.DateTimeFormat}catch(e){return!1}}function K(){return!J(Intl.DateTimeFormat.prototype.formatToParts)}function X(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(e){return!1}}function ee(e){return Array.isArray(e)?e:[e]}function te(e,t,n){if(0!==e.length)return e.reduce(function(e,r){var i=[t(r),r];return e&&n(e[0],i[0])===e[0]?e:i},null)[1]}function ne(e,t){return t.reduce(function(t,n){return t[n]=e[n],t},{})}function re(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ie(e,t,n){return G(e)&&e>=t&&e<=n}function oe(e,t){return e-t*Math.floor(e/t)}function ae(e,t){void 0===t&&(t=2);var n=e<0?"-":"",r=n?-1*e:e;return""+n+(r.toString().length<t?("0".repeat(t)+r).slice(-t):r.toString())}function ue(e){return J(e)||null===e||""===e?void 0:parseInt(e,10)}function se(e){if(!J(e)&&null!==e&&""!==e){var t=1e3*parseFloat("0."+e);return Math.floor(t)}}function ce(e,t,n){void 0===n&&(n=!1);var r=Math.pow(10,t);return(n?Math.trunc:Math.round)(e*r)/r}function le(e){return e%4==0&&(e%100!=0||e%400==0)}function fe(e){return le(e)?366:365}function de(e,t){var n=oe(t-1,12)+1;return 2===n?le(e+(t-n)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function he(e){var t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t)).setUTCFullYear(t.getUTCFullYear()-1900),+t}function me(e){var t=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7,n=e-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return 4===t||3===r?53:52}function ye(e){return e>99?e:e>60?1900+e:2e3+e}function ve(e,t,n,r){void 0===r&&(r=null);var i=new Date(e),o={hour12:!1,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(o.timeZone=r);var a=Object.assign({timeZoneName:t},o),u=Q();if(u&&K()){var s=new Intl.DateTimeFormat(n,a).formatToParts(i).find(function(e){return"timezonename"===e.type.toLowerCase()});return s?s.value:null}if(u){var c=new Intl.DateTimeFormat(n,o).format(i);return new Intl.DateTimeFormat(n,a).format(i).substring(c.length).replace(/^[, \u200e]+/,"")}return null}function ge(e,t){var n=parseInt(e,10);Number.isNaN(n)&&(n=0);var r=parseInt(t,10)||0;return 60*n+(n<0||Object.is(n,-0)?-r:r)}function pe(e){var t=Number(e);if("boolean"==typeof e||""===e||Number.isNaN(t))throw new w("Invalid unit value "+e);return t}function we(e,t,n){var r={};for(var i in e)if(re(e,i)){if(n.indexOf(i)>=0)continue;var o=e[i];if(null==o)continue;r[t(i)]=pe(o)}return r}function ke(e,t){var n=Math.trunc(Math.abs(e/60)),r=Math.trunc(Math.abs(e%60)),i=e>=0?"+":"-";switch(t){case"short":return""+i+ae(n,2)+":"+ae(r,2);case"narrow":return""+i+n+(r>0?":"+r:"");case"techie":return""+i+ae(n,2)+ae(r,2);default:throw new RangeError("Value format "+t+" is out of range for property format")}}function be(e){return ne(e,["hour","minute","second","millisecond"])}var Se=/[A-Za-z_+-]{1,256}(:?\/[A-Za-z_+-]{1,256}(\/[A-Za-z_+-]{1,256})?)?/;function Oe(e){return JSON.stringify(e,Object.keys(e).sort())}var Te=["January","February","March","April","May","June","July","August","September","October","November","December"],Me=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Ne=["J","F","M","A","M","J","J","A","S","O","N","D"];function Ee(e){switch(e){case"narrow":return Ne;case"short":return Me;case"long":return Te;case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var De=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Ie=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Ve=["M","T","W","T","F","S","S"];function Le(e){switch(e){case"narrow":return Ve;case"short":return Ie;case"long":return De;case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var xe=["AM","PM"],Fe=["Before Christ","Anno Domini"],Ce=["BC","AD"],Ze=["B","A"];function je(e){switch(e){case"narrow":return Ze;case"short":return Ce;case"long":return Fe;default:return null}}function Ae(e){return xe[e.hour<12?0:1]}function ze(e,t){return Le(t)[e.weekday-1]}function _e(e,t){return Ee(t)[e.month-1]}function qe(e,t){return je(t)[e.year<0?0:1]}function He(e,t,n,r){void 0===n&&(n="always"),void 0===r&&(r=!1);var i={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},o=-1===["hours","minutes","seconds"].indexOf(e);if("auto"===n&&o){var a="days"===e;switch(t){case 1:return a?"tomorrow":"next "+i[e][0];case-1:return a?"yesterday":"last "+i[e][0];case 0:return a?"today":"this "+i[e][0]}}var u=Object.is(t,-0)||t<0,s=Math.abs(t),c=1===s,l=i[e],f=r?c?l[1]:l[2]||l[1]:c?i[e][0]:e;return u?s+" "+f+" ago":"in "+s+" "+f}function Ue(e){switch(Oe(ne(e,["weekday","era","year","month","day","hour","minute","second","timeZoneName","hour12"]))){case Oe(T):return"M/d/yyyy";case Oe(M):return"LLL d, yyyy";case Oe(N):return"EEE, LLL d, yyyy";case Oe(E):return"LLLL d, yyyy";case Oe(D):return"EEEE, LLLL d, yyyy";case Oe(I):return"h:mm a";case Oe(V):return"h:mm:ss a";case Oe(L):case Oe(x):return"h:mm a";case Oe(F):return"HH:mm";case Oe(C):return"HH:mm:ss";case Oe(Z):case Oe(j):return"HH:mm";case Oe(A):return"M/d/yyyy, h:mm a";case Oe(_):return"LLL d, yyyy, h:mm a";case Oe(U):return"LLLL d, yyyy, h:mm a";case Oe(P):return"EEEE, LLLL d, yyyy, h:mm a";case Oe(z):return"M/d/yyyy, h:mm:ss a";case Oe(q):return"LLL d, yyyy, h:mm:ss a";case Oe(H):return"EEE, d LLL yyyy, h:mm a";case Oe(R):return"LLLL d, yyyy, h:mm:ss a";case Oe(W):return"EEEE, LLLL d, yyyy, h:mm:ss a";default:return"EEEE, LLLL d, yyyy, h:mm a"}}function Re(e,t){for(var n,r="",i=d(e);!(n=i()).done;){var o=n.value;o.literal?r+=o.val:r+=t(o.val)}return r}var Pe={D:T,DD:M,DDD:E,DDDD:D,t:I,tt:V,ttt:L,tttt:x,T:F,TT:C,TTT:Z,TTTT:j,f:A,ff:_,fff:U,ffff:P,F:z,FF:q,FFF:R,FFFF:W},We=function(){function e(e,t){this.opts=t,this.loc=e,this.systemLoc=null}e.create=function(t,n){return void 0===n&&(n={}),new e(t,n)},e.parseFormat=function(e){for(var t=null,n="",r=!1,i=[],o=0;o<e.length;o++){var a=e.charAt(o);"'"===a?(n.length>0&&i.push({literal:r,val:n}),t=null,n="",r=!r):r?n+=a:a===t?n+=a:(n.length>0&&i.push({literal:!1,val:n}),n=a,t=a)}return n.length>0&&i.push({literal:r,val:n}),i},e.macroTokenToFormatOpts=function(e){return Pe[e]};var t=e.prototype;return t.formatWithSystemDefault=function(e,t){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,Object.assign({},this.opts,t)).format()},t.formatDateTime=function(e,t){return void 0===t&&(t={}),this.loc.dtFormatter(e,Object.assign({},this.opts,t)).format()},t.formatDateTimeParts=function(e,t){return void 0===t&&(t={}),this.loc.dtFormatter(e,Object.assign({},this.opts,t)).formatToParts()},t.resolvedOptions=function(e,t){return void 0===t&&(t={}),this.loc.dtFormatter(e,Object.assign({},this.opts,t)).resolvedOptions()},t.num=function(e,t){if(void 0===t&&(t=0),this.opts.forceSimple)return ae(e,t);var n=Object.assign({},this.opts);return t>0&&(n.padTo=t),this.loc.numberFormatter(n).format(e)},t.formatDateTimeFromString=function(t,n){var r=this,i="en"===this.loc.listingMode(),o=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar&&K(),a=function(e,n){return r.loc.extract(t,e,n)},u=function(e){return t.isOffsetFixed&&0===t.offset&&e.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,e.format):""},s=function(e,n){return i?_e(t,e):a(n?{month:e}:{month:e,day:"numeric"},"month")},c=function(e,n){return i?ze(t,e):a(n?{weekday:e}:{weekday:e,month:"long",day:"numeric"},"weekday")},l=function(e){return i?qe(t,e):a({era:e},"era")};return Re(e.parseFormat(n),function(n){switch(n){case"S":return r.num(t.millisecond);case"u":case"SSS":return r.num(t.millisecond,3);case"s":return r.num(t.second);case"ss":return r.num(t.second,2);case"m":return r.num(t.minute);case"mm":return r.num(t.minute,2);case"h":return r.num(t.hour%12==0?12:t.hour%12);case"hh":return r.num(t.hour%12==0?12:t.hour%12,2);case"H":return r.num(t.hour);case"HH":return r.num(t.hour,2);case"Z":return u({format:"narrow",allowZ:r.opts.allowZ});case"ZZ":return u({format:"short",allowZ:r.opts.allowZ});case"ZZZ":return u({format:"techie",allowZ:r.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:r.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:r.loc.locale});case"z":return t.zoneName;case"a":return i?Ae(t):a({hour:"numeric",hour12:!0},"dayperiod");case"d":return o?a({day:"numeric"},"day"):r.num(t.day);case"dd":return o?a({day:"2-digit"},"day"):r.num(t.day,2);case"c":return r.num(t.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return r.num(t.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return o?a({month:"numeric",day:"numeric"},"month"):r.num(t.month);case"LL":return o?a({month:"2-digit",day:"numeric"},"month"):r.num(t.month,2);case"LLL":return s("short",!0);case"LLLL":return s("long",!0);case"LLLLL":return s("narrow",!0);case"M":return o?a({month:"numeric"},"month"):r.num(t.month);case"MM":return o?a({month:"2-digit"},"month"):r.num(t.month,2);case"MMM":return s("short",!1);case"MMMM":return s("long",!1);case"MMMMM":return s("narrow",!1);case"y":return o?a({year:"numeric"},"year"):r.num(t.year);case"yy":return o?a({year:"2-digit"},"year"):r.num(t.year.toString().slice(-2),2);case"yyyy":return o?a({year:"numeric"},"year"):r.num(t.year,4);case"yyyyyy":return o?a({year:"numeric"},"year"):r.num(t.year,6);case"G":return l("short");case"GG":return l("long");case"GGGGG":return l("narrow");case"kk":return r.num(t.weekYear.toString().slice(-2),2);case"kkkk":return r.num(t.weekYear,4);case"W":return r.num(t.weekNumber);case"WW":return r.num(t.weekNumber,2);case"o":return r.num(t.ordinal);case"ooo":return r.num(t.ordinal,3);case"q":return r.num(t.quarter);case"qq":return r.num(t.quarter,2);case"X":return r.num(Math.floor(t.ts/1e3));case"x":return r.num(t.ts);default:return function(n){var i=e.macroTokenToFormatOpts(n);return i?r.formatWithSystemDefault(t,i):n}(n)}})},t.formatDurationFromString=function(t,n){var r,i=this,o=function(e){switch(e[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"M":return"month";case"y":return"year";default:return null}},a=e.parseFormat(n),u=a.reduce(function(e,t){var n=t.literal,r=t.val;return n?e:e.concat(r)},[]),s=t.shiftTo.apply(t,u.map(o).filter(function(e){return e}));return Re(a,(r=s,function(e){var t=o(e);return t?i.num(r.get(t),e.length):e}))},e}(),Je=function(){function e(e,t){this.reason=e,this.explanation=t}return e.prototype.toMessage=function(){return this.explanation?this.reason+": "+this.explanation:this.reason},e}(),Ye=function(){function e(){}var n=e.prototype;return n.offsetName=function(e,t){throw new k},n.formatOffset=function(e,t){throw new k},n.offset=function(e){throw new k},n.equals=function(e){throw new k},t(e,[{key:"type",get:function(){throw new k}},{key:"name",get:function(){throw new k}},{key:"universal",get:function(){throw new k}},{key:"isValid",get:function(){throw new k}}]),e}(),Ge=null,$e=function(e){function r(){return e.apply(this,arguments)||this}n(r,e);var i=r.prototype;return i.offsetName=function(e,t){return ve(e,t.format,t.locale)},i.formatOffset=function(e,t){return ke(this.offset(e),t)},i.offset=function(e){return-new Date(e).getTimezoneOffset()},i.equals=function(e){return"local"===e.type},t(r,[{key:"type",get:function(){return"local"}},{key:"name",get:function(){return Q()?(new Intl.DateTimeFormat).resolvedOptions().timeZone:"local"}},{key:"universal",get:function(){return!1}},{key:"isValid",get:function(){return!0}}],[{key:"instance",get:function(){return null===Ge&&(Ge=new r),Ge}}]),r}(Ye),Be=RegExp("^"+Se.source+"$"),Qe={};function Ke(e){return Qe[e]||(Qe[e]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})),Qe[e]}var Xe={year:0,month:1,day:2,hour:3,minute:4,second:5};function et(e,t){var n=e.format(t).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(n),i=r[1],o=r[2];return[r[3],i,o,r[4],r[5],r[6]]}function tt(e,t){for(var n=e.formatToParts(t),r=[],i=0;i<n.length;i++){var o=n[i],a=o.type,u=o.value,s=Xe[a];J(s)||(r[s]=parseInt(u,10))}return r}var nt={},rt=function(e){function r(t){var n;return(n=e.call(this)||this).zoneName=t,n.valid=r.isValidZone(t),n}n(r,e),r.create=function(e){return nt[e]||(nt[e]=new r(e)),nt[e]},r.resetCache=function(){nt={},Qe={}},r.isValidSpecifier=function(e){return!(!e||!e.match(Be))},r.isValidZone=function(e){try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(t){return!1}},r.parseGMTOffset=function(e){if(e){var t=e.match(/^Etc\/GMT([+-]\d{1,2})$/i);if(t)return-60*parseInt(t[1])}return null};var i=r.prototype;return i.offsetName=function(e,t){return ve(e,t.format,t.locale,this.name)},i.formatOffset=function(e,t){return ke(this.offset(e),t)},i.offset=function(e){var t=new Date(e),n=Ke(this.name),r=n.formatToParts?tt(n,t):et(n,t),i=r[0],o=r[1],a=r[2],u=r[3],s=+t,c=s%1e3;return(he({year:i,month:o,day:a,hour:24===u?0:u,minute:r[4],second:r[5],millisecond:0})-(s-=c>=0?c:1e3+c))/6e4},i.equals=function(e){return"iana"===e.type&&e.name===this.name},t(r,[{key:"type",get:function(){return"iana"}},{key:"name",get:function(){return this.zoneName}},{key:"universal",get:function(){return!1}},{key:"isValid",get:function(){return this.valid}}]),r}(Ye),it=null,ot=function(e){function r(t){var n;return(n=e.call(this)||this).fixed=t,n}n(r,e),r.instance=function(e){return 0===e?r.utcInstance:new r(e)},r.parseSpecifier=function(e){if(e){var t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new r(ge(t[1],t[2]))}return null},t(r,null,[{key:"utcInstance",get:function(){return null===it&&(it=new r(0)),it}}]);var i=r.prototype;return i.offsetName=function(){return this.name},i.formatOffset=function(e,t){return ke(this.fixed,t)},i.offset=function(){return this.fixed},i.equals=function(e){return"fixed"===e.type&&e.fixed===this.fixed},t(r,[{key:"type",get:function(){return"fixed"}},{key:"name",get:function(){return 0===this.fixed?"UTC":"UTC"+ke(this.fixed,"narrow")}},{key:"universal",get:function(){return!0}},{key:"isValid",get:function(){return!0}}]),r}(Ye),at=function(e){function r(t){var n;return(n=e.call(this)||this).zoneName=t,n}n(r,e);var i=r.prototype;return i.offsetName=function(){return null},i.formatOffset=function(){return""},i.offset=function(){return NaN},i.equals=function(){return!1},t(r,[{key:"type",get:function(){return"invalid"}},{key:"name",get:function(){return this.zoneName}},{key:"universal",get:function(){return!1}},{key:"isValid",get:function(){return!1}}]),r}(Ye);function ut(e,t){var n;if(J(e)||null===e)return t;if(e instanceof Ye)return e;if($(e)){var r=e.toLowerCase();return"local"===r?t:"utc"===r||"gmt"===r?ot.utcInstance:null!=(n=rt.parseGMTOffset(e))?ot.instance(n):rt.isValidSpecifier(r)?rt.create(e):ot.parseSpecifier(r)||new at(e)}return Y(e)?ot.instance(e):"object"==typeof e&&e.offset&&"number"==typeof e.offset?e:new at(e)}var st=function(){return Date.now()},ct=null,lt=null,ft=null,dt=null,ht=!1,mt=function(){function e(){}return e.resetCaches=function(){xt.resetCache(),rt.resetCache()},t(e,null,[{key:"now",get:function(){return st},set:function(e){st=e}},{key:"defaultZoneName",get:function(){return e.defaultZone.name},set:function(e){ct=e?ut(e):null}},{key:"defaultZone",get:function(){return ct||$e.instance}},{key:"defaultLocale",get:function(){return lt},set:function(e){lt=e}},{key:"defaultNumberingSystem",get:function(){return ft},set:function(e){ft=e}},{key:"defaultOutputCalendar",get:function(){return dt},set:function(e){dt=e}},{key:"throwOnInvalid",get:function(){return ht},set:function(e){ht=e}}]),e}(),yt={};function vt(e,t){void 0===t&&(t={});var n=JSON.stringify([e,t]),r=yt[n];return r||(r=new Intl.DateTimeFormat(e,t),yt[n]=r),r}var gt={};function pt(e,t){void 0===t&&(t={});var n=JSON.stringify([e,t]),r=gt[n];return r||(r=new Intl.NumberFormat(e,t),gt[n]=r),r}var wt={};function kt(e,t){void 0===t&&(t={});var n=t,r=(n.base,c(n,["base"])),i=JSON.stringify([e,r]),o=wt[i];return o||(o=new Intl.RelativeTimeFormat(e,t),wt[i]=o),o}var bt=null;function St(){if(bt)return bt;if(Q()){var e=(new Intl.DateTimeFormat).resolvedOptions().locale;return bt=e&&"und"!==e?e:"en-US"}return bt="en-US"}function Ot(e){var t=e.indexOf("-u-");if(-1===t)return[e];var n,r=e.substring(0,t);try{n=vt(e).resolvedOptions()}catch(o){n=vt(r).resolvedOptions()}var i=n;return[r,i.numberingSystem,i.calendar]}function Tt(e,t,n){return Q()?n||t?(e+="-u",n&&(e+="-ca-"+n),t&&(e+="-nu-"+t),e):e:[]}function Mt(e){for(var t=[],n=1;n<=12;n++){var r=ci.utc(2016,n,1);t.push(e(r))}return t}function Nt(e){for(var t=[],n=1;n<=7;n++){var r=ci.utc(2016,11,13+n);t.push(e(r))}return t}function Et(e,t,n,r,i){var o=e.listingMode(n);return"error"===o?null:"en"===o?r(t):i(t)}function Dt(e){return(!e.numberingSystem||"latn"===e.numberingSystem)&&("latn"===e.numberingSystem||!e.locale||e.locale.startsWith("en")||Q()&&"latn"===new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem)}var It=function(){function e(e,t,n){if(this.padTo=n.padTo||0,this.floor=n.floor||!1,!t&&Q()){var r={useGrouping:!1};n.padTo>0&&(r.minimumIntegerDigits=n.padTo),this.inf=pt(e,r)}}return e.prototype.format=function(e){if(this.inf){var t=this.floor?Math.floor(e):e;return this.inf.format(t)}return ae(this.floor?Math.floor(e):ce(e,3),this.padTo)},e}(),Vt=function(){function e(e,t,n){var r;if(this.opts=n,this.hasIntl=Q(),e.zone.universal&&this.hasIntl){var i=e.offset/60*-1;i>=-14&&i<=12&&i%1==0?(r=i>=0?"Etc/GMT+"+i:"Etc/GMT"+i,this.dt=e):(r="UTC",n.timeZoneName?this.dt=e:this.dt=0===e.offset?e:ci.fromMillis(e.ts+60*e.offset*1e3))}else"local"===e.zone.type?this.dt=e:(this.dt=e,r=e.zone.name);if(this.hasIntl){var o=Object.assign({},this.opts);r&&(o.timeZone=r),this.dtf=vt(t,o)}}var t=e.prototype;return t.format=function(){if(this.hasIntl)return this.dtf.format(this.dt.toJSDate());var e=Ue(this.opts),t=xt.create("en-US");return We.create(t).formatDateTimeFromString(this.dt,e)},t.formatToParts=function(){return this.hasIntl&&K()?this.dtf.formatToParts(this.dt.toJSDate()):[]},t.resolvedOptions=function(){return this.hasIntl?this.dtf.resolvedOptions():{locale:"en-US",numberingSystem:"latn",outputCalendar:"gregory"}},e}(),Lt=function(){function e(e,t,n){this.opts=Object.assign({style:"long"},n),!t&&X()&&(this.rtf=kt(e,n))}var t=e.prototype;return t.format=function(e,t){return this.rtf?this.rtf.format(e,t):He(t,e,this.opts.numeric,"long"!==this.opts.style)},t.formatToParts=function(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]},e}(),xt=function(){function e(e,t,n,r){var i=Ot(e),o=i[0],a=i[1],u=i[2];this.locale=o,this.numberingSystem=t||a||null,this.outputCalendar=n||u||null,this.intl=Tt(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=r,this.fastNumbersCached=null}e.fromOpts=function(t){return e.create(t.locale,t.numberingSystem,t.outputCalendar,t.defaultToEN)},e.create=function(t,n,r,i){void 0===i&&(i=!1);var o=t||mt.defaultLocale;return new e(o||(i?"en-US":St()),n||mt.defaultNumberingSystem,r||mt.defaultOutputCalendar,o)},e.resetCache=function(){bt=null,yt={},gt={},wt={}},e.fromObject=function(t){var n=void 0===t?{}:t,r=n.locale,i=n.numberingSystem,o=n.outputCalendar;return e.create(r,i,o)};var n=e.prototype;return n.listingMode=function(e){void 0===e&&(e=!0);var t=Q()&&K(),n=this.isEnglish(),r=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return t||n&&r||e?!t||n&&r?"en":"intl":"error"},n.clone=function(t){return t&&0!==Object.getOwnPropertyNames(t).length?e.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,t.defaultToEN||!1):this},n.redefaultToEN=function(e){return void 0===e&&(e={}),this.clone(Object.assign({},e,{defaultToEN:!0}))},n.redefaultToSystem=function(e){return void 0===e&&(e={}),this.clone(Object.assign({},e,{defaultToEN:!1}))},n.months=function(e,t,n){var r=this;return void 0===t&&(t=!1),void 0===n&&(n=!0),Et(this,e,n,Ee,function(){var n=t?{month:e,day:"numeric"}:{month:e},i=t?"format":"standalone";return r.monthsCache[i][e]||(r.monthsCache[i][e]=Mt(function(e){return r.extract(e,n,"month")})),r.monthsCache[i][e]})},n.weekdays=function(e,t,n){var r=this;return void 0===t&&(t=!1),void 0===n&&(n=!0),Et(this,e,n,Le,function(){var n=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},i=t?"format":"standalone";return r.weekdaysCache[i][e]||(r.weekdaysCache[i][e]=Nt(function(e){return r.extract(e,n,"weekday")})),r.weekdaysCache[i][e]})},n.meridiems=function(e){var t=this;return void 0===e&&(e=!0),Et(this,void 0,e,function(){return xe},function(){if(!t.meridiemCache){var e={hour:"numeric",hour12:!0};t.meridiemCache=[ci.utc(2016,11,13,9),ci.utc(2016,11,13,19)].map(function(n){return t.extract(n,e,"dayperiod")})}return t.meridiemCache})},n.eras=function(e,t){var n=this;return void 0===t&&(t=!0),Et(this,e,t,je,function(){var t={era:e};return n.eraCache[e]||(n.eraCache[e]=[ci.utc(-40,1,1),ci.utc(2017,1,1)].map(function(e){return n.extract(e,t,"era")})),n.eraCache[e]})},n.extract=function(e,t,n){var r=this.dtFormatter(e,t).formatToParts().find(function(e){return e.type.toLowerCase()===n});return r?r.value:null},n.numberFormatter=function(e){return void 0===e&&(e={}),new It(this.intl,e.forceSimple||this.fastNumbers,e)},n.dtFormatter=function(e,t){return void 0===t&&(t={}),new Vt(e,this.intl,t)},n.relFormatter=function(e){return void 0===e&&(e={}),new Lt(this.intl,this.isEnglish(),e)},n.isEnglish=function(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||Q()&&new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")},n.equals=function(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar},t(e,[{key:"fastNumbers",get:function(){return null==this.fastNumbersCached&&(this.fastNumbersCached=Dt(this)),this.fastNumbersCached}}]),e}();function Ft(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce(function(e,t){return e+t.source},"");return RegExp("^"+r+"$")}function Ct(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduce(function(t,n){var r=t[0],i=t[1],o=t[2],a=n(e,o),u=a[0],s=a[1],c=a[2];return[Object.assign(r,u),i||s,c]},[{},null,1]).slice(0,2)}}function Zt(e){if(null==e)return[null,null];for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var i=0,o=n;i<o.length;i++){var a=o[i],u=a[0],s=a[1],c=u.exec(e);if(c)return s(c)}return[null,null]}function jt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n){var r,i={};for(r=0;r<t.length;r++)i[t[r]]=ue(e[n+r]);return[i,null,n+r]}}var At=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,zt=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,_t=RegExp(""+zt.source+At.source+"?"),qt=RegExp("(?:T"+_t.source+")?"),Ht=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,Ut=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Rt=/(\d{4})-?(\d{3})/,Pt=jt("weekYear","weekNumber","weekDay"),Wt=jt("year","ordinal"),Jt=/(\d{4})-(\d\d)-(\d\d)/,Yt=RegExp(zt.source+" ?(?:"+At.source+"|("+Se.source+"))?"),Gt=RegExp("(?: "+Yt.source+")?");function $t(e,t,n){var r=e[t];return J(r)?n:ue(r)}function Bt(e,t){return[{year:$t(e,t),month:$t(e,t+1,1),day:$t(e,t+2,1)},null,t+3]}function Qt(e,t){return[{hours:$t(e,t,0),minutes:$t(e,t+1,0),seconds:$t(e,t+2,0),milliseconds:se(e[t+3])},null,t+4]}function Kt(e,t){var n=!e[t]&&!e[t+1],r=ge(e[t+1],e[t+2]);return[{},n?null:ot.instance(r),t+3]}function Xt(e,t){return[{},e[t]?rt.create(e[t]):null,t+1]}var en=RegExp("^T?"+zt.source+"$"),tn=/^-?P(?:(?:(-?\d{1,9})Y)?(?:(-?\d{1,9})M)?(?:(-?\d{1,9})W)?(?:(-?\d{1,9})D)?(?:T(?:(-?\d{1,9})H)?(?:(-?\d{1,9})M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,9}))?S)?)?)$/;function nn(e){var t=e[0],n=e[1],r=e[2],i=e[3],o=e[4],a=e[5],u=e[6],s=e[7],c=e[8],l="-"===t[0],f=function(e){return e&&l?-e:e};return[{years:f(ue(n)),months:f(ue(r)),weeks:f(ue(i)),days:f(ue(o)),hours:f(ue(a)),minutes:f(ue(u)),seconds:f(ue(s)),milliseconds:f(se(c))}]}var rn={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function on(e,t,n,r,i,o,a){var u={year:2===t.length?ye(ue(t)):ue(t),month:Me.indexOf(n)+1,day:ue(r),hour:ue(i),minute:ue(o)};return a&&(u.second=ue(a)),e&&(u.weekday=e.length>3?De.indexOf(e)+1:Ie.indexOf(e)+1),u}var an=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function un(e){var t,n=e[1],r=e[2],i=e[3],o=e[4],a=e[5],u=e[6],s=e[7],c=e[8],l=e[9],f=e[10],d=e[11],h=on(n,o,i,r,a,u,s);return t=c?rn[c]:l?0:ge(f,d),[h,new ot(t)]}function sn(e){return e.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var cn=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,ln=/^(Monday|Tuesday|Wedsday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,fn=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function dn(e){var t=e[1],n=e[2],r=e[3];return[on(t,e[4],r,n,e[5],e[6],e[7]),ot.utcInstance]}function hn(e){var t=e[1],n=e[2],r=e[3],i=e[4],o=e[5],a=e[6];return[on(t,e[7],n,r,i,o,a),ot.utcInstance]}var mn=Ft(Ht,qt),yn=Ft(Ut,qt),vn=Ft(Rt,qt),gn=Ft(_t),pn=Ct(Bt,Qt,Kt),wn=Ct(Pt,Qt,Kt),kn=Ct(Wt,Qt),bn=Ct(Qt,Kt);function Sn(e){return Zt(e,[mn,pn],[yn,wn],[vn,kn],[gn,bn])}function On(e){return Zt(sn(e),[an,un])}function Tn(e){return Zt(e,[cn,dn],[ln,dn],[fn,hn])}function Mn(e){return Zt(e,[tn,nn])}var Nn=Ct(Qt);function En(e){return Zt(e,[en,Nn])}var Dn=Ft(Jt,Gt),In=Ft(Yt),Vn=Ct(Bt,Qt,Kt,Xt),Ln=Ct(Qt,Kt,Xt);function xn(e){return Zt(e,[Dn,Vn],[In,Ln])}var Fn="Invalid Duration",Cn={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},Zn=Object.assign({years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6}},Cn),jn=365.2425,An=30.436875,zn=Object.assign({years:{quarters:4,months:12,weeks:jn/7,days:jn,hours:24*jn,minutes:24*jn*60,seconds:24*jn*60*60,milliseconds:24*jn*60*60*1e3},quarters:{months:3,weeks:jn/28,days:jn/4,hours:24*jn/4,minutes:24*jn*60/4,seconds:24*jn*60*60/4,milliseconds:24*jn*60*60*1e3/4},months:{weeks:An/7,days:An,hours:24*An,minutes:24*An*60,seconds:24*An*60*60,milliseconds:24*An*60*60*1e3}},Cn),_n=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],qn=_n.slice(0).reverse();function Hn(e,t,n){void 0===n&&(n=!1);var r={values:n?t.values:Object.assign({},e.values,t.values||{}),loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy};return new Wn(r)}function Un(e){return e<0?Math.floor(e):Math.ceil(e)}function Rn(e,t,n,r,i){var o=e[i][n],a=t[n]/o,u=!(Math.sign(a)===Math.sign(r[i]))&&0!==r[i]&&Math.abs(a)<=1?Un(a):Math.trunc(a);r[i]+=u,t[n]-=u*o}function Pn(e,t){qn.reduce(function(n,r){return J(t[r])?n:(n&&Rn(e,t,n,t,r),r)},null)}var Wn=function(){function e(e){var t="longterm"===e.conversionAccuracy||!1;this.values=e.values,this.loc=e.loc||xt.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=t?zn:Zn,this.isLuxonDuration=!0}e.fromMillis=function(t,n){return e.fromObject(Object.assign({milliseconds:t},n))},e.fromObject=function(t){if(null==t||"object"!=typeof t)throw new w("Duration.fromObject: argument expected to be an object, got "+(null===t?"null":typeof t));return new e({values:we(t,e.normalizeUnit,["locale","numberingSystem","conversionAccuracy","zone"]),loc:xt.fromObject(t),conversionAccuracy:t.conversionAccuracy})},e.fromISO=function(t,n){var r=Mn(t)[0];if(r){var i=Object.assign(r,n);return e.fromObject(i)}return e.invalid("unparsable",'the input "'+t+"\" can't be parsed as ISO 8601")},e.fromISOTime=function(t,n){var r=En(t)[0];if(r){var i=Object.assign(r,n);return e.fromObject(i)}return e.invalid("unparsable",'the input "'+t+"\" can't be parsed as ISO 8601")},e.invalid=function(t,n){if(void 0===n&&(n=null),!t)throw new w("need to specify a reason the Duration is invalid");var r=t instanceof Je?t:new Je(t,n);if(mt.throwOnInvalid)throw new v(r);return new e({invalid:r})},e.normalizeUnit=function(e){var t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e?e.toLowerCase():e];if(!t)throw new p(e);return t},e.isDuration=function(e){return e&&e.isLuxonDuration||!1};var n=e.prototype;return n.toFormat=function(e,t){void 0===t&&(t={});var n=Object.assign({},t,{floor:!1!==t.round&&!1!==t.floor});return this.isValid?We.create(this.loc,n).formatDurationFromString(this,e):Fn},n.toObject=function(e){if(void 0===e&&(e={}),!this.isValid)return{};var t=Object.assign({},this.values);return e.includeConfig&&(t.conversionAccuracy=this.conversionAccuracy,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t},n.toISO=function(){if(!this.isValid)return null;var e="P";return 0!==this.years&&(e+=this.years+"Y"),0===this.months&&0===this.quarters||(e+=this.months+3*this.quarters+"M"),0!==this.weeks&&(e+=this.weeks+"W"),0!==this.days&&(e+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(e+="T"),0!==this.hours&&(e+=this.hours+"H"),0!==this.minutes&&(e+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(e+=ce(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===e&&(e+="T0S"),e},n.toISOTime=function(e){if(void 0===e&&(e={}),!this.isValid)return null;var t=this.toMillis();if(t<0||t>=864e5)return null;e=Object.assign({suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended"},e);var n=this.shiftTo("hours","minutes","seconds","milliseconds"),r="basic"===e.format?"hhmm":"hh:mm";e.suppressSeconds&&0===n.seconds&&0===n.milliseconds||(r+="basic"===e.format?"ss":":ss",e.suppressMilliseconds&&0===n.milliseconds||(r+=".SSS"));var i=n.toFormat(r);return e.includePrefix&&(i="T"+i),i},n.toJSON=function(){return this.toISO()},n.toString=function(){return this.toISO()},n.toMillis=function(){return this.as("milliseconds")},n.valueOf=function(){return this.toMillis()},n.plus=function(e){if(!this.isValid)return this;for(var t,n=Jn(e),r={},i=d(_n);!(t=i()).done;){var o=t.value;(re(n.values,o)||re(this.values,o))&&(r[o]=n.get(o)+this.get(o))}return Hn(this,{values:r},!0)},n.minus=function(e){if(!this.isValid)return this;var t=Jn(e);return this.plus(t.negate())},n.mapUnits=function(e){if(!this.isValid)return this;for(var t={},n=0,r=Object.keys(this.values);n<r.length;n++){var i=r[n];t[i]=pe(e(this.values[i],i))}return Hn(this,{values:t},!0)},n.get=function(t){return this[e.normalizeUnit(t)]},n.set=function(t){return this.isValid?Hn(this,{values:Object.assign(this.values,we(t,e.normalizeUnit,[]))}):this},n.reconfigure=function(e){var t=void 0===e?{}:e,n=t.locale,r=t.numberingSystem,i=t.conversionAccuracy,o={loc:this.loc.clone({locale:n,numberingSystem:r})};return i&&(o.conversionAccuracy=i),Hn(this,o)},n.as=function(e){return this.isValid?this.shiftTo(e).get(e):NaN},n.normalize=function(){if(!this.isValid)return this;var e=this.toObject();return Pn(this.matrix,e),Hn(this,{values:e},!0)},n.shiftTo=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];if(!this.isValid)return this;if(0===n.length)return this;n=n.map(function(t){return e.normalizeUnit(t)});for(var i,o,a={},u={},s=this.toObject(),c=d(_n);!(o=c()).done;){var l=o.value;if(n.indexOf(l)>=0){i=l;var f=0;for(var h in u)f+=this.matrix[h][l]*u[h],u[h]=0;Y(s[l])&&(f+=s[l]);var m=Math.trunc(f);for(var y in a[l]=m,u[l]=f-m,s)_n.indexOf(y)>_n.indexOf(l)&&Rn(this.matrix,s,y,a,l)}else Y(s[l])&&(u[l]=s[l])}for(var v in u)0!==u[v]&&(a[i]+=v===i?u[v]:u[v]/this.matrix[i][v]);return Hn(this,{values:a},!0).normalize()},n.negate=function(){if(!this.isValid)return this;for(var e={},t=0,n=Object.keys(this.values);t<n.length;t++){var r=n[t];e[r]=-this.values[r]}return Hn(this,{values:e},!0)},n.equals=function(e){if(!this.isValid||!e.isValid)return!1;if(!this.loc.equals(e.loc))return!1;for(var t,n=d(_n);!(t=n()).done;){var r=t.value;if(i=this.values[r],o=e.values[r],!(void 0===i||0===i?void 0===o||0===o:i===o))return!1}var i,o;return!0},t(e,[{key:"locale",get:function(){return this.isValid?this.loc.locale:null}},{key:"numberingSystem",get:function(){return this.isValid?this.loc.numberingSystem:null}},{key:"years",get:function(){return this.isValid?this.values.years||0:NaN}},{key:"quarters",get:function(){return this.isValid?this.values.quarters||0:NaN}},{key:"months",get:function(){return this.isValid?this.values.months||0:NaN}},{key:"weeks",get:function(){return this.isValid?this.values.weeks||0:NaN}},{key:"days",get:function(){return this.isValid?this.values.days||0:NaN}},{key:"hours",get:function(){return this.isValid?this.values.hours||0:NaN}},{key:"minutes",get:function(){return this.isValid?this.values.minutes||0:NaN}},{key:"seconds",get:function(){return this.isValid?this.values.seconds||0:NaN}},{key:"milliseconds",get:function(){return this.isValid?this.values.milliseconds||0:NaN}},{key:"isValid",get:function(){return null===this.invalid}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}}]),e}();function Jn(e){if(Y(e))return Wn.fromMillis(e);if(Wn.isDuration(e))return e;if("object"==typeof e)return Wn.fromObject(e);throw new w("Unknown duration argument "+e+" of type "+typeof e)}var Yn="Invalid Interval";function Gn(e,t){return e&&e.isValid?t&&t.isValid?t<e?$n.invalid("end before start","The end of an interval must be after its start, but you had start="+e.toISO()+" and end="+t.toISO()):null:$n.invalid("missing or invalid end"):$n.invalid("missing or invalid start")}var $n=function(){function e(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}e.invalid=function(t,n){if(void 0===n&&(n=null),!t)throw new w("need to specify a reason the Interval is invalid");var r=t instanceof Je?t:new Je(t,n);if(mt.throwOnInvalid)throw new y(r);return new e({invalid:r})},e.fromDateTimes=function(t,n){var r=li(t),i=li(n),o=Gn(r,i);return null==o?new e({start:r,end:i}):o},e.after=function(t,n){var r=Jn(n),i=li(t);return e.fromDateTimes(i,i.plus(r))},e.before=function(t,n){var r=Jn(n),i=li(t);return e.fromDateTimes(i.minus(r),i)},e.fromISO=function(t,n){var r=(t||"").split("/",2),i=r[0],o=r[1];if(i&&o){var a,u,s,c;try{u=(a=ci.fromISO(i,n)).isValid}catch(o){u=!1}try{c=(s=ci.fromISO(o,n)).isValid}catch(o){c=!1}if(u&&c)return e.fromDateTimes(a,s);if(u){var l=Wn.fromISO(o,n);if(l.isValid)return e.after(a,l)}else if(c){var f=Wn.fromISO(i,n);if(f.isValid)return e.before(s,f)}}return e.invalid("unparsable",'the input "'+t+"\" can't be parsed as ISO 8601")},e.isInterval=function(e){return e&&e.isLuxonInterval||!1};var n=e.prototype;return n.length=function(e){return void 0===e&&(e="milliseconds"),this.isValid?this.toDuration.apply(this,[e]).get(e):NaN},n.count=function(e){if(void 0===e&&(e="milliseconds"),!this.isValid)return NaN;var t=this.start.startOf(e),n=this.end.startOf(e);return Math.floor(n.diff(t,e).get(e))+1},n.hasSame=function(e){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,e))},n.isEmpty=function(){return this.s.valueOf()===this.e.valueOf()},n.isAfter=function(e){return!!this.isValid&&this.s>e},n.isBefore=function(e){return!!this.isValid&&this.e<=e},n.contains=function(e){return!!this.isValid&&(this.s<=e&&this.e>e)},n.set=function(t){var n=void 0===t?{}:t,r=n.start,i=n.end;return this.isValid?e.fromDateTimes(r||this.s,i||this.e):this},n.splitAt=function(){var t=this;if(!this.isValid)return[];for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];for(var o=r.map(li).filter(function(e){return t.contains(e)}).sort(),a=[],u=this.s,s=0;u<this.e;){var c=o[s]||this.e,l=+c>+this.e?this.e:c;a.push(e.fromDateTimes(u,l)),u=l,s+=1}return a},n.splitBy=function(t){var n=Jn(t);if(!this.isValid||!n.isValid||0===n.as("milliseconds"))return[];for(var r,i,o=this.s,a=[];o<this.e;)i=+(r=o.plus(n))>+this.e?this.e:r,a.push(e.fromDateTimes(o,i)),o=i;return a},n.divideEqually=function(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]},n.overlaps=function(e){return this.e>e.s&&this.s<e.e},n.abutsStart=function(e){return!!this.isValid&&+this.e==+e.s},n.abutsEnd=function(e){return!!this.isValid&&+e.e==+this.s},n.engulfs=function(e){return!!this.isValid&&(this.s<=e.s&&this.e>=e.e)},n.equals=function(e){return!(!this.isValid||!e.isValid)&&(this.s.equals(e.s)&&this.e.equals(e.e))},n.intersection=function(t){if(!this.isValid)return this;var n=this.s>t.s?this.s:t.s,r=this.e<t.e?this.e:t.e;return n>r?null:e.fromDateTimes(n,r)},n.union=function(t){if(!this.isValid)return this;var n=this.s<t.s?this.s:t.s,r=this.e>t.e?this.e:t.e;return e.fromDateTimes(n,r)},e.merge=function(e){var t=e.sort(function(e,t){return e.s-t.s}).reduce(function(e,t){var n=e[0],r=e[1];return r?r.overlaps(t)||r.abutsStart(t)?[n,r.union(t)]:[n.concat([r]),t]:[n,t]},[[],null]),n=t[0],r=t[1];return r&&n.push(r),n},e.xor=function(t){for(var n,r,i=null,o=0,a=[],u=t.map(function(e){return[{time:e.s,type:"s"},{time:e.e,type:"e"}]}),s=d((n=Array.prototype).concat.apply(n,u).sort(function(e,t){return e.time-t.time}));!(r=s()).done;){var c=r.value;1===(o+="s"===c.type?1:-1)?i=c.time:(i&&+i!=+c.time&&a.push(e.fromDateTimes(i,c.time)),i=null)}return e.merge(a)},n.difference=function(){for(var t=this,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e.xor([this].concat(r)).map(function(e){return t.intersection(e)}).filter(function(e){return e&&!e.isEmpty()})},n.toString=function(){return this.isValid?"["+this.s.toISO()+" – "+this.e.toISO()+")":Yn},n.toISO=function(e){return this.isValid?this.s.toISO(e)+"/"+this.e.toISO(e):Yn},n.toISODate=function(){return this.isValid?this.s.toISODate()+"/"+this.e.toISODate():Yn},n.toISOTime=function(e){return this.isValid?this.s.toISOTime(e)+"/"+this.e.toISOTime(e):Yn},n.toFormat=function(e,t){var n=(void 0===t?{}:t).separator,r=void 0===n?" – ":n;return this.isValid?""+this.s.toFormat(e)+r+this.e.toFormat(e):Yn},n.toDuration=function(e,t){return this.isValid?this.e.diff(this.s,e,t):Wn.invalid(this.invalidReason)},n.mapEndpoints=function(t){return e.fromDateTimes(t(this.s),t(this.e))},t(e,[{key:"start",get:function(){return this.isValid?this.s:null}},{key:"end",get:function(){return this.isValid?this.e:null}},{key:"isValid",get:function(){return null===this.invalidReason}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}}]),e}(),Bn=function(){function e(){}return e.hasDST=function(e){void 0===e&&(e=mt.defaultZone);var t=ci.now().setZone(e).set({month:12});return!e.universal&&t.offset!==t.set({month:6}).offset},e.isValidIANAZone=function(e){return rt.isValidSpecifier(e)&&rt.isValidZone(e)},e.normalizeZone=function(e){return ut(e,mt.defaultZone)},e.months=function(e,t){void 0===e&&(e="long");var n=void 0===t?{}:t,r=n.locale,i=void 0===r?null:r,o=n.numberingSystem,a=void 0===o?null:o,u=n.outputCalendar,s=void 0===u?"gregory":u;return xt.create(i,a,s).months(e)},e.monthsFormat=function(e,t){void 0===e&&(e="long");var n=void 0===t?{}:t,r=n.locale,i=void 0===r?null:r,o=n.numberingSystem,a=void 0===o?null:o,u=n.outputCalendar,s=void 0===u?"gregory":u;return xt.create(i,a,s).months(e,!0)},e.weekdays=function(e,t){void 0===e&&(e="long");var n=void 0===t?{}:t,r=n.locale,i=void 0===r?null:r,o=n.numberingSystem,a=void 0===o?null:o;return xt.create(i,a,null).weekdays(e)},e.weekdaysFormat=function(e,t){void 0===e&&(e="long");var n=void 0===t?{}:t,r=n.locale,i=void 0===r?null:r,o=n.numberingSystem,a=void 0===o?null:o;return xt.create(i,a,null).weekdays(e,!0)},e.meridiems=function(e){var t=(void 0===e?{}:e).locale,n=void 0===t?null:t;return xt.create(n).meridiems()},e.eras=function(e,t){void 0===e&&(e="short");var n=(void 0===t?{}:t).locale,r=void 0===n?null:n;return xt.create(r,null,"gregory").eras(e)},e.features=function(){var e=!1,t=!1,n=!1,r=!1;if(Q()){e=!0,t=K(),r=X();try{n="America/New_York"===new Intl.DateTimeFormat("en",{timeZone:"America/New_York"}).resolvedOptions().timeZone}catch(i){n=!1}}return{intl:e,intlTokens:t,zones:n,relative:r}},e}();function Qn(e,t){var n=function(e){return e.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf()},r=n(t)-n(e);return Math.floor(Wn.fromMillis(r).as("days"))}function Kn(e,t,n){for(var r,i,o={},a=0,u=[["years",function(e,t){return t.year-e.year}],["quarters",function(e,t){return t.quarter-e.quarter}],["months",function(e,t){return t.month-e.month+12*(t.year-e.year)}],["weeks",function(e,t){var n=Qn(e,t);return(n-n%7)/7}],["days",Qn]];a<u.length;a++){var s=u[a],c=s[0],l=s[1];if(n.indexOf(c)>=0){var f;r=c;var d,h=l(e,t);if((i=e.plus(((f={})[c]=h,f)))>t)e=e.plus(((d={})[c]=h-1,d)),h-=1;else e=i;o[c]=h}}return[e,o,i,r]}function Xn(e,t,n,r){var i=Kn(e,t,n),o=i[0],a=i[1],u=i[2],s=i[3],c=t-o,l=n.filter(function(e){return["hours","minutes","seconds","milliseconds"].indexOf(e)>=0});if(0===l.length){var f;if(u<t)u=o.plus(((f={})[s]=1,f));u!==o&&(a[s]=(a[s]||0)+c/(u-o))}var d,h=Wn.fromObject(Object.assign(a,r));return l.length>0?(d=Wn.fromMillis(c,r)).shiftTo.apply(d,l).plus(h):h}var er={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},tr={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},nr=er.hanidec.replace(/[\[|\]]/g,"").split("");function rr(e){var t=parseInt(e,10);if(isNaN(t)){t="";for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(-1!==e[n].search(er.hanidec))t+=nr.indexOf(e[n]);else for(var i in tr){var o=tr[i],a=o[0],u=o[1];r>=a&&r<=u&&(t+=r-a)}}return parseInt(t,10)}return t}function ir(e,t){var n=e.numberingSystem;return void 0===t&&(t=""),new RegExp(""+er[n||"latn"]+t)}var or="missing Intl.DateTimeFormat.formatToParts support";function ar(e,t){return void 0===t&&(t=function(e){return e}),{regex:e,deser:function(e){var n=e[0];return t(rr(n))}}}var ur=String.fromCharCode(160),sr="( |"+ur+")",cr=new RegExp(sr,"g");function lr(e){return e.replace(/\./g,"\\.?").replace(cr,sr)}function fr(e){return e.replace(/\./g,"").replace(cr," ").toLowerCase()}function dr(e,t){return null===e?null:{regex:RegExp(e.map(lr).join("|")),deser:function(n){var r=n[0];return e.findIndex(function(e){return fr(r)===fr(e)})+t}}}function hr(e,t){return{regex:e,deser:function(e){return ge(e[1],e[2])},groups:t}}function mr(e){return{regex:e,deser:function(e){return e[0]}}}function yr(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function vr(e,t){var n=ir(t),r=ir(t,"{2}"),i=ir(t,"{3}"),o=ir(t,"{4}"),a=ir(t,"{6}"),u=ir(t,"{1,2}"),s=ir(t,"{1,3}"),c=ir(t,"{1,6}"),l=ir(t,"{1,9}"),f=ir(t,"{2,4}"),d=ir(t,"{4,6}"),h=function(e){return{regex:RegExp(yr(e.val)),deser:function(e){return e[0]},literal:!0}},m=function(m){if(e.literal)return h(m);switch(m.val){case"G":return dr(t.eras("short",!1),0);case"GG":return dr(t.eras("long",!1),0);case"y":return ar(c);case"yy":return ar(f,ye);case"yyyy":return ar(o);case"yyyyy":return ar(d);case"yyyyyy":return ar(a);case"M":return ar(u);case"MM":return ar(r);case"MMM":return dr(t.months("short",!0,!1),1);case"MMMM":return dr(t.months("long",!0,!1),1);case"L":return ar(u);case"LL":return ar(r);case"LLL":return dr(t.months("short",!1,!1),1);case"LLLL":return dr(t.months("long",!1,!1),1);case"d":return ar(u);case"dd":return ar(r);case"o":return ar(s);case"ooo":return ar(i);case"HH":return ar(r);case"H":return ar(u);case"hh":return ar(r);case"h":return ar(u);case"mm":return ar(r);case"m":case"q":return ar(u);case"qq":return ar(r);case"s":return ar(u);case"ss":return ar(r);case"S":return ar(s);case"SSS":return ar(i);case"u":return mr(l);case"a":return dr(t.meridiems(),0);case"kkkk":return ar(o);case"kk":return ar(f,ye);case"W":return ar(u);case"WW":return ar(r);case"E":case"c":return ar(n);case"EEE":return dr(t.weekdays("short",!1,!1),1);case"EEEE":return dr(t.weekdays("long",!1,!1),1);case"ccc":return dr(t.weekdays("short",!0,!1),1);case"cccc":return dr(t.weekdays("long",!0,!1),1);case"Z":case"ZZ":return hr(new RegExp("([+-]"+u.source+")(?::("+r.source+"))?"),2);case"ZZZ":return hr(new RegExp("([+-]"+u.source+")("+r.source+")?"),2);case"z":return mr(/[a-z_+-/]{1,256}?/i);default:return h(m)}}(e)||{invalidReason:or};return m.token=e,m}var gr={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour:{numeric:"h","2-digit":"hh"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"}};function pr(e,t,n){var r=e.type,i=e.value;if("literal"===r)return{literal:!0,val:i};var o=n[r],a=gr[r];return"object"==typeof a&&(a=a[o]),a?{literal:!1,val:a}:void 0}function wr(e){return["^"+e.map(function(e){return e.regex}).reduce(function(e,t){return e+"("+t.source+")"},"")+"$",e]}function kr(e,t,n){var r=e.match(t);if(r){var i={},o=1;for(var a in n)if(re(n,a)){var u=n[a],s=u.groups?u.groups+1:1;!u.literal&&u.token&&(i[u.token.val[0]]=u.deser(r.slice(o,o+s))),o+=s}return[r,i]}return[r,{}]}function br(e){var t;return t=J(e.Z)?J(e.z)?null:rt.create(e.z):new ot(e.Z),J(e.q)||(e.M=3*(e.q-1)+1),J(e.h)||(e.h<12&&1===e.a?e.h+=12:12===e.h&&0===e.a&&(e.h=0)),0===e.G&&e.y&&(e.y=-e.y),J(e.u)||(e.S=se(e.u)),[Object.keys(e).reduce(function(t,n){var r=function(e){switch(e){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}}(n);return r&&(t[r]=e[n]),t},{}),t]}var Sr=null;function Or(){return Sr||(Sr=ci.fromMillis(1555555555555)),Sr}function Tr(e,t){if(e.literal)return e;var n=We.macroTokenToFormatOpts(e.val);if(!n)return e;var r=We.create(t,n).formatDateTimeParts(Or()).map(function(e){return pr(e,t,n)});return r.includes(void 0)?e:r}function Mr(e,t){var n;return(n=Array.prototype).concat.apply(n,e.map(function(e){return Tr(e,t)}))}function Nr(e,t,n){var r=Mr(We.parseFormat(n),e),i=r.map(function(t){return vr(t,e)}),o=i.find(function(e){return e.invalidReason});if(o)return{input:t,tokens:r,invalidReason:o.invalidReason};var a=wr(i),u=a[0],s=a[1],c=RegExp(u,"i"),l=kr(t,c,s),f=l[0],d=l[1],h=d?br(d):[null,null],m=h[0],y=h[1];if(re(d,"a")&&re(d,"H"))throw new g("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:r,regex:c,rawMatches:f,matches:d,result:m,zone:y}}function Er(e,t,n){var r=Nr(e,t,n);return[r.result,r.zone,r.invalidReason]}var Dr=[0,31,59,90,120,151,181,212,243,273,304,334],Ir=[0,31,60,91,121,152,182,213,244,274,305,335];function Vr(e,t){return new Je("unit out of range","you specified "+t+" (of type "+typeof t+") as a "+e+", which is invalid")}function Lr(e,t,n){var r=new Date(Date.UTC(e,t-1,n)).getUTCDay();return 0===r?7:r}function xr(e,t,n){return n+(le(e)?Ir:Dr)[t-1]}function Fr(e,t){var n=le(e)?Ir:Dr,r=n.findIndex(function(e){return e<t});return{month:r+1,day:t-n[r]}}function Cr(e){var t,n=e.year,r=e.month,i=e.day,o=xr(n,r,i),a=Lr(n,r,i),u=Math.floor((o-a+10)/7);return u<1?u=me(t=n-1):u>me(n)?(t=n+1,u=1):t=n,Object.assign({weekYear:t,weekNumber:u,weekday:a},be(e))}function Zr(e){var t,n=e.weekYear,r=e.weekNumber,i=e.weekday,o=Lr(n,1,4),a=fe(n),u=7*r+i-o-3;u<1?u+=fe(t=n-1):u>a?(t=n+1,u-=fe(n)):t=n;var s=Fr(t,u),c=s.month,l=s.day;return Object.assign({year:t,month:c,day:l},be(e))}function jr(e){var t=e.year,n=xr(t,e.month,e.day);return Object.assign({year:t,ordinal:n},be(e))}function Ar(e){var t=e.year,n=Fr(t,e.ordinal),r=n.month,i=n.day;return Object.assign({year:t,month:r,day:i},be(e))}function zr(e){var t=G(e.weekYear),n=ie(e.weekNumber,1,me(e.weekYear)),r=ie(e.weekday,1,7);return t?n?!r&&Vr("weekday",e.weekday):Vr("week",e.week):Vr("weekYear",e.weekYear)}function _r(e){var t=G(e.year),n=ie(e.ordinal,1,fe(e.year));return t?!n&&Vr("ordinal",e.ordinal):Vr("year",e.year)}function qr(e){var t=G(e.year),n=ie(e.month,1,12),r=ie(e.day,1,de(e.year,e.month));return t?n?!r&&Vr("day",e.day):Vr("month",e.month):Vr("year",e.year)}function Hr(e){var t=e.hour,n=e.minute,r=e.second,i=e.millisecond,o=ie(t,0,23)||24===t&&0===n&&0===r&&0===i,a=ie(n,0,59),u=ie(r,0,59),s=ie(i,0,999);return o?a?u?!s&&Vr("millisecond",i):Vr("second",r):Vr("minute",n):Vr("hour",t)}var Ur="Invalid DateTime",Rr=864e13;function Pr(e){return new Je("unsupported zone",'the zone "'+e.name+'" is not supported')}function Wr(e){return null===e.weekData&&(e.weekData=Cr(e.c)),e.weekData}function Jr(e,t){var n={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new ci(Object.assign({},n,t,{old:n}))}function Yr(e,t,n){var r=e-60*t*1e3,i=n.offset(r);if(t===i)return[r,t];r-=60*(i-t)*1e3;var o=n.offset(r);return i===o?[r,i]:[e-60*Math.min(i,o)*1e3,Math.max(i,o)]}function Gr(e,t){var n=new Date(e+=60*t*1e3);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function $r(e,t,n){return Yr(he(e),t,n)}function Br(e,t){var n=e.o,r=e.c.year+Math.trunc(t.years),i=e.c.month+Math.trunc(t.months)+3*Math.trunc(t.quarters),o=Object.assign({},e.c,{year:r,month:i,day:Math.min(e.c.day,de(r,i))+Math.trunc(t.days)+7*Math.trunc(t.weeks)}),a=Wn.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),u=Yr(he(o),n,e.zone),s=u[0],c=u[1];return 0!==a&&(s+=a,c=e.zone.offset(s)),{ts:s,o:c}}function Qr(e,t,n,r,i){var o=n.setZone,a=n.zone;if(e&&0!==Object.keys(e).length){var u=t||a,s=ci.fromObject(Object.assign(e,n,{zone:u,setZone:void 0}));return o?s:s.setZone(a)}return ci.invalid(new Je("unparsable",'the input "'+i+"\" can't be parsed as "+r))}function Kr(e,t,n){return void 0===n&&(n=!0),e.isValid?We.create(xt.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function Xr(e,t){var n=t.suppressSeconds,r=void 0!==n&&n,i=t.suppressMilliseconds,o=void 0!==i&&i,a=t.includeOffset,u=t.includePrefix,s=void 0!==u&&u,c=t.includeZone,l=void 0!==c&&c,f=t.spaceZone,d=void 0!==f&&f,h=t.format,m=void 0===h?"extended":h,y="basic"===m?"HHmm":"HH:mm";r&&0===e.second&&0===e.millisecond||(y+="basic"===m?"ss":":ss",o&&0===e.millisecond||(y+=".SSS")),(l||a)&&d&&(y+=" "),l?y+="z":a&&(y+="basic"===m?"ZZZ":"ZZ");var v=Kr(e,y);return s&&(v="T"+v),v}var ei={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},ti={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},ni={ordinal:1,hour:0,minute:0,second:0,millisecond:0},ri=["year","month","day","hour","minute","second","millisecond"],ii=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],oi=["year","ordinal","hour","minute","second","millisecond"];function ai(e){var t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new p(e);return t}function ui(e,t){for(var n,r=d(ri);!(n=r()).done;){var i=n.value;J(e[i])&&(e[i]=ei[i])}var o=qr(e)||Hr(e);if(o)return ci.invalid(o);var a=mt.now(),u=$r(e,t.offset(a),t),s=u[0],c=u[1];return new ci({ts:s,zone:t,o:c})}function si(e,t,n){var r=!!J(n.round)||n.round,i=function(e,i){return e=ce(e,r||n.calendary?0:2,!0),t.loc.clone(n).relFormatter(n).format(e,i)},o=function(r){return n.calendary?t.hasSame(e,r)?0:t.startOf(r).diff(e.startOf(r),r).get(r):t.diff(e,r).get(r)};if(n.unit)return i(o(n.unit),n.unit);for(var a,u=d(n.units);!(a=u()).done;){var s=a.value,c=o(s);if(Math.abs(c)>=1)return i(c,s)}return i(0,n.units[n.units.length-1])}var ci=function(){function e(e){var t=e.zone||mt.defaultZone,n=e.invalid||(Number.isNaN(e.ts)?new Je("invalid input"):null)||(t.isValid?null:Pr(t));this.ts=J(e.ts)?mt.now():e.ts;var r=null,i=null;if(!n)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t)){var o=[e.old.c,e.old.o];r=o[0],i=o[1]}else{var a=t.offset(this.ts);r=Gr(this.ts,a),r=(n=Number.isNaN(r.year)?new Je("invalid input"):null)?null:r,i=n?null:a}this._zone=t,this.loc=e.loc||xt.create(),this.invalid=n,this.weekData=null,this.c=r,this.o=i,this.isLuxonDateTime=!0}e.now=function(){return new e({})},e.local=function(t,n,r,i,o,a,u){return J(t)?new e({}):ui({year:t,month:n,day:r,hour:i,minute:o,second:a,millisecond:u},mt.defaultZone)},e.utc=function(t,n,r,i,o,a,u){return J(t)?new e({ts:mt.now(),zone:ot.utcInstance}):ui({year:t,month:n,day:r,hour:i,minute:o,second:a,millisecond:u},ot.utcInstance)},e.fromJSDate=function(t,n){void 0===n&&(n={});var r=B(t)?t.valueOf():NaN;if(Number.isNaN(r))return e.invalid("invalid input");var i=ut(n.zone,mt.defaultZone);return i.isValid?new e({ts:r,zone:i,loc:xt.fromObject(n)}):e.invalid(Pr(i))},e.fromMillis=function(t,n){if(void 0===n&&(n={}),Y(t))return t<-Rr||t>Rr?e.invalid("Timestamp out of range"):new e({ts:t,zone:ut(n.zone,mt.defaultZone),loc:xt.fromObject(n)});throw new w("fromMillis requires a numerical input, but received a "+typeof t+" with value "+t)},e.fromSeconds=function(t,n){if(void 0===n&&(n={}),Y(t))return new e({ts:1e3*t,zone:ut(n.zone,mt.defaultZone),loc:xt.fromObject(n)});throw new w("fromSeconds requires a numerical input")},e.fromObject=function(t){var n=ut(t.zone,mt.defaultZone);if(!n.isValid)return e.invalid(Pr(n));var r=mt.now(),i=n.offset(r),o=we(t,ai,["zone","locale","outputCalendar","numberingSystem"]),a=!J(o.ordinal),u=!J(o.year),s=!J(o.month)||!J(o.day),c=u||s,l=o.weekYear||o.weekNumber,f=xt.fromObject(t);if((c||a)&&l)throw new g("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(s&&a)throw new g("Can't mix ordinal dates with month/day");var h,m,y=l||o.weekday&&!c,v=Gr(r,i);y?(h=ii,m=ti,v=Cr(v)):a?(h=oi,m=ni,v=jr(v)):(h=ri,m=ei);for(var p,w=!1,k=d(h);!(p=k()).done;){var b=p.value;J(o[b])?o[b]=w?m[b]:v[b]:w=!0}var S=(y?zr(o):a?_r(o):qr(o))||Hr(o);if(S)return e.invalid(S);var O=$r(y?Zr(o):a?Ar(o):o,i,n),T=new e({ts:O[0],zone:n,o:O[1],loc:f});return o.weekday&&c&&t.weekday!==T.weekday?e.invalid("mismatched weekday","you can't specify both a weekday of "+o.weekday+" and a date of "+T.toISO()):T},e.fromISO=function(e,t){void 0===t&&(t={});var n=Sn(e);return Qr(n[0],n[1],t,"ISO 8601",e)},e.fromRFC2822=function(e,t){void 0===t&&(t={});var n=On(e);return Qr(n[0],n[1],t,"RFC 2822",e)},e.fromHTTP=function(e,t){void 0===t&&(t={});var n=Tn(e);return Qr(n[0],n[1],t,"HTTP",t)},e.fromFormat=function(t,n,r){if(void 0===r&&(r={}),J(t)||J(n))throw new w("fromFormat requires an input string and a format");var i=r,o=i.locale,a=void 0===o?null:o,u=i.numberingSystem,s=void 0===u?null:u,c=Er(xt.fromOpts({locale:a,numberingSystem:s,defaultToEN:!0}),t,n),l=c[0],f=c[1],d=c[2];return d?e.invalid(d):Qr(l,f,r,"format "+n,t)},e.fromString=function(t,n,r){return void 0===r&&(r={}),e.fromFormat(t,n,r)},e.fromSQL=function(e,t){void 0===t&&(t={});var n=xn(e);return Qr(n[0],n[1],t,"SQL",e)},e.invalid=function(t,n){if(void 0===n&&(n=null),!t)throw new w("need to specify a reason the DateTime is invalid");var r=t instanceof Je?t:new Je(t,n);if(mt.throwOnInvalid)throw new m(r);return new e({invalid:r})},e.isDateTime=function(e){return e&&e.isLuxonDateTime||!1};var n=e.prototype;return n.get=function(e){return this[e]},n.resolvedLocaleOpts=function(e){void 0===e&&(e={});var t=We.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t.locale,numberingSystem:t.numberingSystem,outputCalendar:t.calendar}},n.toUTC=function(e,t){return void 0===e&&(e=0),void 0===t&&(t={}),this.setZone(ot.instance(e),t)},n.toLocal=function(){return this.setZone(mt.defaultZone)},n.setZone=function(t,n){var r=void 0===n?{}:n,i=r.keepLocalTime,o=void 0!==i&&i,a=r.keepCalendarTime,u=void 0!==a&&a;if((t=ut(t,mt.defaultZone)).equals(this.zone))return this;if(t.isValid){var s=this.ts;if(o||u){var c=t.offset(this.ts);s=$r(this.toObject(),c,t)[0]}return Jr(this,{ts:s,zone:t})}return e.invalid(Pr(t))},n.reconfigure=function(e){var t=void 0===e?{}:e,n=t.locale,r=t.numberingSystem,i=t.outputCalendar;return Jr(this,{loc:this.loc.clone({locale:n,numberingSystem:r,outputCalendar:i})})},n.setLocale=function(e){return this.reconfigure({locale:e})},n.set=function(e){if(!this.isValid)return this;var t,n=we(e,ai,[]);!J(n.weekYear)||!J(n.weekNumber)||!J(n.weekday)?t=Zr(Object.assign(Cr(this.c),n)):J(n.ordinal)?(t=Object.assign(this.toObject(),n),J(n.day)&&(t.day=Math.min(de(t.year,t.month),t.day))):t=Ar(Object.assign(jr(this.c),n));var r=$r(t,this.o,this.zone);return Jr(this,{ts:r[0],o:r[1]})},n.plus=function(e){return this.isValid?Jr(this,Br(this,Jn(e))):this},n.minus=function(e){return this.isValid?Jr(this,Br(this,Jn(e).negate())):this},n.startOf=function(e){if(!this.isValid)return this;var t={},n=Wn.normalizeUnit(e);switch(n){case"years":t.month=1;case"quarters":case"months":t.day=1;case"weeks":case"days":t.hour=0;case"hours":t.minute=0;case"minutes":t.second=0;case"seconds":t.millisecond=0}if("weeks"===n&&(t.weekday=1),"quarters"===n){var r=Math.ceil(this.month/3);t.month=3*(r-1)+1}return this.set(t)},n.endOf=function(e){var t;return this.isValid?this.plus((t={},t[e]=1,t)).startOf(e).minus(1):this},n.toFormat=function(e,t){return void 0===t&&(t={}),this.isValid?We.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):Ur},n.toLocaleString=function(e){return void 0===e&&(e=T),this.isValid?We.create(this.loc.clone(e),e).formatDateTime(this):Ur},n.toLocaleParts=function(e){return void 0===e&&(e={}),this.isValid?We.create(this.loc.clone(e),e).formatDateTimeParts(this):[]},n.toISO=function(e){return void 0===e&&(e={}),this.isValid?this.toISODate(e)+"T"+this.toISOTime(e):null},n.toISODate=function(e){var t=(void 0===e?{}:e).format,n="basic"===(void 0===t?"extended":t)?"yyyyMMdd":"yyyy-MM-dd";return this.year>9999&&(n="+"+n),Kr(this,n)},n.toISOWeekDate=function(){return Kr(this,"kkkk-'W'WW-c")},n.toISOTime=function(e){var t=void 0===e?{}:e,n=t.suppressMilliseconds,r=void 0!==n&&n,i=t.suppressSeconds,o=void 0!==i&&i,a=t.includeOffset,u=void 0===a||a,s=t.includePrefix,c=void 0!==s&&s,l=t.format;return Xr(this,{suppressSeconds:o,suppressMilliseconds:r,includeOffset:u,includePrefix:c,format:void 0===l?"extended":l})},n.toRFC2822=function(){return Kr(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)},n.toHTTP=function(){return Kr(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")},n.toSQLDate=function(){return Kr(this,"yyyy-MM-dd")},n.toSQLTime=function(e){var t=void 0===e?{}:e,n=t.includeOffset,r=void 0===n||n,i=t.includeZone;return Xr(this,{includeOffset:r,includeZone:void 0!==i&&i,spaceZone:!0})},n.toSQL=function(e){return void 0===e&&(e={}),this.isValid?this.toSQLDate()+" "+this.toSQLTime(e):null},n.toString=function(){return this.isValid?this.toISO():Ur},n.valueOf=function(){return this.toMillis()},n.toMillis=function(){return this.isValid?this.ts:NaN},n.toSeconds=function(){return this.isValid?this.ts/1e3:NaN},n.toJSON=function(){return this.toISO()},n.toBSON=function(){return this.toJSDate()},n.toObject=function(e){if(void 0===e&&(e={}),!this.isValid)return{};var t=Object.assign({},this.c);return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t},n.toJSDate=function(){return new Date(this.isValid?this.ts:NaN)},n.diff=function(e,t,n){if(void 0===t&&(t="milliseconds"),void 0===n&&(n={}),!this.isValid||!e.isValid)return Wn.invalid(this.invalid||e.invalid,"created by diffing an invalid DateTime");var r=Object.assign({locale:this.locale,numberingSystem:this.numberingSystem},n),i=ee(t).map(Wn.normalizeUnit),o=e.valueOf()>this.valueOf(),a=Xn(o?this:e,o?e:this,i,r);return o?a.negate():a},n.diffNow=function(t,n){return void 0===t&&(t="milliseconds"),void 0===n&&(n={}),this.diff(e.now(),t,n)},n.until=function(e){return this.isValid?$n.fromDateTimes(this,e):this},n.hasSame=function(e,t){if(!this.isValid)return!1;var n=e.valueOf(),r=this.setZone(e.zone,{keepLocalTime:!0});return r.startOf(t)<=n&&n<=r.endOf(t)},n.equals=function(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)},n.toRelative=function(t){if(void 0===t&&(t={}),!this.isValid)return null;var n=t.base||e.fromObject({zone:this.zone}),r=t.padding?this<n?-t.padding:t.padding:0;return si(n,this.plus(r),Object.assign(t,{numeric:"always",units:["years","months","days","hours","minutes","seconds"]}))},n.toRelativeCalendar=function(t){return void 0===t&&(t={}),this.isValid?si(t.base||e.fromObject({zone:this.zone}),this,Object.assign(t,{numeric:"auto",units:["years","months","days"],calendary:!0})):null},e.min=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];if(!n.every(e.isDateTime))throw new w("min requires all arguments be DateTimes");return te(n,function(e){return e.valueOf()},Math.min)},e.max=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];if(!n.every(e.isDateTime))throw new w("max requires all arguments be DateTimes");return te(n,function(e){return e.valueOf()},Math.max)},e.fromFormatExplain=function(e,t,n){void 0===n&&(n={});var r=n,i=r.locale,o=void 0===i?null:i,a=r.numberingSystem,u=void 0===a?null:a;return Nr(xt.fromOpts({locale:o,numberingSystem:u,defaultToEN:!0}),e,t)},e.fromStringExplain=function(t,n,r){return void 0===r&&(r={}),e.fromFormatExplain(t,n,r)},t(e,[{key:"isValid",get:function(){return null===this.invalid}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}},{key:"locale",get:function(){return this.isValid?this.loc.locale:null}},{key:"numberingSystem",get:function(){return this.isValid?this.loc.numberingSystem:null}},{key:"outputCalendar",get:function(){return this.isValid?this.loc.outputCalendar:null}},{key:"zone",get:function(){return this._zone}},{key:"zoneName",get:function(){return this.isValid?this.zone.name:null}},{key:"year",get:function(){return this.isValid?this.c.year:NaN}},{key:"quarter",get:function(){return this.isValid?Math.ceil(this.c.month/3):NaN}},{key:"month",get:function(){return this.isValid?this.c.month:NaN}},{key:"day",get:function(){return this.isValid?this.c.day:NaN}},{key:"hour",get:function(){return this.isValid?this.c.hour:NaN}},{key:"minute",get:function(){return this.isValid?this.c.minute:NaN}},{key:"second",get:function(){return this.isValid?this.c.second:NaN}},{key:"millisecond",get:function(){return this.isValid?this.c.millisecond:NaN}},{key:"weekYear",get:function(){return this.isValid?Wr(this).weekYear:NaN}},{key:"weekNumber",get:function(){return this.isValid?Wr(this).weekNumber:NaN}},{key:"weekday",get:function(){return this.isValid?Wr(this).weekday:NaN}},{key:"ordinal",get:function(){return this.isValid?jr(this.c).ordinal:NaN}},{key:"monthShort",get:function(){return this.isValid?Bn.months("short",{locale:this.locale})[this.month-1]:null}},{key:"monthLong",get:function(){return this.isValid?Bn.months("long",{locale:this.locale})[this.month-1]:null}},{key:"weekdayShort",get:function(){return this.isValid?Bn.weekdays("short",{locale:this.locale})[this.weekday-1]:null}},{key:"weekdayLong",get:function(){return this.isValid?Bn.weekdays("long",{locale:this.locale})[this.weekday-1]:null}},{key:"offset",get:function(){return this.isValid?+this.o:NaN}},{key:"offsetNameShort",get:function(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}},{key:"offsetNameLong",get:function(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}},{key:"isOffsetFixed",get:function(){return this.isValid?this.zone.universal:null}},{key:"isInDST",get:function(){return!this.isOffsetFixed&&(this.offset>this.set({month:1}).offset||this.offset>this.set({month:5}).offset)}},{key:"isInLeapYear",get:function(){return le(this.year)}},{key:"daysInMonth",get:function(){return de(this.year,this.month)}},{key:"daysInYear",get:function(){return this.isValid?fe(this.year):NaN}},{key:"weeksInWeekYear",get:function(){return this.isValid?me(this.weekYear):NaN}}],[{key:"DATE_SHORT",get:function(){return T}},{key:"DATE_MED",get:function(){return M}},{key:"DATE_MED_WITH_WEEKDAY",get:function(){return N}},{key:"DATE_FULL",get:function(){return E}},{key:"DATE_HUGE",get:function(){return D}},{key:"TIME_SIMPLE",get:function(){return I}},{key:"TIME_WITH_SECONDS",get:function(){return V}},{key:"TIME_WITH_SHORT_OFFSET",get:function(){return L}},{key:"TIME_WITH_LONG_OFFSET",get:function(){return x}},{key:"TIME_24_SIMPLE",get:function(){return F}},{key:"TIME_24_WITH_SECONDS",get:function(){return C}},{key:"TIME_24_WITH_SHORT_OFFSET",get:function(){return Z}},{key:"TIME_24_WITH_LONG_OFFSET",get:function(){return j}},{key:"DATETIME_SHORT",get:function(){return A}},{key:"DATETIME_SHORT_WITH_SECONDS",get:function(){return z}},{key:"DATETIME_MED",get:function(){return _}},{key:"DATETIME_MED_WITH_SECONDS",get:function(){return q}},{key:"DATETIME_MED_WITH_WEEKDAY",get:function(){return H}},{key:"DATETIME_FULL",get:function(){return U}},{key:"DATETIME_FULL_WITH_SECONDS",get:function(){return R}},{key:"DATETIME_HUGE",get:function(){return P}},{key:"DATETIME_HUGE_WITH_SECONDS",get:function(){return W}}]),e}();function li(e){if(ci.isDateTime(e))return e;if(e&&e.valueOf&&Y(e.valueOf()))return ci.fromJSDate(e);if(e&&"object"==typeof e)return ci.fromObject(e);throw new w("Unknown datetime argument: "+e+", of type "+typeof e)}var fi="1.26.0";exports.DateTime=ci,exports.Duration=Wn,exports.FixedOffsetZone=ot,exports.IANAZone=rt,exports.Info=Bn,exports.Interval=$n,exports.InvalidZone=at,exports.LocalZone=$e,exports.Settings=mt,exports.VERSION=fi,exports.Zone=Ye;
},{}],"nidE":[function(require,module,exports) {
window.NodeList&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=function(o,t){t=t||window;for(var i=0;i<this.length;i++)o.call(t,this[i],i,this)});
},{}],"J4Nk":[function(require,module,exports) {
"use strict";var r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,e=Object.prototype.propertyIsEnumerable;function n(r){if(null==r)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(r)}function o(){try{if(!Object.assign)return!1;var r=new String("abc");if(r[5]="de","5"===Object.getOwnPropertyNames(r)[0])return!1;for(var t={},e=0;e<10;e++)t["_"+String.fromCharCode(e)]=e;if("0123456789"!==Object.getOwnPropertyNames(t).map(function(r){return t[r]}).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach(function(r){n[r]=r}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(o){return!1}}module.exports=o()?Object.assign:function(o,c){for(var a,i,s=n(o),f=1;f<arguments.length;f++){for(var u in a=Object(arguments[f]))t.call(a,u)&&(s[u]=a[u]);if(r){i=r(a);for(var b=0;b<i.length;b++)e.call(a,i[b])&&(s[i[b]]=a[i[b]])}}return s};
},{}],"rd3l":[function(require,module,exports) {
module.exports=function(o){return o&&"object"==typeof o&&"function"==typeof o.copy&&"function"==typeof o.fill&&"function"==typeof o.readUInt8};
},{}],"fCKU":[function(require,module,exports) {
"function"==typeof Object.create?module.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:module.exports=function(t,e){t.super_=e;var o=function(){};o.prototype=e.prototype,t.prototype=new o,t.prototype.constructor=t};
},{}],"pBGv":[function(require,module,exports) {

var t,e,n=module.exports={};function r(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function i(e){if(t===setTimeout)return setTimeout(e,0);if((t===r||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}function u(t){if(e===clearTimeout)return clearTimeout(t);if((e===o||!e)&&clearTimeout)return e=clearTimeout,clearTimeout(t);try{return e(t)}catch(n){try{return e.call(null,t)}catch(n){return e.call(this,t)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:r}catch(n){t=r}try{e="function"==typeof clearTimeout?clearTimeout:o}catch(n){e=o}}();var c,s=[],l=!1,a=-1;function f(){l&&c&&(l=!1,c.length?s=c.concat(s):a=-1,s.length&&h())}function h(){if(!l){var t=i(f);l=!0;for(var e=s.length;e;){for(c=s,s=[];++a<e;)c&&c[a].run();a=-1,e=s.length}c=null,l=!1,u(t)}}function m(t,e){this.fun=t,this.array=e}function p(){}n.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];s.push(new m(t,e)),1!==s.length||l||i(h)},m.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.env={},n.argv=[],n.version="",n.versions={},n.on=p,n.addListener=p,n.once=p,n.off=p,n.removeListener=p,n.removeAllListeners=p,n.emit=p,n.prependListener=p,n.prependOnceListener=p,n.listeners=function(t){return[]},n.binding=function(t){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(t){throw new Error("process.chdir is not supported")},n.umask=function(){return 0};
},{}],"XOAc":[function(require,module,exports) {
var global = arguments[3];
var process = require("process");
var e=arguments[3],t=require("process"),r=/%[sdj%]/g;exports.format=function(e){if(!v(e)){for(var t=[],n=0;n<arguments.length;n++)t.push(i(arguments[n]));return t.join(" ")}n=1;for(var o=arguments,s=o.length,u=String(e).replace(r,function(e){if("%%"===e)return"%";if(n>=s)return e;switch(e){case"%s":return String(o[n++]);case"%d":return Number(o[n++]);case"%j":try{return JSON.stringify(o[n++])}catch(t){return"[Circular]"}default:return e}}),c=o[n];n<s;c=o[++n])h(c)||!z(c)?u+=" "+c:u+=" "+i(c);return u},exports.deprecate=function(r,n){if(j(e.process))return function(){return exports.deprecate(r,n).apply(this,arguments)};if(!0===t.noDeprecation)return r;var o=!1;return function(){if(!o){if(t.throwDeprecation)throw new Error(n);t.traceDeprecation?console.trace(n):console.error(n),o=!0}return r.apply(this,arguments)}};var n,o={};function i(e,t){var r={seen:[],stylize:u};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),x(t)?r.showHidden=t:t&&exports._extend(r,t),j(r.showHidden)&&(r.showHidden=!1),j(r.depth)&&(r.depth=2),j(r.colors)&&(r.colors=!1),j(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=s),p(r,e,r.depth)}function s(e,t){var r=i.styles[t];return r?"["+i.colors[r][0]+"m"+e+"["+i.colors[r][1]+"m":e}function u(e,t){return e}function c(e){var t={};return e.forEach(function(e,r){t[e]=!0}),t}function p(e,t,r){if(e.customInspect&&t&&D(t.inspect)&&t.inspect!==exports.inspect&&(!t.constructor||t.constructor.prototype!==t)){var n=t.inspect(r,e);return v(n)||(n=p(e,n,r)),n}var o=l(e,t);if(o)return o;var i=Object.keys(t),s=c(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(t)),E(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return a(t);if(0===i.length){if(D(t)){var u=t.name?": "+t.name:"";return e.stylize("[Function"+u+"]","special")}if(O(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(w(t))return e.stylize(Date.prototype.toString.call(t),"date");if(E(t))return a(t)}var x,h="",b=!1,m=["{","}"];(d(t)&&(b=!0,m=["[","]"]),D(t))&&(h=" [Function"+(t.name?": "+t.name:"")+"]");return O(t)&&(h=" "+RegExp.prototype.toString.call(t)),w(t)&&(h=" "+Date.prototype.toUTCString.call(t)),E(t)&&(h=" "+a(t)),0!==i.length||b&&0!=t.length?r<0?O(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),x=b?f(e,t,r,s,i):i.map(function(n){return g(e,t,r,s,n,b)}),e.seen.pop(),y(x,h,m)):m[0]+h+m[1]}function l(e,t){if(j(t))return e.stylize("undefined","undefined");if(v(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return m(t)?e.stylize(""+t,"number"):x(t)?e.stylize(""+t,"boolean"):h(t)?e.stylize("null","null"):void 0}function a(e){return"["+Error.prototype.toString.call(e)+"]"}function f(e,t,r,n,o){for(var i=[],s=0,u=t.length;s<u;++s)$(t,String(s))?i.push(g(e,t,r,n,String(s),!0)):i.push("");return o.forEach(function(o){o.match(/^\d+$/)||i.push(g(e,t,r,n,o,!0))}),i}function g(e,t,r,n,o,i){var s,u,c;if((c=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]}).get?u=c.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):c.set&&(u=e.stylize("[Setter]","special")),$(n,o)||(s="["+o+"]"),u||(e.seen.indexOf(c.value)<0?(u=h(r)?p(e,c.value,null):p(e,c.value,r-1)).indexOf("\n")>-1&&(u=i?u.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+u.split("\n").map(function(e){return"   "+e}).join("\n")):u=e.stylize("[Circular]","special")),j(s)){if(i&&o.match(/^\d+$/))return u;(s=JSON.stringify(""+o)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=e.stylize(s,"string"))}return s+": "+u}function y(e,t,r){return e.reduce(function(e,t){return 0,t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]:r[0]+t+" "+e.join(", ")+" "+r[1]}function d(e){return Array.isArray(e)}function x(e){return"boolean"==typeof e}function h(e){return null===e}function b(e){return null==e}function m(e){return"number"==typeof e}function v(e){return"string"==typeof e}function S(e){return"symbol"==typeof e}function j(e){return void 0===e}function O(e){return z(e)&&"[object RegExp]"===A(e)}function z(e){return"object"==typeof e&&null!==e}function w(e){return z(e)&&"[object Date]"===A(e)}function E(e){return z(e)&&("[object Error]"===A(e)||e instanceof Error)}function D(e){return"function"==typeof e}function N(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e}function A(e){return Object.prototype.toString.call(e)}function J(e){return e<10?"0"+e.toString(10):e.toString(10)}exports.debuglog=function(e){if(j(n)&&(n=""),e=e.toUpperCase(),!o[e])if(new RegExp("\\b"+e+"\\b","i").test(n)){var r=t.pid;o[e]=function(){var t=exports.format.apply(exports,arguments);console.error("%s %d: %s",e,r,t)}}else o[e]=function(){};return o[e]},exports.inspect=i,i.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},i.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},exports.isArray=d,exports.isBoolean=x,exports.isNull=h,exports.isNullOrUndefined=b,exports.isNumber=m,exports.isString=v,exports.isSymbol=S,exports.isUndefined=j,exports.isRegExp=O,exports.isObject=z,exports.isDate=w,exports.isError=E,exports.isFunction=D,exports.isPrimitive=N,exports.isBuffer=require("./support/isBuffer");var R=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function H(){var e=new Date,t=[J(e.getHours()),J(e.getMinutes()),J(e.getSeconds())].join(":");return[e.getDate(),R[e.getMonth()],t].join(" ")}function $(e,t){return Object.prototype.hasOwnProperty.call(e,t)}exports.log=function(){console.log("%s - %s",H(),exports.format.apply(exports,arguments))},exports.inherits=require("inherits"),exports._extend=function(e,t){if(!t||!z(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e};
},{"./support/isBuffer":"rd3l","inherits":"fCKU","process":"pBGv"}],"DlZn":[function(require,module,exports) {
var global = arguments[3];
var t=arguments[3],e=require("object-assign");function r(t,e){if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0}function n(e){return t.Buffer&&"function"==typeof t.Buffer.isBuffer?t.Buffer.isBuffer(e):!(null==e||!e._isBuffer)}var i=require("util/"),o=Object.prototype.hasOwnProperty,u=Array.prototype.slice,a="foo"===function(){}.name;function c(t){return Object.prototype.toString.call(t)}function f(e){return!n(e)&&("function"==typeof t.ArrayBuffer&&("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):!!e&&(e instanceof DataView||!!(e.buffer&&e.buffer instanceof ArrayBuffer))))}var s=module.exports=q,l=/\s*function\s+([^\(\s]*)\s*/;function p(t){if(i.isFunction(t)){if(a)return t.name;var e=t.toString().match(l);return e&&e[1]}}function g(t,e){return"string"==typeof t?t.length<e?t:t.slice(0,e):t}function E(t){if(a||!i.isFunction(t))return i.inspect(t);var e=p(t);return"[Function"+(e?": "+e:"")+"]"}function h(t){return g(E(t.actual),128)+" "+t.operator+" "+g(E(t.expected),128)}function y(t,e,r,n,i){throw new s.AssertionError({message:r,actual:t,expected:e,operator:n,stackStartFunction:i})}function q(t,e){t||y(t,!0,e,"==",s.ok)}function d(t,e,o,u){if(t===e)return!0;if(n(t)&&n(e))return 0===r(t,e);if(i.isDate(t)&&i.isDate(e))return t.getTime()===e.getTime();if(i.isRegExp(t)&&i.isRegExp(e))return t.source===e.source&&t.global===e.global&&t.multiline===e.multiline&&t.lastIndex===e.lastIndex&&t.ignoreCase===e.ignoreCase;if(null!==t&&"object"==typeof t||null!==e&&"object"==typeof e){if(f(t)&&f(e)&&c(t)===c(e)&&!(t instanceof Float32Array||t instanceof Float64Array))return 0===r(new Uint8Array(t.buffer),new Uint8Array(e.buffer));if(n(t)!==n(e))return!1;var a=(u=u||{actual:[],expected:[]}).actual.indexOf(t);return-1!==a&&a===u.expected.indexOf(e)||(u.actual.push(t),u.expected.push(e),b(t,e,o,u))}return o?t===e:t==e}function m(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function b(t,e,r,n){if(null==t||null==e)return!1;if(i.isPrimitive(t)||i.isPrimitive(e))return t===e;if(r&&Object.getPrototypeOf(t)!==Object.getPrototypeOf(e))return!1;var o=m(t),a=m(e);if(o&&!a||!o&&a)return!1;if(o)return d(t=u.call(t),e=u.call(e),r);var c,f,s=A(t),l=A(e);if(s.length!==l.length)return!1;for(s.sort(),l.sort(),f=s.length-1;f>=0;f--)if(s[f]!==l[f])return!1;for(f=s.length-1;f>=0;f--)if(!d(t[c=s[f]],e[c],r,n))return!1;return!0}function v(t,e,r){d(t,e,!0)&&y(t,e,r,"notDeepStrictEqual",v)}function x(t,e){if(!t||!e)return!1;if("[object RegExp]"==Object.prototype.toString.call(e))return e.test(t);try{if(t instanceof e)return!0}catch(r){}return!Error.isPrototypeOf(e)&&!0===e.call({},t)}function S(t){var e;try{t()}catch(r){e=r}return e}function w(t,e,r,n){var o;if("function"!=typeof e)throw new TypeError('"block" argument must be a function');"string"==typeof r&&(n=r,r=null),o=S(e),n=(r&&r.name?" ("+r.name+").":".")+(n?" "+n:"."),t&&!o&&y(o,r,"Missing expected exception"+n);var u="string"==typeof n,a=!t&&o&&!r;if((!t&&i.isError(o)&&u&&x(o,r)||a)&&y(o,r,"Got unwanted exception"+n),t&&o&&r&&!x(o,r)||!t&&o)throw o}function O(t,e){t||y(t,!0,e,"==",O)}s.AssertionError=function(t){this.name="AssertionError",this.actual=t.actual,this.expected=t.expected,this.operator=t.operator,t.message?(this.message=t.message,this.generatedMessage=!1):(this.message=h(this),this.generatedMessage=!0);var e=t.stackStartFunction||y;if(Error.captureStackTrace)Error.captureStackTrace(this,e);else{var r=new Error;if(r.stack){var n=r.stack,i=p(e),o=n.indexOf("\n"+i);if(o>=0){var u=n.indexOf("\n",o+1);n=n.substring(u+1)}this.stack=n}}},i.inherits(s.AssertionError,Error),s.fail=y,s.ok=q,s.equal=function(t,e,r){t!=e&&y(t,e,r,"==",s.equal)},s.notEqual=function(t,e,r){t==e&&y(t,e,r,"!=",s.notEqual)},s.deepEqual=function(t,e,r){d(t,e,!1)||y(t,e,r,"deepEqual",s.deepEqual)},s.deepStrictEqual=function(t,e,r){d(t,e,!0)||y(t,e,r,"deepStrictEqual",s.deepStrictEqual)},s.notDeepEqual=function(t,e,r){d(t,e,!1)&&y(t,e,r,"notDeepEqual",s.notDeepEqual)},s.notDeepStrictEqual=v,s.strictEqual=function(t,e,r){t!==e&&y(t,e,r,"===",s.strictEqual)},s.notStrictEqual=function(t,e,r){t===e&&y(t,e,r,"!==",s.notStrictEqual)},s.throws=function(t,e,r){w(!0,t,e,r)},s.doesNotThrow=function(t,e,r){w(!1,t,e,r)},s.ifError=function(t){if(t)throw t},s.strict=e(O,s,{equal:s.strictEqual,deepEqual:s.deepStrictEqual,notEqual:s.notStrictEqual,notDeepEqual:s.notDeepStrictEqual}),s.strict.strict=s.strict;var A=Object.keys||function(t){var e=[];for(var r in t)o.call(t,r)&&e.push(r);return e};
},{"object-assign":"J4Nk","util/":"XOAc"}],"T2os":[function(require,module,exports) {
"use strict";var e,t="object"==typeof Reflect?Reflect:null,n=t&&"function"==typeof t.apply?t.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};function r(e){console&&console.warn&&console.warn(e)}e=t&&"function"==typeof t.ownKeys?t.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var i=Number.isNaN||function(e){return e!=e};function o(){o.init.call(this)}module.exports=o,module.exports.once=m,o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var s=10;function u(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function f(e){return void 0===e._maxListeners?o.defaultMaxListeners:e._maxListeners}function v(e,t,n,i){var o,s,v;if(u(n),void 0===(s=e._events)?(s=e._events=Object.create(null),e._eventsCount=0):(void 0!==s.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),s=e._events),v=s[t]),void 0===v)v=s[t]=n,++e._eventsCount;else if("function"==typeof v?v=s[t]=i?[n,v]:[v,n]:i?v.unshift(n):v.push(n),(o=f(e))>0&&v.length>o&&!v.warned){v.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+v.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=v.length,r(l)}return e}function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function c(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=l.bind(r);return i.listener=n,r.wrapFn=i,i}function a(e,t,n){var r=e._events;if(void 0===r)return[];var i=r[t];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?d(i):p(i,i.length)}function h(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function p(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function y(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function d(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function m(e,t){return new Promise(function(n,r){function i(){void 0!==o&&e.removeListener("error",o),n([].slice.call(arguments))}var o;"error"!==t&&(o=function(n){e.removeListener(t,i),r(n)},e.once("error",o)),e.once(t,i)})}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");s=e}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},o.prototype.getMaxListeners=function(){return f(this)},o.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var i="error"===e,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var u=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw u.context=s,u}var f=o[e];if(void 0===f)return!1;if("function"==typeof f)n(f,this,t);else{var v=f.length,l=p(f,v);for(r=0;r<v;++r)n(l[r],this,t)}return!0},o.prototype.addListener=function(e,t){return v(this,e,t,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(e,t){return v(this,e,t,!0)},o.prototype.once=function(e,t){return u(t),this.on(e,c(this,e,t)),this},o.prototype.prependOnceListener=function(e,t){return u(t),this.prependListener(e,c(this,e,t)),this},o.prototype.removeListener=function(e,t){var n,r,i,o,s;if(u(t),void 0===(r=this._events))return this;if(void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){s=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():y(n,i),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,s||t)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},o.prototype.listeners=function(e){return a(this,e,!0)},o.prototype.rawListeners=function(e){return a(this,e,!1)},o.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},o.prototype.listenerCount=h,o.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]};
},{}],"Bm0n":[function(require,module,exports) {
"function"==typeof Object.create?module.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:module.exports=function(t,e){if(e){t.super_=e;var o=function(){};o.prototype=e.prototype,t.prototype=new o,t.prototype.constructor=t}};
},{}],"Yj0v":[function(require,module,exports) {
var process = require("process");
var n=require("process");function e(e,r,t,c){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var i,l,u=arguments.length;switch(u){case 0:case 1:return n.nextTick(e);case 2:return n.nextTick(function(){e.call(null,r)});case 3:return n.nextTick(function(){e.call(null,r,t)});case 4:return n.nextTick(function(){e.call(null,r,t,c)});default:for(i=new Array(u-1),l=0;l<i.length;)i[l++]=arguments[l];return n.nextTick(function(){e.apply(null,i)})}}void 0===n||!n.version||0===n.version.indexOf("v0.")||0===n.version.indexOf("v1.")&&0!==n.version.indexOf("v1.8.")?module.exports={nextTick:e}:module.exports=n;
},{"process":"pBGv"}],"REa7":[function(require,module,exports) {
var r={}.toString;module.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)};
},{}],"ExO1":[function(require,module,exports) {
module.exports=require("events").EventEmitter;
},{"events":"T2os"}],"yh9p":[function(require,module,exports) {
"use strict";exports.byteLength=u,exports.toByteArray=i,exports.fromByteArray=d;for(var r=[],t=[],e="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=n.length;o<a;++o)r[o]=n[o],t[n.charCodeAt(o)]=o;function h(r){var t=r.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=r.indexOf("=");return-1===e&&(e=t),[e,e===t?0:4-e%4]}function u(r){var t=h(r),e=t[0],n=t[1];return 3*(e+n)/4-n}function c(r,t,e){return 3*(t+e)/4-e}function i(r){var n,o,a=h(r),u=a[0],i=a[1],f=new e(c(r,u,i)),A=0,d=i>0?u-4:u;for(o=0;o<d;o+=4)n=t[r.charCodeAt(o)]<<18|t[r.charCodeAt(o+1)]<<12|t[r.charCodeAt(o+2)]<<6|t[r.charCodeAt(o+3)],f[A++]=n>>16&255,f[A++]=n>>8&255,f[A++]=255&n;return 2===i&&(n=t[r.charCodeAt(o)]<<2|t[r.charCodeAt(o+1)]>>4,f[A++]=255&n),1===i&&(n=t[r.charCodeAt(o)]<<10|t[r.charCodeAt(o+1)]<<4|t[r.charCodeAt(o+2)]>>2,f[A++]=n>>8&255,f[A++]=255&n),f}function f(t){return r[t>>18&63]+r[t>>12&63]+r[t>>6&63]+r[63&t]}function A(r,t,e){for(var n,o=[],a=t;a<e;a+=3)n=(r[a]<<16&16711680)+(r[a+1]<<8&65280)+(255&r[a+2]),o.push(f(n));return o.join("")}function d(t){for(var e,n=t.length,o=n%3,a=[],h=0,u=n-o;h<u;h+=16383)a.push(A(t,h,h+16383>u?u:h+16383));return 1===o?(e=t[n-1],a.push(r[e>>2]+r[e<<4&63]+"==")):2===o&&(e=(t[n-2]<<8)+t[n-1],a.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),a.join("")}t["-".charCodeAt(0)]=62,t["_".charCodeAt(0)]=63;
},{}],"JgNJ":[function(require,module,exports) {
exports.read=function(a,o,t,r,h){var M,p,w=8*h-r-1,f=(1<<w)-1,e=f>>1,i=-7,N=t?h-1:0,n=t?-1:1,s=a[o+N];for(N+=n,M=s&(1<<-i)-1,s>>=-i,i+=w;i>0;M=256*M+a[o+N],N+=n,i-=8);for(p=M&(1<<-i)-1,M>>=-i,i+=r;i>0;p=256*p+a[o+N],N+=n,i-=8);if(0===M)M=1-e;else{if(M===f)return p?NaN:1/0*(s?-1:1);p+=Math.pow(2,r),M-=e}return(s?-1:1)*p*Math.pow(2,M-r)},exports.write=function(a,o,t,r,h,M){var p,w,f,e=8*M-h-1,i=(1<<e)-1,N=i>>1,n=23===h?Math.pow(2,-24)-Math.pow(2,-77):0,s=r?0:M-1,u=r?1:-1,l=o<0||0===o&&1/o<0?1:0;for(o=Math.abs(o),isNaN(o)||o===1/0?(w=isNaN(o)?1:0,p=i):(p=Math.floor(Math.log(o)/Math.LN2),o*(f=Math.pow(2,-p))<1&&(p--,f*=2),(o+=p+N>=1?n/f:n*Math.pow(2,1-N))*f>=2&&(p++,f/=2),p+N>=i?(w=0,p=i):p+N>=1?(w=(o*f-1)*Math.pow(2,h),p+=N):(w=o*Math.pow(2,N-1)*Math.pow(2,h),p=0));h>=8;a[t+s]=255&w,s+=u,w/=256,h-=8);for(p=p<<h|w,e+=h;e>0;a[t+s]=255&p,s+=u,p/=256,e-=8);a[t+s-u]|=128*l};
},{}],"dskh":[function(require,module,exports) {

var global = arguments[3];
var t=arguments[3],r=require("base64-js"),e=require("ieee754"),n=require("isarray");function i(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(r){return!1}}function o(){return f.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function u(t,r){if(o()<r)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(r)).__proto__=f.prototype:(null===t&&(t=new f(r)),t.length=r),t}function f(t,r,e){if(!(f.TYPED_ARRAY_SUPPORT||this instanceof f))return new f(t,r,e);if("number"==typeof t){if("string"==typeof r)throw new Error("If encoding is specified then the first argument must be a string");return c(this,t)}return s(this,t,r,e)}function s(t,r,e,n){if("number"==typeof r)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&r instanceof ArrayBuffer?g(t,r,e,n):"string"==typeof r?l(t,r,e):y(t,r)}function h(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function a(t,r,e,n){return h(r),r<=0?u(t,r):void 0!==e?"string"==typeof n?u(t,r).fill(e,n):u(t,r).fill(e):u(t,r)}function c(t,r){if(h(r),t=u(t,r<0?0:0|w(r)),!f.TYPED_ARRAY_SUPPORT)for(var e=0;e<r;++e)t[e]=0;return t}function l(t,r,e){if("string"==typeof e&&""!==e||(e="utf8"),!f.isEncoding(e))throw new TypeError('"encoding" must be a valid string encoding');var n=0|v(r,e),i=(t=u(t,n)).write(r,e);return i!==n&&(t=t.slice(0,i)),t}function p(t,r){var e=r.length<0?0:0|w(r.length);t=u(t,e);for(var n=0;n<e;n+=1)t[n]=255&r[n];return t}function g(t,r,e,n){if(r.byteLength,e<0||r.byteLength<e)throw new RangeError("'offset' is out of bounds");if(r.byteLength<e+(n||0))throw new RangeError("'length' is out of bounds");return r=void 0===e&&void 0===n?new Uint8Array(r):void 0===n?new Uint8Array(r,e):new Uint8Array(r,e,n),f.TYPED_ARRAY_SUPPORT?(t=r).__proto__=f.prototype:t=p(t,r),t}function y(t,r){if(f.isBuffer(r)){var e=0|w(r.length);return 0===(t=u(t,e)).length?t:(r.copy(t,0,0,e),t)}if(r){if("undefined"!=typeof ArrayBuffer&&r.buffer instanceof ArrayBuffer||"length"in r)return"number"!=typeof r.length||W(r.length)?u(t,0):p(t,r);if("Buffer"===r.type&&n(r.data))return p(t,r.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function w(t){if(t>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|t}function d(t){return+t!=t&&(t=0),f.alloc(+t)}function v(t,r){if(f.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var e=t.length;if(0===e)return 0;for(var n=!1;;)switch(r){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":case void 0:return $(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*e;case"hex":return e>>>1;case"base64":return K(t).length;default:if(n)return $(t).length;r=(""+r).toLowerCase(),n=!0}}function E(t,r,e){var n=!1;if((void 0===r||r<0)&&(r=0),r>this.length)return"";if((void 0===e||e>this.length)&&(e=this.length),e<=0)return"";if((e>>>=0)<=(r>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return x(this,r,e);case"utf8":case"utf-8":return Y(this,r,e);case"ascii":return L(this,r,e);case"latin1":case"binary":return D(this,r,e);case"base64":return S(this,r,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,r,e);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function b(t,r,e){var n=t[r];t[r]=t[e],t[e]=n}function R(t,r,e,n,i){if(0===t.length)return-1;if("string"==typeof e?(n=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),e=+e,isNaN(e)&&(e=i?0:t.length-1),e<0&&(e=t.length+e),e>=t.length){if(i)return-1;e=t.length-1}else if(e<0){if(!i)return-1;e=0}if("string"==typeof r&&(r=f.from(r,n)),f.isBuffer(r))return 0===r.length?-1:_(t,r,e,n,i);if("number"==typeof r)return r&=255,f.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,r,e):Uint8Array.prototype.lastIndexOf.call(t,r,e):_(t,[r],e,n,i);throw new TypeError("val must be string, number or Buffer")}function _(t,r,e,n,i){var o,u=1,f=t.length,s=r.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||r.length<2)return-1;u=2,f/=2,s/=2,e/=2}function h(t,r){return 1===u?t[r]:t.readUInt16BE(r*u)}if(i){var a=-1;for(o=e;o<f;o++)if(h(t,o)===h(r,-1===a?0:o-a)){if(-1===a&&(a=o),o-a+1===s)return a*u}else-1!==a&&(o-=o-a),a=-1}else for(e+s>f&&(e=f-s),o=e;o>=0;o--){for(var c=!0,l=0;l<s;l++)if(h(t,o+l)!==h(r,l)){c=!1;break}if(c)return o}return-1}function A(t,r,e,n){e=Number(e)||0;var i=t.length-e;n?(n=Number(n))>i&&(n=i):n=i;var o=r.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var u=0;u<n;++u){var f=parseInt(r.substr(2*u,2),16);if(isNaN(f))return u;t[e+u]=f}return u}function m(t,r,e,n){return Q($(r,t.length-e),t,e,n)}function P(t,r,e,n){return Q(G(r),t,e,n)}function T(t,r,e,n){return P(t,r,e,n)}function B(t,r,e,n){return Q(K(r),t,e,n)}function U(t,r,e,n){return Q(H(r,t.length-e),t,e,n)}function S(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function Y(t,r,e){e=Math.min(t.length,e);for(var n=[],i=r;i<e;){var o,u,f,s,h=t[i],a=null,c=h>239?4:h>223?3:h>191?2:1;if(i+c<=e)switch(c){case 1:h<128&&(a=h);break;case 2:128==(192&(o=t[i+1]))&&(s=(31&h)<<6|63&o)>127&&(a=s);break;case 3:o=t[i+1],u=t[i+2],128==(192&o)&&128==(192&u)&&(s=(15&h)<<12|(63&o)<<6|63&u)>2047&&(s<55296||s>57343)&&(a=s);break;case 4:o=t[i+1],u=t[i+2],f=t[i+3],128==(192&o)&&128==(192&u)&&128==(192&f)&&(s=(15&h)<<18|(63&o)<<12|(63&u)<<6|63&f)>65535&&s<1114112&&(a=s)}null===a?(a=65533,c=1):a>65535&&(a-=65536,n.push(a>>>10&1023|55296),a=56320|1023&a),n.push(a),i+=c}return O(n)}exports.Buffer=f,exports.SlowBuffer=d,exports.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:i(),exports.kMaxLength=o(),f.poolSize=8192,f._augment=function(t){return t.__proto__=f.prototype,t},f.from=function(t,r,e){return s(null,t,r,e)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(t,r,e){return a(null,t,r,e)},f.allocUnsafe=function(t){return c(null,t)},f.allocUnsafeSlow=function(t){return c(null,t)},f.isBuffer=function(t){return!(null==t||!t._isBuffer)},f.compare=function(t,r){if(!f.isBuffer(t)||!f.isBuffer(r))throw new TypeError("Arguments must be Buffers");if(t===r)return 0;for(var e=t.length,n=r.length,i=0,o=Math.min(e,n);i<o;++i)if(t[i]!==r[i]){e=t[i],n=r[i];break}return e<n?-1:n<e?1:0},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,r){if(!n(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f.alloc(0);var e;if(void 0===r)for(r=0,e=0;e<t.length;++e)r+=t[e].length;var i=f.allocUnsafe(r),o=0;for(e=0;e<t.length;++e){var u=t[e];if(!f.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(i,o),o+=u.length}return i},f.byteLength=v,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<t;r+=2)b(this,r,r+1);return this},f.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<t;r+=4)b(this,r,r+3),b(this,r+1,r+2);return this},f.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<t;r+=8)b(this,r,r+7),b(this,r+1,r+6),b(this,r+2,r+5),b(this,r+3,r+4);return this},f.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?Y(this,0,t):E.apply(this,arguments)},f.prototype.equals=function(t){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===f.compare(this,t)},f.prototype.inspect=function(){var t="",r=exports.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},f.prototype.compare=function(t,r,e,n,i){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===r&&(r=0),void 0===e&&(e=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),r<0||e>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&r>=e)return 0;if(n>=i)return-1;if(r>=e)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),u=(e>>>=0)-(r>>>=0),s=Math.min(o,u),h=this.slice(n,i),a=t.slice(r,e),c=0;c<s;++c)if(h[c]!==a[c]){o=h[c],u=a[c];break}return o<u?-1:u<o?1:0},f.prototype.includes=function(t,r,e){return-1!==this.indexOf(t,r,e)},f.prototype.indexOf=function(t,r,e){return R(this,t,r,e,!0)},f.prototype.lastIndexOf=function(t,r,e){return R(this,t,r,e,!1)},f.prototype.write=function(t,r,e,n){if(void 0===r)n="utf8",e=this.length,r=0;else if(void 0===e&&"string"==typeof r)n=r,e=this.length,r=0;else{if(!isFinite(r))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");r|=0,isFinite(e)?(e|=0,void 0===n&&(n="utf8")):(n=e,e=void 0)}var i=this.length-r;if((void 0===e||e>i)&&(e=i),t.length>0&&(e<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return A(this,t,r,e);case"utf8":case"utf-8":return m(this,t,r,e);case"ascii":return P(this,t,r,e);case"latin1":case"binary":return T(this,t,r,e);case"base64":return B(this,t,r,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,t,r,e);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var I=4096;function O(t){var r=t.length;if(r<=I)return String.fromCharCode.apply(String,t);for(var e="",n=0;n<r;)e+=String.fromCharCode.apply(String,t.slice(n,n+=I));return e}function L(t,r,e){var n="";e=Math.min(t.length,e);for(var i=r;i<e;++i)n+=String.fromCharCode(127&t[i]);return n}function D(t,r,e){var n="";e=Math.min(t.length,e);for(var i=r;i<e;++i)n+=String.fromCharCode(t[i]);return n}function x(t,r,e){var n=t.length;(!r||r<0)&&(r=0),(!e||e<0||e>n)&&(e=n);for(var i="",o=r;o<e;++o)i+=Z(t[o]);return i}function C(t,r,e){for(var n=t.slice(r,e),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function M(t,r,e){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+r>e)throw new RangeError("Trying to access beyond buffer length")}function k(t,r,e,n,i,o){if(!f.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(r>i||r<o)throw new RangeError('"value" argument is out of bounds');if(e+n>t.length)throw new RangeError("Index out of range")}function N(t,r,e,n){r<0&&(r=65535+r+1);for(var i=0,o=Math.min(t.length-e,2);i<o;++i)t[e+i]=(r&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function z(t,r,e,n){r<0&&(r=4294967295+r+1);for(var i=0,o=Math.min(t.length-e,4);i<o;++i)t[e+i]=r>>>8*(n?i:3-i)&255}function F(t,r,e,n,i,o){if(e+n>t.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function j(t,r,n,i,o){return o||F(t,r,n,4,3.4028234663852886e38,-3.4028234663852886e38),e.write(t,r,n,i,23,4),n+4}function q(t,r,n,i,o){return o||F(t,r,n,8,1.7976931348623157e308,-1.7976931348623157e308),e.write(t,r,n,i,52,8),n+8}f.prototype.slice=function(t,r){var e,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(r=void 0===r?n:~~r)<0?(r+=n)<0&&(r=0):r>n&&(r=n),r<t&&(r=t),f.TYPED_ARRAY_SUPPORT)(e=this.subarray(t,r)).__proto__=f.prototype;else{var i=r-t;e=new f(i,void 0);for(var o=0;o<i;++o)e[o]=this[o+t]}return e},f.prototype.readUIntLE=function(t,r,e){t|=0,r|=0,e||M(t,r,this.length);for(var n=this[t],i=1,o=0;++o<r&&(i*=256);)n+=this[t+o]*i;return n},f.prototype.readUIntBE=function(t,r,e){t|=0,r|=0,e||M(t,r,this.length);for(var n=this[t+--r],i=1;r>0&&(i*=256);)n+=this[t+--r]*i;return n},f.prototype.readUInt8=function(t,r){return r||M(t,1,this.length),this[t]},f.prototype.readUInt16LE=function(t,r){return r||M(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUInt16BE=function(t,r){return r||M(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUInt32LE=function(t,r){return r||M(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},f.prototype.readUInt32BE=function(t,r){return r||M(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readIntLE=function(t,r,e){t|=0,r|=0,e||M(t,r,this.length);for(var n=this[t],i=1,o=0;++o<r&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*r)),n},f.prototype.readIntBE=function(t,r,e){t|=0,r|=0,e||M(t,r,this.length);for(var n=r,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*r)),o},f.prototype.readInt8=function(t,r){return r||M(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},f.prototype.readInt16LE=function(t,r){r||M(t,2,this.length);var e=this[t]|this[t+1]<<8;return 32768&e?4294901760|e:e},f.prototype.readInt16BE=function(t,r){r||M(t,2,this.length);var e=this[t+1]|this[t]<<8;return 32768&e?4294901760|e:e},f.prototype.readInt32LE=function(t,r){return r||M(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,r){return r||M(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readFloatLE=function(t,r){return r||M(t,4,this.length),e.read(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,r){return r||M(t,4,this.length),e.read(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,r){return r||M(t,8,this.length),e.read(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,r){return r||M(t,8,this.length),e.read(this,t,!1,52,8)},f.prototype.writeUIntLE=function(t,r,e,n){(t=+t,r|=0,e|=0,n)||k(this,t,r,e,Math.pow(2,8*e)-1,0);var i=1,o=0;for(this[r]=255&t;++o<e&&(i*=256);)this[r+o]=t/i&255;return r+e},f.prototype.writeUIntBE=function(t,r,e,n){(t=+t,r|=0,e|=0,n)||k(this,t,r,e,Math.pow(2,8*e)-1,0);var i=e-1,o=1;for(this[r+i]=255&t;--i>=0&&(o*=256);)this[r+i]=t/o&255;return r+e},f.prototype.writeUInt8=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,1,255,0),f.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[r]=255&t,r+1},f.prototype.writeUInt16LE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):N(this,t,r,!0),r+2},f.prototype.writeUInt16BE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):N(this,t,r,!1),r+2},f.prototype.writeUInt32LE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t):z(this,t,r,!0),r+4},f.prototype.writeUInt32BE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):z(this,t,r,!1),r+4},f.prototype.writeIntLE=function(t,r,e,n){if(t=+t,r|=0,!n){var i=Math.pow(2,8*e-1);k(this,t,r,e,i-1,-i)}var o=0,u=1,f=0;for(this[r]=255&t;++o<e&&(u*=256);)t<0&&0===f&&0!==this[r+o-1]&&(f=1),this[r+o]=(t/u>>0)-f&255;return r+e},f.prototype.writeIntBE=function(t,r,e,n){if(t=+t,r|=0,!n){var i=Math.pow(2,8*e-1);k(this,t,r,e,i-1,-i)}var o=e-1,u=1,f=0;for(this[r+o]=255&t;--o>=0&&(u*=256);)t<0&&0===f&&0!==this[r+o+1]&&(f=1),this[r+o]=(t/u>>0)-f&255;return r+e},f.prototype.writeInt8=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,1,127,-128),f.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[r]=255&t,r+1},f.prototype.writeInt16LE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):N(this,t,r,!0),r+2},f.prototype.writeInt16BE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):N(this,t,r,!1),r+2},f.prototype.writeInt32LE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,4,2147483647,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24):z(this,t,r,!0),r+4},f.prototype.writeInt32BE=function(t,r,e){return t=+t,r|=0,e||k(this,t,r,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),f.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):z(this,t,r,!1),r+4},f.prototype.writeFloatLE=function(t,r,e){return j(this,t,r,!0,e)},f.prototype.writeFloatBE=function(t,r,e){return j(this,t,r,!1,e)},f.prototype.writeDoubleLE=function(t,r,e){return q(this,t,r,!0,e)},f.prototype.writeDoubleBE=function(t,r,e){return q(this,t,r,!1,e)},f.prototype.copy=function(t,r,e,n){if(e||(e=0),n||0===n||(n=this.length),r>=t.length&&(r=t.length),r||(r=0),n>0&&n<e&&(n=e),n===e)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(e<0||e>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-r<n-e&&(n=t.length-r+e);var i,o=n-e;if(this===t&&e<r&&r<n)for(i=o-1;i>=0;--i)t[i+r]=this[i+e];else if(o<1e3||!f.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+r]=this[i+e];else Uint8Array.prototype.set.call(t,this.subarray(e,e+o),r);return o},f.prototype.fill=function(t,r,e,n){if("string"==typeof t){if("string"==typeof r?(n=r,r=0,e=this.length):"string"==typeof e&&(n=e,e=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!f.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof t&&(t&=255);if(r<0||this.length<r||this.length<e)throw new RangeError("Out of range index");if(e<=r)return this;var o;if(r>>>=0,e=void 0===e?this.length:e>>>0,t||(t=0),"number"==typeof t)for(o=r;o<e;++o)this[o]=t;else{var u=f.isBuffer(t)?t:$(new f(t,n).toString()),s=u.length;for(o=0;o<e-r;++o)this[o+r]=u[o%s]}return this};var V=/[^+\/0-9A-Za-z-_]/g;function X(t){if((t=J(t).replace(V,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}function J(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function Z(t){return t<16?"0"+t.toString(16):t.toString(16)}function $(t,r){var e;r=r||1/0;for(var n=t.length,i=null,o=[],u=0;u<n;++u){if((e=t.charCodeAt(u))>55295&&e<57344){if(!i){if(e>56319){(r-=3)>-1&&o.push(239,191,189);continue}if(u+1===n){(r-=3)>-1&&o.push(239,191,189);continue}i=e;continue}if(e<56320){(r-=3)>-1&&o.push(239,191,189),i=e;continue}e=65536+(i-55296<<10|e-56320)}else i&&(r-=3)>-1&&o.push(239,191,189);if(i=null,e<128){if((r-=1)<0)break;o.push(e)}else if(e<2048){if((r-=2)<0)break;o.push(e>>6|192,63&e|128)}else if(e<65536){if((r-=3)<0)break;o.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error("Invalid code point");if((r-=4)<0)break;o.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return o}function G(t){for(var r=[],e=0;e<t.length;++e)r.push(255&t.charCodeAt(e));return r}function H(t,r){for(var e,n,i,o=[],u=0;u<t.length&&!((r-=2)<0);++u)n=(e=t.charCodeAt(u))>>8,i=e%256,o.push(i),o.push(n);return o}function K(t){return r.toByteArray(X(t))}function Q(t,r,e,n){for(var i=0;i<n&&!(i+e>=r.length||i>=t.length);++i)r[i+e]=t[i];return i}function W(t){return t!=t}
},{"base64-js":"yh9p","ieee754":"JgNJ","isarray":"REa7","buffer":"dskh"}],"ZoTc":[function(require,module,exports) {

var r=require("buffer"),e=r.Buffer;function n(r,e){for(var n in r)e[n]=r[n]}function o(r,n,o){return e(r,n,o)}e.from&&e.alloc&&e.allocUnsafe&&e.allocUnsafeSlow?module.exports=r:(n(r,exports),exports.Buffer=o),n(e,o),o.from=function(r,n,o){if("number"==typeof r)throw new TypeError("Argument must not be a number");return e(r,n,o)},o.alloc=function(r,n,o){if("number"!=typeof r)throw new TypeError("Argument must be a number");var f=e(r);return void 0!==n?"string"==typeof o?f.fill(n,o):f.fill(n):f.fill(0),f},o.allocUnsafe=function(r){if("number"!=typeof r)throw new TypeError("Argument must be a number");return e(r)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)};
},{"buffer":"dskh"}],"Q14w":[function(require,module,exports) {
var Buffer = require("buffer").Buffer;
var r=require("buffer").Buffer;function t(r){return Array.isArray?Array.isArray(r):"[object Array]"===a(r)}function e(r){return"boolean"==typeof r}function n(r){return null===r}function o(r){return null==r}function i(r){return"number"==typeof r}function u(r){return"string"==typeof r}function s(r){return"symbol"==typeof r}function f(r){return void 0===r}function p(r){return"[object RegExp]"===a(r)}function c(r){return"object"==typeof r&&null!==r}function l(r){return"[object Date]"===a(r)}function y(r){return"[object Error]"===a(r)||r instanceof Error}function x(r){return"function"==typeof r}function b(r){return null===r||"boolean"==typeof r||"number"==typeof r||"string"==typeof r||"symbol"==typeof r||void 0===r}function a(r){return Object.prototype.toString.call(r)}exports.isArray=t,exports.isBoolean=e,exports.isNull=n,exports.isNullOrUndefined=o,exports.isNumber=i,exports.isString=u,exports.isSymbol=s,exports.isUndefined=f,exports.isRegExp=p,exports.isObject=c,exports.isDate=l,exports.isError=y,exports.isFunction=x,exports.isPrimitive=b,exports.isBuffer=r.isBuffer;
},{"buffer":"dskh"}],"rDCW":[function(require,module,exports) {

},{}],"wlMe":[function(require,module,exports) {

"use strict";function t(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}var n=require("safe-buffer").Buffer,e=require("util");function i(t,n,e){t.copy(n,e)}module.exports=function(){function e(){t(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(t){var n={data:t,next:null};this.length>0?this.tail.next=n:this.head=n,this.tail=n,++this.length},e.prototype.unshift=function(t){var n={data:t,next:this.head};0===this.length&&(this.tail=n),this.head=n,++this.length},e.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(t){if(0===this.length)return"";for(var n=this.head,e=""+n.data;n=n.next;)e+=t+n.data;return e},e.prototype.concat=function(t){if(0===this.length)return n.alloc(0);if(1===this.length)return this.head.data;for(var e=n.allocUnsafe(t>>>0),h=this.head,a=0;h;)i(h.data,e,a),a+=h.data.length,h=h.next;return e},e}(),e&&e.inspect&&e.inspect.custom&&(module.exports.prototype[e.inspect.custom]=function(){var t=e.inspect({length:this.length});return this.constructor.name+" "+t});
},{"safe-buffer":"ZoTc","util":"rDCW"}],"GRUB":[function(require,module,exports) {
"use strict";var t=require("process-nextick-args");function e(e,a){var r=this,s=this._readableState&&this._readableState.destroyed,d=this._writableState&&this._writableState.destroyed;return s||d?(a?a(e):!e||this._writableState&&this._writableState.errorEmitted||t.nextTick(i,this,e),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!a&&e?(t.nextTick(i,r,e),r._writableState&&(r._writableState.errorEmitted=!0)):a&&a(e)}),this)}function a(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function i(t,e){t.emit("error",e)}module.exports={destroy:e,undestroy:a};
},{"process-nextick-args":"Yj0v"}],"yM1o":[function(require,module,exports) {
var global = arguments[3];
var r=arguments[3];function t(r,t){if(e("noDeprecation"))return r;var n=!1;return function(){if(!n){if(e("throwDeprecation"))throw new Error(t);e("traceDeprecation")?console.trace(t):console.warn(t),n=!0}return r.apply(this,arguments)}}function e(t){try{if(!r.localStorage)return!1}catch(n){return!1}var e=r.localStorage[t];return null!=e&&"true"===String(e).toLowerCase()}module.exports=t;
},{}],"WSyY":[function(require,module,exports) {
var process = require("process");

var global = arguments[3];
var e=require("process"),t=arguments[3],n=require("process-nextick-args");function r(e,t,n){this.chunk=e,this.encoding=t,this.callback=n,this.next=null}function i(e){var t=this;this.next=null,this.entry=null,this.finish=function(){W(t,e)}}module.exports=g;var o,s=n.nextTick;g.WritableState=y;var f=Object.create(require("core-util-is"));f.inherits=require("inherits");var u={deprecate:require("util-deprecate")},a=require("./internal/streams/stream"),c=require("safe-buffer").Buffer,l=t.Uint8Array||function(){};function d(e){return c.from(e)}function h(e){return c.isBuffer(e)||e instanceof l}var b,p=require("./internal/streams/destroy");function w(){}function y(e,t){o=o||require("./_stream_duplex"),e=e||{};var n=t instanceof o;this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var r=e.highWaterMark,s=e.writableHighWaterMark,f=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:n&&(s||0===s)?s:f,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var u=!1===e.decodeStrings;this.decodeStrings=!u,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){S(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function g(e){if(o=o||require("./_stream_duplex"),!(b.call(g,this)||this instanceof o))return new g(e);this._writableState=new y(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),a.call(this)}function k(e,t){var r=new Error("write after end");e.emit("error",r),n.nextTick(t,r)}function v(e,t,r,i){var o=!0,s=!1;return null===r?s=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||t.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(e.emit("error",s),n.nextTick(i,s),o=!1),o}function q(e,t,n){return e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=c.from(t,n)),t}function _(e,t,n,r,i,o){if(!n){var s=q(t,r,i);r!==s&&(n=!0,i="buffer",r=s)}var f=t.objectMode?1:r.length;t.length+=f;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var a=t.lastBufferedRequest;t.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:o,next:null},a?a.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else m(e,t,!1,f,r,i,o);return u}function m(e,t,n,r,i,o,s){t.writelen=r,t.writecb=s,t.writing=!0,t.sync=!0,n?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function R(e,t,r,i,o){--t.pendingcb,r?(n.nextTick(o,i),n.nextTick(T,e,t),e._writableState.errorEmitted=!0,e.emit("error",i)):(o(i),e._writableState.errorEmitted=!0,e.emit("error",i),T(e,t))}function x(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function S(e,t){var n=e._writableState,r=n.sync,i=n.writecb;if(x(n),t)R(e,n,r,t,i);else{var o=E(n);o||n.corked||n.bufferProcessing||!n.bufferedRequest||B(e,n),r?s(M,e,n,o,i):M(e,n,o,i)}}function M(e,t,n,r){n||j(e,t),t.pendingcb--,r(),T(e,t)}function j(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function B(e,t){t.bufferProcessing=!0;var n=t.bufferedRequest;if(e._writev&&n&&n.next){var r=t.bufferedRequestCount,o=new Array(r),s=t.corkedRequestsFree;s.entry=n;for(var f=0,u=!0;n;)o[f]=n,n.isBuf||(u=!1),n=n.next,f+=1;o.allBuffers=u,m(e,t,!0,t.length,o,"",s.finish),t.pendingcb++,t.lastBufferedRequest=null,s.next?(t.corkedRequestsFree=s.next,s.next=null):t.corkedRequestsFree=new i(t),t.bufferedRequestCount=0}else{for(;n;){var a=n.chunk,c=n.encoding,l=n.callback;if(m(e,t,!1,t.objectMode?1:a.length,a,c,l),n=n.next,t.bufferedRequestCount--,t.writing)break}null===n&&(t.lastBufferedRequest=null)}t.bufferedRequest=n,t.bufferProcessing=!1}function E(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function C(e,t){e._final(function(n){t.pendingcb--,n&&e.emit("error",n),t.prefinished=!0,e.emit("prefinish"),T(e,t)})}function P(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,n.nextTick(C,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function T(e,t){var n=E(t);return n&&(P(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),n}function F(e,t,r){t.ending=!0,T(e,t),r&&(t.finished?n.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function W(e,t,n){var r=e.entry;for(e.entry=null;r;){var i=r.callback;t.pendingcb--,i(n),r=r.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e}f.inherits(g,a),y.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(y.prototype,"buffer",{get:u.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(b=Function.prototype[Symbol.hasInstance],Object.defineProperty(g,Symbol.hasInstance,{value:function(e){return!!b.call(this,e)||this===g&&(e&&e._writableState instanceof y)}})):b=function(e){return e instanceof this},g.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},g.prototype.write=function(e,t,n){var r=this._writableState,i=!1,o=!r.objectMode&&h(e);return o&&!c.isBuffer(e)&&(e=d(e)),"function"==typeof t&&(n=t,t=null),o?t="buffer":t||(t=r.defaultEncoding),"function"!=typeof n&&(n=w),r.ended?k(this,n):(o||v(this,r,e,n))&&(r.pendingcb++,i=_(this,r,o,e,t,n)),i},g.prototype.cork=function(){this._writableState.corked++},g.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||B(this,e))},g.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(g.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),g.prototype._write=function(e,t,n){n(new Error("_write() is not implemented"))},g.prototype._writev=null,g.prototype.end=function(e,t,n){var r=this._writableState;"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!=e&&this.write(e,t),r.corked&&(r.corked=1,this.uncork()),r.ending||r.finished||F(this,r,n)},Object.defineProperty(g.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),g.prototype.destroy=p.destroy,g.prototype._undestroy=p.undestroy,g.prototype._destroy=function(e,t){this.end(),t(e)};
},{"process-nextick-args":"Yj0v","core-util-is":"Q14w","inherits":"Bm0n","util-deprecate":"yM1o","./internal/streams/stream":"ExO1","safe-buffer":"ZoTc","./internal/streams/destroy":"GRUB","./_stream_duplex":"Hba0","process":"pBGv"}],"Hba0":[function(require,module,exports) {
"use strict";var e=require("process-nextick-args"),t=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};module.exports=l;var r=Object.create(require("core-util-is"));r.inherits=require("inherits");var i=require("./_stream_readable"),a=require("./_stream_writable");r.inherits(l,i);for(var o=t(a.prototype),s=0;s<o.length;s++){var n=o[s];l.prototype[n]||(l.prototype[n]=a.prototype[n])}function l(e){if(!(this instanceof l))return new l(e);i.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",h)}function h(){this.allowHalfOpen||this._writableState.ended||e.nextTick(d,this)}function d(e){e.end()}Object.defineProperty(l.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(l.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),l.prototype._destroy=function(t,r){this.push(null),this.end(),e.nextTick(r,t)};
},{"process-nextick-args":"Yj0v","core-util-is":"Q14w","inherits":"Bm0n","./_stream_readable":"DHrQ","./_stream_writable":"WSyY"}],"BUbk":[function(require,module,exports) {

"use strict";var t=require("safe-buffer").Buffer,e=t.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function s(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}function i(i){var a=s(i);if("string"!=typeof a&&(t.isEncoding===e||!e(i)))throw new Error("Unknown encoding: "+i);return a||i}function a(e){var s;switch(this.encoding=i(e),this.encoding){case"utf16le":this.text=c,this.end=f,s=4;break;case"utf8":this.fillLast=l,s=4;break;case"base64":this.text=d,this.end=g,s=3;break;default:return this.write=N,void(this.end=v)}this.lastNeed=0,this.lastTotal=0,this.lastChar=t.allocUnsafe(s)}function r(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function n(t,e,s){var i=e.length-1;if(i<s)return 0;var a=r(e[i]);return a>=0?(a>0&&(t.lastNeed=a-1),a):--i<s||-2===a?0:(a=r(e[i]))>=0?(a>0&&(t.lastNeed=a-2),a):--i<s||-2===a?0:(a=r(e[i]))>=0?(a>0&&(2===a?a=0:t.lastNeed=a-3),a):0}function h(t,e,s){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}function l(t){var e=this.lastTotal-this.lastNeed,s=h(this,t,e);return void 0!==s?s:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function u(t,e){var s=n(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=s;var i=t.length-(s-this.lastNeed);return t.copy(this.lastChar,0,i),t.toString("utf8",e,i)}function o(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e}function c(t,e){if((t.length-e)%2==0){var s=t.toString("utf16le",e);if(s){var i=s.charCodeAt(s.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],s.slice(0,-1)}return s}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function f(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var s=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,s)}return e}function d(t,e){var s=(t.length-e)%3;return 0===s?t.toString("base64",e):(this.lastNeed=3-s,this.lastTotal=3,1===s?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-s))}function g(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function N(t){return t.toString(this.encoding)}function v(t){return t&&t.length?this.write(t):""}exports.StringDecoder=a,a.prototype.write=function(t){if(0===t.length)return"";var e,s;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";s=this.lastNeed,this.lastNeed=0}else s=0;return s<t.length?e?e+this.text(t,s):this.text(t,s):e||""},a.prototype.end=o,a.prototype.text=u,a.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length};
},{"safe-buffer":"ZoTc"}],"DHrQ":[function(require,module,exports) {

var global = arguments[3];
var process = require("process");
var e=arguments[3],t=require("process"),n=require("process-nextick-args");module.exports=_;var r,i=require("isarray");_.ReadableState=w;var a=require("events").EventEmitter,d=function(e,t){return e.listeners(t).length},o=require("./internal/streams/stream"),s=require("safe-buffer").Buffer,u=e.Uint8Array||function(){};function l(e){return s.from(e)}function h(e){return s.isBuffer(e)||e instanceof u}var p=Object.create(require("core-util-is"));p.inherits=require("inherits");var f=require("util"),c=void 0;c=f&&f.debuglog?f.debuglog("stream"):function(){};var g,b=require("./internal/streams/BufferList"),m=require("./internal/streams/destroy");p.inherits(_,o);var v=["error","close","destroy","pause","resume"];function y(e,t,n){if("function"==typeof e.prependListener)return e.prependListener(t,n);e._events&&e._events[t]?i(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n)}function w(e,t){e=e||{};var n=t instanceof(r=r||require("./_stream_duplex"));this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var i=e.highWaterMark,a=e.readableHighWaterMark,d=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(a||0===a)?a:d,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(g||(g=require("string_decoder/").StringDecoder),this.decoder=new g(e.encoding),this.encoding=e.encoding)}function _(e){if(r=r||require("./_stream_duplex"),!(this instanceof _))return new _(e);this._readableState=new w(e,this),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),o.call(this)}function M(e,t,n,r,i){var a,d=e._readableState;null===t?(d.reading=!1,x(e,d)):(i||(a=k(d,t)),a?e.emit("error",a):d.objectMode||t&&t.length>0?("string"==typeof t||d.objectMode||Object.getPrototypeOf(t)===s.prototype||(t=l(t)),r?d.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):S(e,d,t,!0):d.ended?e.emit("error",new Error("stream.push() after EOF")):(d.reading=!1,d.decoder&&!n?(t=d.decoder.write(t),d.objectMode||0!==t.length?S(e,d,t,!1):C(e,d)):S(e,d,t,!1))):r||(d.reading=!1));return j(d)}function S(e,t,n,r){t.flowing&&0===t.length&&!t.sync?(e.emit("data",n),e.read(0)):(t.length+=t.objectMode?1:n.length,r?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&q(e)),C(e,t)}function k(e,t){var n;return h(t)||"string"==typeof t||void 0===t||e.objectMode||(n=new TypeError("Invalid non-string/buffer chunk")),n}function j(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}Object.defineProperty(_.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),_.prototype.destroy=m.destroy,_.prototype._undestroy=m.undestroy,_.prototype._destroy=function(e,t){this.push(null),t(e)},_.prototype.push=function(e,t){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof e&&((t=t||r.defaultEncoding)!==r.encoding&&(e=s.from(e,t),t=""),n=!0),M(this,e,t,!1,n)},_.prototype.unshift=function(e){return M(this,e,null,!0,!1)},_.prototype.isPaused=function(){return!1===this._readableState.flowing},_.prototype.setEncoding=function(e){return g||(g=require("string_decoder/").StringDecoder),this._readableState.decoder=new g(e),this._readableState.encoding=e,this};var R=8388608;function E(e){return e>=R?e=R:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function L(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=E(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function x(e,t){if(!t.ended){if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,q(e)}}function q(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(c("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?n.nextTick(W,e):W(e))}function W(e){c("emit readable"),e.emit("readable"),B(e)}function C(e,t){t.readingMore||(t.readingMore=!0,n.nextTick(D,e,t))}function D(e,t){for(var n=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(c("maybeReadMore read 0"),e.read(0),n!==t.length);)n=t.length;t.readingMore=!1}function O(e){return function(){var t=e._readableState;c("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&d(e,"data")&&(t.flowing=!0,B(e))}}function T(e){c("readable nexttick read 0"),e.read(0)}function U(e,t){t.resumeScheduled||(t.resumeScheduled=!0,n.nextTick(P,e,t))}function P(e,t){t.reading||(c("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),B(e),t.flowing&&!t.reading&&e.read(0)}function B(e){var t=e._readableState;for(c("flow",t.flowing);t.flowing&&null!==e.read(););}function H(e,t){return 0===t.length?null:(t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):n=I(e,t.buffer,t.decoder),n);var n}function I(e,t,n){var r;return e<t.head.data.length?(r=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):r=e===t.head.data.length?t.shift():n?A(e,t):F(e,t),r}function A(e,t){var n=t.head,r=1,i=n.data;for(e-=i.length;n=n.next;){var a=n.data,d=e>a.length?a.length:e;if(d===a.length?i+=a:i+=a.slice(0,e),0===(e-=d)){d===a.length?(++r,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=a.slice(d));break}++r}return t.length-=r,i}function F(e,t){var n=s.allocUnsafe(e),r=t.head,i=1;for(r.data.copy(n),e-=r.data.length;r=r.next;){var a=r.data,d=e>a.length?a.length:e;if(a.copy(n,n.length-e,0,d),0===(e-=d)){d===a.length?(++i,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=a.slice(d));break}++i}return t.length-=i,n}function z(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,n.nextTick(G,t,e))}function G(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function J(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}_.prototype.read=function(e){c("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return c("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?z(this):q(this),null;if(0===(e=L(e,t))&&t.ended)return 0===t.length&&z(this),null;var r,i=t.needReadable;return c("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&c("length less than watermark",i=!0),t.ended||t.reading?c("reading or ended",i=!1):i&&(c("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=L(n,t))),null===(r=e>0?H(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&z(this)),null!==r&&this.emit("data",r),r},_.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},_.prototype.pipe=function(e,r){var i=this,a=this._readableState;switch(a.pipesCount){case 0:a.pipes=e;break;case 1:a.pipes=[a.pipes,e];break;default:a.pipes.push(e)}a.pipesCount+=1,c("pipe count=%d opts=%j",a.pipesCount,r);var o=(!r||!1!==r.end)&&e!==t.stdout&&e!==t.stderr?u:v;function s(t,n){c("onunpipe"),t===i&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,c("cleanup"),e.removeListener("close",b),e.removeListener("finish",m),e.removeListener("drain",l),e.removeListener("error",g),e.removeListener("unpipe",s),i.removeListener("end",u),i.removeListener("end",v),i.removeListener("data",f),h=!0,!a.awaitDrain||e._writableState&&!e._writableState.needDrain||l())}function u(){c("onend"),e.end()}a.endEmitted?n.nextTick(o):i.once("end",o),e.on("unpipe",s);var l=O(i);e.on("drain",l);var h=!1;var p=!1;function f(t){c("ondata"),p=!1,!1!==e.write(t)||p||((1===a.pipesCount&&a.pipes===e||a.pipesCount>1&&-1!==J(a.pipes,e))&&!h&&(c("false write response, pause",i._readableState.awaitDrain),i._readableState.awaitDrain++,p=!0),i.pause())}function g(t){c("onerror",t),v(),e.removeListener("error",g),0===d(e,"error")&&e.emit("error",t)}function b(){e.removeListener("finish",m),v()}function m(){c("onfinish"),e.removeListener("close",b),v()}function v(){c("unpipe"),i.unpipe(e)}return i.on("data",f),y(e,"error",g),e.once("close",b),e.once("finish",m),e.emit("pipe",i),a.flowing||(c("pipe resume"),i.resume()),e},_.prototype.unpipe=function(e){var t=this._readableState,n={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,n),this);if(!e){var r=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var a=0;a<i;a++)r[a].emit("unpipe",this,n);return this}var d=J(t.pipes,e);return-1===d?this:(t.pipes.splice(d,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,n),this)},_.prototype.on=function(e,t){var r=o.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var i=this._readableState;i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.emittedReadable=!1,i.reading?i.length&&q(this):n.nextTick(T,this))}return r},_.prototype.addListener=_.prototype.on,_.prototype.resume=function(){var e=this._readableState;return e.flowing||(c("resume"),e.flowing=!0,U(this,e)),this},_.prototype.pause=function(){return c("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(c("pause"),this._readableState.flowing=!1,this.emit("pause")),this},_.prototype.wrap=function(e){var t=this,n=this._readableState,r=!1;for(var i in e.on("end",function(){if(c("wrapped end"),n.decoder&&!n.ended){var e=n.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(i){(c("wrapped data"),n.decoder&&(i=n.decoder.write(i)),n.objectMode&&null==i)||(n.objectMode||i&&i.length)&&(t.push(i)||(r=!0,e.pause()))}),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var a=0;a<v.length;a++)e.on(v[a],this.emit.bind(this,v[a]));return this._read=function(t){c("wrapped _read",t),r&&(r=!1,e.resume())},this},Object.defineProperty(_.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),_._fromList=H;
},{"process-nextick-args":"Yj0v","isarray":"REa7","events":"T2os","./internal/streams/stream":"ExO1","safe-buffer":"ZoTc","core-util-is":"Q14w","inherits":"Bm0n","util":"rDCW","./internal/streams/BufferList":"wlMe","./internal/streams/destroy":"GRUB","./_stream_duplex":"Hba0","string_decoder/":"BUbk","process":"pBGv"}],"tlBz":[function(require,module,exports) {
"use strict";module.exports=n;var t=require("./_stream_duplex"),r=Object.create(require("core-util-is"));function e(t,r){var e=this._transformState;e.transforming=!1;var n=e.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));e.writechunk=null,e.writecb=null,null!=r&&this.push(r),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function n(r){if(!(this instanceof n))return new n(r);t.call(this,r),this._transformState={afterTransform:e.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,r&&("function"==typeof r.transform&&(this._transform=r.transform),"function"==typeof r.flush&&(this._flush=r.flush)),this.on("prefinish",i)}function i(){var t=this;"function"==typeof this._flush?this._flush(function(r,e){a(t,r,e)}):a(this,null,null)}function a(t,r,e){if(r)return t.emit("error",r);if(null!=e&&t.push(e),t._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(t._transformState.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}r.inherits=require("inherits"),r.inherits(n,t),n.prototype.push=function(r,e){return this._transformState.needTransform=!1,t.prototype.push.call(this,r,e)},n.prototype._transform=function(t,r,e){throw new Error("_transform() is not implemented")},n.prototype._write=function(t,r,e){var n=this._transformState;if(n.writecb=e,n.writechunk=t,n.writeencoding=r,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},n.prototype._read=function(t){var r=this._transformState;null!==r.writechunk&&r.writecb&&!r.transforming?(r.transforming=!0,this._transform(r.writechunk,r.writeencoding,r.afterTransform)):r.needTransform=!0},n.prototype._destroy=function(r,e){var n=this;t.prototype._destroy.call(this,r,function(t){e(t),n.emit("close")})};
},{"./_stream_duplex":"Hba0","core-util-is":"Q14w","inherits":"Bm0n"}],"nwyA":[function(require,module,exports) {
"use strict";module.exports=t;var r=require("./_stream_transform"),e=Object.create(require("core-util-is"));function t(e){if(!(this instanceof t))return new t(e);r.call(this,e)}e.inherits=require("inherits"),e.inherits(t,r),t.prototype._transform=function(r,e,t){t(null,r)};
},{"./_stream_transform":"tlBz","core-util-is":"Q14w","inherits":"Bm0n"}],"tzeh":[function(require,module,exports) {
exports=module.exports=require("./lib/_stream_readable.js"),exports.Stream=exports,exports.Readable=exports,exports.Writable=require("./lib/_stream_writable.js"),exports.Duplex=require("./lib/_stream_duplex.js"),exports.Transform=require("./lib/_stream_transform.js"),exports.PassThrough=require("./lib/_stream_passthrough.js");
},{"./lib/_stream_readable.js":"DHrQ","./lib/_stream_writable.js":"WSyY","./lib/_stream_duplex.js":"Hba0","./lib/_stream_transform.js":"tlBz","./lib/_stream_passthrough.js":"nwyA"}],"LnjZ":[function(require,module,exports) {
module.exports=require("./lib/_stream_writable.js");
},{"./lib/_stream_writable.js":"WSyY"}],"kT1X":[function(require,module,exports) {
module.exports=require("./lib/_stream_duplex.js");
},{"./lib/_stream_duplex.js":"Hba0"}],"A9KG":[function(require,module,exports) {
module.exports=require("./readable").Transform;
},{"./readable":"tzeh"}],"C6nS":[function(require,module,exports) {
module.exports=require("./readable").PassThrough;
},{"./readable":"tzeh"}],"fnRj":[function(require,module,exports) {
module.exports=n;var e=require("events").EventEmitter,r=require("inherits");function n(){e.call(this)}r(n,e),n.Readable=require("readable-stream/readable.js"),n.Writable=require("readable-stream/writable.js"),n.Duplex=require("readable-stream/duplex.js"),n.Transform=require("readable-stream/transform.js"),n.PassThrough=require("readable-stream/passthrough.js"),n.Stream=n,n.prototype.pipe=function(r,n){var o=this;function t(e){r.writable&&!1===r.write(e)&&o.pause&&o.pause()}function s(){o.readable&&o.resume&&o.resume()}o.on("data",t),r.on("drain",s),r._isStdio||n&&!1===n.end||(o.on("end",a),o.on("close",u));var i=!1;function a(){i||(i=!0,r.end())}function u(){i||(i=!0,"function"==typeof r.destroy&&r.destroy())}function d(r){if(l(),0===e.listenerCount(this,"error"))throw r}function l(){o.removeListener("data",t),r.removeListener("drain",s),o.removeListener("end",a),o.removeListener("close",u),o.removeListener("error",d),r.removeListener("error",d),o.removeListener("end",l),o.removeListener("close",l),r.removeListener("close",l)}return o.on("error",d),r.on("error",d),o.on("end",l),o.on("close",l),r.on("close",l),r.emit("pipe",o),r};
},{"events":"T2os","inherits":"Bm0n","readable-stream/readable.js":"tzeh","readable-stream/writable.js":"LnjZ","readable-stream/duplex.js":"kT1X","readable-stream/transform.js":"A9KG","readable-stream/passthrough.js":"C6nS"}],"gfUn":[function(require,module,exports) {
var process = require("process");
var e=require("process"),t=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++)r[t[n]]=Object.getOwnPropertyDescriptor(e,t[n]);return r},r=/%[sdj%]/g;exports.format=function(e){if(!v(e)){for(var t=[],n=0;n<arguments.length;n++)t.push(i(arguments[n]));return t.join(" ")}n=1;for(var o=arguments,u=o.length,s=String(e).replace(r,function(e){if("%%"===e)return"%";if(n>=u)return e;switch(e){case"%s":return String(o[n++]);case"%d":return Number(o[n++]);case"%j":try{return JSON.stringify(o[n++])}catch(t){return"[Circular]"}default:return e}}),c=o[n];n<u;c=o[++n])h(c)||!S(c)?s+=" "+c:s+=" "+i(c);return s},exports.deprecate=function(t,r){if(void 0!==e&&!0===e.noDeprecation)return t;if(void 0===e)return function(){return exports.deprecate(t,r).apply(this,arguments)};var n=!1;return function(){if(!n){if(e.throwDeprecation)throw new Error(r);e.traceDeprecation?console.trace(r):console.error(r),n=!0}return t.apply(this,arguments)}};var n,o={};function i(e,t){var r={seen:[],stylize:s};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),b(t)?r.showHidden=t:t&&exports._extend(r,t),j(r.showHidden)&&(r.showHidden=!1),j(r.depth)&&(r.depth=2),j(r.colors)&&(r.colors=!1),j(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=u),p(r,e,r.depth)}function u(e,t){var r=i.styles[t];return r?"["+i.colors[r][0]+"m"+e+"["+i.colors[r][1]+"m":e}function s(e,t){return e}function c(e){var t={};return e.forEach(function(e,r){t[e]=!0}),t}function p(e,t,r){if(e.customInspect&&t&&P(t.inspect)&&t.inspect!==exports.inspect&&(!t.constructor||t.constructor.prototype!==t)){var n=t.inspect(r,e);return v(n)||(n=p(e,n,r)),n}var o=l(e,t);if(o)return o;var i=Object.keys(t),u=c(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(t)),E(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return f(t);if(0===i.length){if(P(t)){var s=t.name?": "+t.name:"";return e.stylize("[Function"+s+"]","special")}if(w(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(z(t))return e.stylize(Date.prototype.toString.call(t),"date");if(E(t))return f(t)}var b,h="",m=!1,x=["{","}"];(d(t)&&(m=!0,x=["[","]"]),P(t))&&(h=" [Function"+(t.name?": "+t.name:"")+"]");return w(t)&&(h=" "+RegExp.prototype.toString.call(t)),z(t)&&(h=" "+Date.prototype.toUTCString.call(t)),E(t)&&(h=" "+f(t)),0!==i.length||m&&0!=t.length?r<0?w(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),b=m?a(e,t,r,u,i):i.map(function(n){return y(e,t,r,u,n,m)}),e.seen.pop(),g(b,h,x)):x[0]+h+x[1]}function l(e,t){if(j(t))return e.stylize("undefined","undefined");if(v(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return x(t)?e.stylize(""+t,"number"):b(t)?e.stylize(""+t,"boolean"):h(t)?e.stylize("null","null"):void 0}function f(e){return"["+Error.prototype.toString.call(e)+"]"}function a(e,t,r,n,o){for(var i=[],u=0,s=t.length;u<s;++u)A(t,String(u))?i.push(y(e,t,r,n,String(u),!0)):i.push("");return o.forEach(function(o){o.match(/^\d+$/)||i.push(y(e,t,r,n,o,!0))}),i}function y(e,t,r,n,o,i){var u,s,c;if((c=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]}).get?s=c.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):c.set&&(s=e.stylize("[Setter]","special")),A(n,o)||(u="["+o+"]"),s||(e.seen.indexOf(c.value)<0?(s=h(r)?p(e,c.value,null):p(e,c.value,r-1)).indexOf("\n")>-1&&(s=i?s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+s.split("\n").map(function(e){return"   "+e}).join("\n")):s=e.stylize("[Circular]","special")),j(u)){if(i&&o.match(/^\d+$/))return s;(u=JSON.stringify(""+o)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(u=u.substr(1,u.length-2),u=e.stylize(u,"name")):(u=u.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),u=e.stylize(u,"string"))}return u+": "+s}function g(e,t,r){return e.reduce(function(e,t){return 0,t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]:r[0]+t+" "+e.join(", ")+" "+r[1]}function d(e){return Array.isArray(e)}function b(e){return"boolean"==typeof e}function h(e){return null===e}function m(e){return null==e}function x(e){return"number"==typeof e}function v(e){return"string"==typeof e}function O(e){return"symbol"==typeof e}function j(e){return void 0===e}function w(e){return S(e)&&"[object RegExp]"===T(e)}function S(e){return"object"==typeof e&&null!==e}function z(e){return S(e)&&"[object Date]"===T(e)}function E(e){return S(e)&&("[object Error]"===T(e)||e instanceof Error)}function P(e){return"function"==typeof e}function D(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e}function T(e){return Object.prototype.toString.call(e)}function N(e){return e<10?"0"+e.toString(10):e.toString(10)}exports.debuglog=function(t){if(j(n)&&(n=""),t=t.toUpperCase(),!o[t])if(new RegExp("\\b"+t+"\\b","i").test(n)){var r=e.pid;o[t]=function(){var e=exports.format.apply(exports,arguments);console.error("%s %d: %s",t,r,e)}}else o[t]=function(){};return o[t]},exports.inspect=i,i.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},i.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},exports.isArray=d,exports.isBoolean=b,exports.isNull=h,exports.isNullOrUndefined=m,exports.isNumber=x,exports.isString=v,exports.isSymbol=O,exports.isUndefined=j,exports.isRegExp=w,exports.isObject=S,exports.isDate=z,exports.isError=E,exports.isFunction=P,exports.isPrimitive=D,exports.isBuffer=require("./support/isBuffer");var F=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function k(){var e=new Date,t=[N(e.getHours()),N(e.getMinutes()),N(e.getSeconds())].join(":");return[e.getDate(),F[e.getMonth()],t].join(" ")}function A(e,t){return Object.prototype.hasOwnProperty.call(e,t)}exports.log=function(){console.log("%s - %s",k(),exports.format.apply(exports,arguments))},exports.inherits=require("inherits"),exports._extend=function(e,t){if(!t||!S(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e};var J="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function R(e,t){if(!e){var r=new Error("Promise was rejected with a falsy value");r.reason=e,e=r}return t(e)}function H(r){if("function"!=typeof r)throw new TypeError('The "original" argument must be of type Function');function n(){for(var t=[],n=0;n<arguments.length;n++)t.push(arguments[n]);var o=t.pop();if("function"!=typeof o)throw new TypeError("The last argument must be of type Function");var i=this,u=function(){return o.apply(i,arguments)};r.apply(this,t).then(function(t){e.nextTick(u,null,t)},function(t){e.nextTick(R,t,u)})}return Object.setPrototypeOf(n,Object.getPrototypeOf(r)),Object.defineProperties(n,t(r)),n}exports.promisify=function(e){if("function"!=typeof e)throw new TypeError('The "original" argument must be of type Function');if(J&&e[J]){var r;if("function"!=typeof(r=e[J]))throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(r,J,{value:r,enumerable:!1,writable:!1,configurable:!0}),r}function r(){for(var t,r,n=new Promise(function(e,n){t=e,r=n}),o=[],i=0;i<arguments.length;i++)o.push(arguments[i]);o.push(function(e,n){e?r(e):t(n)});try{e.apply(this,o)}catch(u){r(u)}return n}return Object.setPrototypeOf(r,Object.getPrototypeOf(e)),J&&Object.defineProperty(r,J,{value:r,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(r,t(e))},exports.promisify.custom=J,exports.callbackify=H;
},{"./support/isBuffer":"rd3l","inherits":"fCKU","process":"pBGv"}],"VDee":[function(require,module,exports) {
var Buffer = require("buffer").Buffer;
var r=require("buffer").Buffer,e=require("assert"),t=require("stream").Stream,n=require("util"),o=/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/;function c(r){return r.charAt(0).toUpperCase()+r.slice(1)}function a(r,t,o,c,u){throw new e.AssertionError({message:n.format("%s (%s) is required",r,t),actual:void 0===u?typeof c:u(c),expected:t,operator:o||"===",stackStartFunction:a.caller})}function u(r){return Object.prototype.toString.call(r).slice(8,-1)}function f(){}var i={bool:{check:function(r){return"boolean"==typeof r}},func:{check:function(r){return"function"==typeof r}},string:{check:function(r){return"string"==typeof r}},object:{check:function(r){return"object"==typeof r&&null!==r}},number:{check:function(r){return"number"==typeof r&&!isNaN(r)}},finite:{check:function(r){return"number"==typeof r&&!isNaN(r)&&isFinite(r)}},buffer:{check:function(e){return r.isBuffer(e)},operator:"Buffer.isBuffer"},array:{check:function(r){return Array.isArray(r)},operator:"Array.isArray"},stream:{check:function(r){return r instanceof t},operator:"instanceof",actual:u},date:{check:function(r){return r instanceof Date},operator:"instanceof",actual:u},regexp:{check:function(r){return r instanceof RegExp},operator:"instanceof",actual:u},uuid:{check:function(r){return"string"==typeof r&&o.test(r)},operator:"isUUID"}};function s(r){var t,n=Object.keys(i);return t=function(r,e){r||a(e,"true",r)},n.forEach(function(e){if(r)t[e]=f;else{var n=i[e];t[e]=function(r,t){n.check(r)||a(t,e,n.operator,r,n.actual)}}}),n.forEach(function(e){var n="optional"+c(e);if(r)t[n]=f;else{var o=i[e];t[n]=function(r,t){null!=r&&(o.check(r)||a(t,e,o.operator,r,o.actual))}}}),n.forEach(function(e){var n="arrayOf"+c(e);if(r)t[n]=f;else{var o=i[e],u="["+e+"]";t[n]=function(r,e){var t;for(Array.isArray(r)||a(e,u,o.operator,r,o.actual),t=0;t<r.length;t++)o.check(r[t])||a(e,u,o.operator,r,o.actual)}}}),n.forEach(function(e){var n="optionalArrayOf"+c(e);if(r)t[n]=f;else{var o=i[e],u="["+e+"]";t[n]=function(r,e){var t;if(null!=r)for(Array.isArray(r)||a(e,u,o.operator,r,o.actual),t=0;t<r.length;t++)o.check(r[t])||a(e,u,o.operator,r,o.actual)}}}),Object.keys(e).forEach(function(n){t[n]="AssertionError"!==n&&r?f:e[n]}),t._setExports=s,t}module.exports=s(void 0);
},{"assert":"DlZn","stream":"fnRj","util":"gfUn","buffer":"dskh"}],"nHYk":[function(require,module,exports) {
var global = arguments[3];
var n=arguments[3];Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=Object.keys,e=Array.isArray,r="undefined"!=typeof self?self:"undefined"!=typeof window?window:n;function i(n,e){return"object"!=typeof e?n:(t(e).forEach(function(t){n[t]=e[t]}),n)}var o=Object.getPrototypeOf,u={}.hasOwnProperty;function a(n,t){return u.call(n,t)}function c(n,e){"function"==typeof e&&(e=e(o(n))),t(e).forEach(function(t){f(n,t,e[t])})}var s=Object.defineProperty;function f(n,t,e,r){s(n,t,i(e&&a(e,"get")&&"function"==typeof e.get?{get:e.get,set:e.set,configurable:!0}:{value:e,configurable:!0,writable:!0},r))}function l(n){return{from:function(t){return n.prototype=Object.create(t.prototype),f(n.prototype,"constructor",n),{extend:c.bind(null,n.prototype)}}}}var h=Object.getOwnPropertyDescriptor;function d(n,t){var e;return h(n,t)||(e=o(n))&&d(e,t)}var v=[].slice;function p(n,t,e){return v.call(n,t,e)}function y(n,t){return t(n)}function m(n){if(!n)throw new Error("Assertion Failed")}function g(n){r.setImmediate?setImmediate(n):setTimeout(n,0)}function b(n,t){return n.reduce(function(n,e,r){var i=t(e,r);return i&&(n[i[0]]=i[1]),n},{})}function w(n,t){return function(){try{n.apply(this,arguments)}catch(e){t(e)}}}function _(n,t,e){try{n.apply(null,e)}catch(r){t&&t(r)}}function k(n,t){if(a(n,t))return n[t];if(!t)return n;if("string"!=typeof t){for(var e=[],r=0,i=t.length;r<i;++r){var o=k(n,t[r]);e.push(o)}return e}var u=t.indexOf(".");if(-1!==u){var c=n[t.substr(0,u)];return void 0===c?void 0:k(c,t.substr(u+1))}}function x(n,t,e){if(n&&void 0!==t&&!("isFrozen"in Object&&Object.isFrozen(n)))if("string"!=typeof t&&"length"in t){m("string"!=typeof e&&"length"in e);for(var r=0,i=t.length;r<i;++r)x(n,t[r],e[r])}else{var o=t.indexOf(".");if(-1!==o){var u=t.substr(0,o),a=t.substr(o+1);if(""===a)void 0===e?delete n[u]:n[u]=e;else{var c=n[u];c||(c=n[u]={}),x(c,a,e)}}else void 0===e?delete n[t]:n[t]=e}}function P(n,t){"string"==typeof t?x(n,t,void 0):"length"in t&&[].map.call(t,function(t){x(n,t,void 0)})}function j(n){var t={};for(var e in n)a(n,e)&&(t[e]=n[e]);return t}var E=[].concat;function A(n){return E.apply([],n)}var O="Boolean,String,Date,RegExp,Blob,File,FileList,ArrayBuffer,DataView,Uint8ClampedArray,ImageData,Map,Set".split(",").concat(A([8,16,32,64].map(function(n){return["Int","Uint","Float"].map(function(t){return t+n+"Array"})}))).filter(function(n){return r[n]}).map(function(n){return r[n]});function S(n){if(!n||"object"!=typeof n)return n;var t;if(e(n)){t=[];for(var r=0,i=n.length;r<i;++r)t.push(S(n[r]))}else if(O.indexOf(n.constructor)>=0)t=n;else for(var o in t=n.constructor?Object.create(n.constructor.prototype):{},n)a(n,o)&&(t[o]=S(n[o]));return t}function D(n,e,r,i){return r=r||{},i=i||"",t(n).forEach(function(t){if(a(e,t)){var o=n[t],u=e[t];"object"==typeof o&&"object"==typeof u&&o&&u&&""+o.constructor==""+u.constructor?D(o,u,r,i+t+"."):o!==u&&(r[i+t]=e[t])}else r[i+t]=void 0}),t(e).forEach(function(t){a(n,t)||(r[i+t]=e[t])}),r}var I="undefined"!=typeof Symbol&&Symbol.iterator,C=I?function(n){var t;return null!=n&&(t=n[I])&&t.apply(n)}:function(){return null},K={};function T(n){var t,r,i,o;if(1===arguments.length){if(e(n))return n.slice();if(this===K&&"string"==typeof n)return[n];if(o=C(n)){for(r=[];!(i=o.next()).done;)r.push(i.value);return r}if(null==n)return[n];if("number"==typeof(t=n.length)){for(r=new Array(t);t--;)r[t]=n[t];return r}return[n]}for(t=arguments.length,r=new Array(t);t--;)r[t]=arguments[t];return r}var B="undefined"!=typeof location&&/^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(location.href);function F(n,t){B=n,M=t}var M=function(){return!0},N=!new Error("").stack;function q(){"use strict";if(N)try{throw q.arguments,new Error}catch(n){return n}return new Error}function R(n,t){var e=n.stack;return e?(t=t||0,0===e.indexOf(n.name)&&(t+=(n.name+n.message).split("\n").length),e.split("\n").slice(t).filter(M).map(function(n){return"\n"+n}).join("")):""}function U(n,t){return function(){return console.warn(n+" is deprecated. See https://github.com/dfahlander/Dexie.js/wiki/Deprecations. "+R(q(),1)),t.apply(this,arguments)}}var V=["Modify","Bulk","OpenFailed","VersionChange","Schema","Upgrade","InvalidTable","MissingAPI","NoSuchDatabase","InvalidArgument","SubTransaction","Unsupported","Internal","DatabaseClosed","PrematureCommit","ForeignAwait"],z=["Unknown","Constraint","Data","TransactionInactive","ReadOnly","Version","NotFound","InvalidState","InvalidAccess","Abort","Timeout","QuotaExceeded","Syntax","DataClone"],L=V.concat(z),W={VersionChanged:"Database version changed by other database connection",DatabaseClosed:"Database has been closed",Abort:"Transaction aborted",TransactionInactive:"Transaction has already completed or failed"};function Q(n,t){this._e=q(),this.name=n,this.message=t}function H(n,t){return n+". Errors: "+t.map(function(n){return n.toString()}).filter(function(n,t,e){return e.indexOf(n)===t}).join("\n")}function G(n,t,e,r){this._e=q(),this.failures=t,this.failedKeys=r,this.successCount=e}function J(n,t){this._e=q(),this.name="BulkError",this.failures=t,this.message=H(n,t)}l(Q).from(Error).extend({stack:{get:function(){return this._stack||(this._stack=this.name+": "+this.message+R(this._e,2))}},toString:function(){return this.name+": "+this.message}}),l(G).from(Q),l(J).from(Q);var Y=L.reduce(function(n,t){return n[t]=t+"Error",n},{}),$=Q,X=L.reduce(function(n,t){var e=t+"Error";function r(n,r){this._e=q(),this.name=e,n?"string"==typeof n?(this.message=n,this.inner=r||null):"object"==typeof n&&(this.message=n.name+" "+n.message,this.inner=n):(this.message=W[t]||e,this.inner=null)}return l(r).from($),n[t]=r,n},{});X.Syntax=SyntaxError,X.Type=TypeError,X.Range=RangeError;var Z=z.reduce(function(n,t){return n[t+"Error"]=X[t],n},{});function nn(n,t){if(!n||n instanceof Q||n instanceof TypeError||n instanceof SyntaxError||!n.name||!Z[n.name])return n;var e=new Z[n.name](t||n.message,n);return"stack"in n&&f(e,"stack",{get:function(){return this.inner.stack}}),e}var tn=L.reduce(function(n,t){return-1===["Syntax","Type","Range"].indexOf(t)&&(n[t+"Error"]=X[t]),n},{});function en(){}function rn(n){return n}function on(n,t){return null==n||n===rn?t:function(e){return t(n(e))}}function un(n,t){return function(){n.apply(this,arguments),t.apply(this,arguments)}}function an(n,t){return n===en?t:function(){var e=n.apply(this,arguments);void 0!==e&&(arguments[0]=e);var r=this.onsuccess,i=this.onerror;this.onsuccess=null,this.onerror=null;var o=t.apply(this,arguments);return r&&(this.onsuccess=this.onsuccess?un(r,this.onsuccess):r),i&&(this.onerror=this.onerror?un(i,this.onerror):i),void 0!==o?o:e}}function cn(n,t){return n===en?t:function(){n.apply(this,arguments);var e=this.onsuccess,r=this.onerror;this.onsuccess=this.onerror=null,t.apply(this,arguments),e&&(this.onsuccess=this.onsuccess?un(e,this.onsuccess):e),r&&(this.onerror=this.onerror?un(r,this.onerror):r)}}function sn(n,t){return n===en?t:function(e){var r=n.apply(this,arguments);i(e,r);var o=this.onsuccess,u=this.onerror;this.onsuccess=null,this.onerror=null;var a=t.apply(this,arguments);return o&&(this.onsuccess=this.onsuccess?un(o,this.onsuccess):o),u&&(this.onerror=this.onerror?un(u,this.onerror):u),void 0===r?void 0===a?void 0:a:i(r,a)}}function fn(n,t){return n===en?t:function(){return!1!==t.apply(this,arguments)&&n.apply(this,arguments)}}function ln(n,t){return n===en?t:function(){var e=n.apply(this,arguments);if(e&&"function"==typeof e.then){for(var r=this,i=arguments.length,o=new Array(i);i--;)o[i]=arguments[i];return e.then(function(){return t.apply(r,o)})}return t.apply(this,arguments)}}tn.ModifyError=G,tn.DexieError=Q,tn.BulkError=J;var hn={},dn=100,vn=20,pn=7,yn=function(){try{return new Function("let F=async ()=>{},p=F();return [p,Object.getPrototypeOf(p),Promise.resolve(),F.constructor];")()}catch(t){var n=r.Promise;return n?[n.resolve(),n.prototype,n.resolve()]:[]}}(),mn=yn[0],gn=yn[1],bn=yn[2],wn=gn&&gn.then,_n=mn&&mn.constructor,kn=yn[3],xn=!!bn,Pn=!1,jn=bn?function(){bn.then(Gn)}:r.setImmediate?setImmediate.bind(null,Gn):r.MutationObserver?function(){var n=document.createElement("div");new MutationObserver(function(){Gn(),n=null}).observe(n,{attributes:!0}),n.setAttribute("i","1")}:function(){setTimeout(Gn,0)},En=function(n,t){Bn.push([n,t]),On&&(jn(),On=!1)},An=!0,On=!0,Sn=[],Dn=[],In=null,Cn=rn,Kn={id:"global",global:!0,ref:0,unhandleds:[],onunhandled:kt,pgp:!1,env:{},finalize:function(){this.unhandleds.forEach(function(n){try{kt(n[0],n[1])}catch(t){}})}},Tn=Kn,Bn=[],Fn=0,Mn=[];function Nn(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");this._listeners=[],this.onuncatched=en,this._lib=!1;var t=this._PSD=Tn;if(B&&(this._stackHolder=q(),this._prev=null,this._numPrev=0),"function"!=typeof n){if(n!==hn)throw new TypeError("Not a function");return this._state=arguments[1],this._value=arguments[2],void(!1===this._state&&Vn(this,this._value))}this._state=null,this._value=null,++t.ref,Un(this,n)}var qn={get:function(){var n=Tn,t=at;function e(e,r){var i=this,o=!n.global&&(n!==Tn||t!==at);o&&lt();var u=new Nn(function(t,u){Ln(i,new Rn(bt(e,n,o),bt(r,n,o),t,u,n))});return B&&Hn(u,this),u}return e.prototype=hn,e},set:function(n){f(this,"then",n&&n.prototype===hn?qn:{get:function(){return n},set:qn.set})}};function Rn(n,t,e,r,i){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.resolve=e,this.reject=r,this.psd=i}function Un(n,t){try{t(function(t){if(null===n._state){if(t===n)throw new TypeError("A promise cannot be resolved with itself.");var e=n._lib&&Jn();t&&"function"==typeof t.then?Un(n,function(n,e){t instanceof Nn?t._then(n,e):t.then(n,e)}):(n._state=!0,n._value=t,zn(n)),e&&Yn()}},Vn.bind(null,n))}catch(e){Vn(n,e)}}function Vn(n,t){if(Dn.push(t),null===n._state){var e=n._lib&&Jn();t=Cn(t),n._state=!1,n._value=t,B&&null!==t&&"object"==typeof t&&!t._promise&&_(function(){var e=d(t,"stack");t._promise=n,f(t,"stack",{get:function(){return Pn?e&&(e.get?e.get.apply(t):e.value):n.stack}})}),Zn(n),zn(n),e&&Yn()}}function zn(n){var t=n._listeners;n._listeners=[];for(var e=0,r=t.length;e<r;++e)Ln(n,t[e]);var i=n._PSD;--i.ref||i.finalize(),0===Fn&&(++Fn,En(function(){0==--Fn&&$n()},[]))}function Ln(n,t){if(null!==n._state){var e=n._state?t.onFulfilled:t.onRejected;if(null===e)return(n._state?t.resolve:t.reject)(n._value);++t.psd.ref,++Fn,En(Wn,[e,n,t])}else n._listeners.push(t)}function Wn(n,t,e){try{In=t;var r,i=t._value;t._state?r=n(i):(Dn.length&&(Dn=[]),r=n(i),-1===Dn.indexOf(i)&&nt(t)),e.resolve(r)}catch(o){e.reject(o)}finally{In=null,0==--Fn&&$n(),--e.psd.ref||e.psd.finalize()}}function Qn(n,t,e){if(t.length===e)return t;var r="";if(!1===n._state){var i,o,u=n._value;null!=u?(i=u.name||"Error",o=u.message||u,r=R(u,0)):(i=u,o=""),t.push(i+(o?": "+o:"")+r)}return B&&((r=R(n._stackHolder,2))&&-1===t.indexOf(r)&&t.push(r),n._prev&&Qn(n._prev,t,e)),t}function Hn(n,t){var e=t?t._numPrev+1:0;e<dn&&(n._prev=t,n._numPrev=e)}function Gn(){Jn()&&Yn()}function Jn(){var n=An;return An=!1,On=!1,n}function Yn(){var n,t,e;do{for(;Bn.length>0;)for(n=Bn,Bn=[],e=n.length,t=0;t<e;++t){var r=n[t];r[0].apply(null,r[1])}}while(Bn.length>0);An=!0,On=!0}function $n(){var n=Sn;Sn=[],n.forEach(function(n){n._PSD.onunhandled.call(null,n._value,n)});for(var t=Mn.slice(0),e=t.length;e;)t[--e]()}function Xn(n){Mn.push(function t(){n(),Mn.splice(Mn.indexOf(t),1)}),++Fn,En(function(){0==--Fn&&$n()},[])}function Zn(n){Sn.some(function(t){return t._value===n._value})||Sn.push(n)}function nt(n){for(var t=Sn.length;t;)if(Sn[--t]._value===n._value)return void Sn.splice(t,1)}function tt(n){return new Nn(hn,!1,n)}function et(n,t){var e=Tn;return function(){var r=Jn(),i=Tn;try{return pt(e,!0),n.apply(this,arguments)}catch(o){t&&t(o)}finally{pt(i,!1),r&&Yn()}}}c(Nn.prototype,{then:qn,_then:function(n,t){Ln(this,new Rn(null,null,n,t,Tn))},catch:function(n){if(1===arguments.length)return this.then(null,n);var t=arguments[0],e=arguments[1];return"function"==typeof t?this.then(null,function(n){return n instanceof t?e(n):tt(n)}):this.then(null,function(n){return n&&n.name===t?e(n):tt(n)})},finally:function(n){return this.then(function(t){return n(),t},function(t){return n(),tt(t)})},stack:{get:function(){if(this._stack)return this._stack;try{Pn=!0;var n=Qn(this,[],vn).join("\nFrom previous: ");return null!==this._state&&(this._stack=n),n}finally{Pn=!1}}},timeout:function(n,t){var e=this;return n<1/0?new Nn(function(r,i){var o=setTimeout(function(){return i(new X.Timeout(t))},n);e.then(r,i).finally(clearTimeout.bind(null,o))}):this}}),"undefined"!=typeof Symbol&&Symbol.toStringTag&&f(Nn.prototype,Symbol.toStringTag,"Promise"),Kn.env=yt(),c(Nn,{all:function(){var n=T.apply(null,arguments).map(ht);return new Nn(function(t,e){0===n.length&&t([]);var r=n.length;n.forEach(function(i,o){return Nn.resolve(i).then(function(e){n[o]=e,--r||t(n)},e)})})},resolve:function(n){if(n instanceof Nn)return n;if(n&&"function"==typeof n.then)return new Nn(function(t,e){n.then(t,e)});var t=new Nn(hn,!0,n);return Hn(t,In),t},reject:tt,race:function(){var n=T.apply(null,arguments).map(ht);return new Nn(function(t,e){n.map(function(n){return Nn.resolve(n).then(t,e)})})},PSD:{get:function(){return Tn},set:function(n){return Tn=n}},newPSD:st,usePSD:mt,scheduler:{get:function(){return En},set:function(n){En=n}},rejectionMapper:{get:function(){return Cn},set:function(n){Cn=n}},follow:function(n,t){return new Nn(function(e,r){return st(function(t,e){var r=Tn;r.unhandleds=[],r.onunhandled=e,r.finalize=un(function(){var n=this;Xn(function(){0===n.unhandleds.length?t():e(n.unhandleds[0])})},r.finalize),n()},t,e,r)})}});var rt={awaits:0,echoes:0,id:0},it=0,ot=[],ut=0,at=0,ct=0;function st(n,t,e,r){var o=Tn,u=Object.create(o);u.parent=o,u.ref=0,u.global=!1,u.id=++ct;var a=Kn.env;u.env=xn?{Promise:Nn,PromiseProp:{value:Nn,configurable:!0,writable:!0},all:Nn.all,race:Nn.race,resolve:Nn.resolve,reject:Nn.reject,nthen:wt(a.nthen,u),gthen:wt(a.gthen,u)}:{},t&&i(u,t),++o.ref,u.finalize=function(){--this.parent.ref||this.parent.finalize()};var c=mt(u,n,e,r);return 0===u.ref&&u.finalize(),c}function ft(){return rt.id||(rt.id=++it),++rt.awaits,rt.echoes+=pn,rt.id}function lt(n){!rt.awaits||n&&n!==rt.id||(0==--rt.awaits&&(rt.id=0),rt.echoes=rt.awaits*pn)}function ht(n){return rt.echoes&&n&&n.constructor===_n?(ft(),n.then(function(n){return lt(),n},function(n){return lt(),xt(n)})):n}function dt(n){++at,rt.echoes&&0!=--rt.echoes||(rt.echoes=rt.id=0),ot.push(Tn),pt(n,!0)}function vt(){var n=ot[ot.length-1];ot.pop(),pt(n,!1)}function pt(n,t){var e=Tn;if((t?!rt.echoes||ut++&&n===Tn:!ut||--ut&&n===Tn)||gt(t?dt.bind(null,n):vt),n!==Tn&&(Tn=n,e===Kn&&(Kn.env=yt()),xn)){var i=Kn.env.Promise,o=n.env;gn.then=o.nthen,i.prototype.then=o.gthen,(e.global||n.global)&&(Object.defineProperty(r,"Promise",o.PromiseProp),i.all=o.all,i.race=o.race,i.resolve=o.resolve,i.reject=o.reject)}}function yt(){var n=r.Promise;return xn?{Promise:n,PromiseProp:Object.getOwnPropertyDescriptor(r,"Promise"),all:n.all,race:n.race,resolve:n.resolve,reject:n.reject,nthen:gn.then,gthen:n.prototype.then}:{}}function mt(n,t,e,r,i){var o=Tn;try{return pt(n,!0),t(e,r,i)}finally{pt(o,!1)}}function gt(n){wn.call(mn,n)}function bt(n,t,e){return"function"!=typeof n?n:function(){var r=Tn;e&&ft(),pt(t,!0);try{return n.apply(this,arguments)}finally{pt(r,!1)}}}function wt(n,t){return function(e,r){return n.call(this,bt(e,t,!1),bt(r,t,!1))}}var _t="unhandledrejection";function kt(n,t){var e;try{e=t.onuncatched(n)}catch(a){}if(!1!==e)try{var o,u={promise:t,reason:n};if(r.document&&document.createEvent?((o=document.createEvent("Event")).initEvent(_t,!0,!0),i(o,u)):r.CustomEvent&&i(o=new CustomEvent(_t,{detail:u}),u),o&&r.dispatchEvent&&(dispatchEvent(o),!r.PromiseRejectionEvent&&r.onunhandledrejection))try{r.onunhandledrejection(o)}catch(c){}o.defaultPrevented||console.warn("Unhandled rejection: "+(n.stack||n))}catch(a){}}var xt=Nn.reject;function Pt(n){var r={},i=function(t,e){if(e){for(var i=arguments.length,o=new Array(i-1);--i;)o[i-1]=arguments[i];return r[t].subscribe.apply(null,o),n}if("string"==typeof t)return r[t]};i.addEventType=a;for(var o=1,u=arguments.length;o<u;++o)a(arguments[o]);return i;function a(n,o,u){if("object"!=typeof n){var c;o||(o=fn),u||(u=en);var s={subscribers:[],fire:u,subscribe:function(n){-1===s.subscribers.indexOf(n)&&(s.subscribers.push(n),s.fire=o(s.fire,n))},unsubscribe:function(n){s.subscribers=s.subscribers.filter(function(t){return t!==n}),s.fire=s.subscribers.reduce(o,u)}};return r[n]=i[n]=s,s}t(c=n).forEach(function(n){var t=c[n];if(e(t))a(n,c[n][0],c[n][1]);else{if("asap"!==t)throw new X.InvalidArgument("Invalid event config");var r=a(n,rn,function(){for(var n=arguments.length,t=new Array(n);n--;)t[n]=arguments[n];r.subscribers.forEach(function(n){g(function(){n.apply(null,t)})})})}})}}var jt,Et="{version}",At=String.fromCharCode(65535),Ot=function(){try{return IDBKeyRange.only([[]]),[[]]}catch(n){return At}}(),St=-1/0,Dt="Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.",It="String expected.",Ct=[],Kt="undefined"!=typeof navigator&&/(MSIE|Trident|Edge)/.test(navigator.userAgent),Tt=Kt,Bt=Kt,Ft=function(n){return!/(dexie\.js|dexie\.min\.js)/.test(n)};function Mt(n,o){var u,s,l,h=Mt.dependencies,d=i({addons:Mt.addons,autoOpen:!0,indexedDB:h.indexedDB,IDBKeyRange:h.IDBKeyRange},o),v=d.addons,g=d.autoOpen,P=d.indexedDB,E=d.IDBKeyRange,O=this._dbSchema={},I=[],C=[],F={},M=null,N=null,R=!1,V=null,z=!1,L="readwrite",W=this,Q=new Nn(function(n){u=n}),H=new Nn(function(n,t){s=t}),Y=!0,$=!!Jt(P);function Z(n){this._cfg={version:n,storesSource:null,dbschema:{},tables:{},contentUpgrade:null},this.stores({})}function nn(n,e,r){var i=W._createTransaction(L,C,O);i.create(e),i._completion.catch(r);var o=i._reject.bind(i);st(function(){Tn.trans=i,0===n?(t(O).forEach(function(n){tn(e,n,O[n].primKey,O[n].indexes)}),Nn.follow(function(){return W.on.populate.fire(i)}).catch(o)):function(n,e,r){var i=[],o=I.filter(function(t){return t._cfg.version===n})[0];if(!o)throw new X.Upgrade("Dexie specification of currently installed DB version is missing");O=W._dbSchema=o._cfg.dbschema;var u=!1;return I.filter(function(t){return t._cfg.version>n}).forEach(function(n){i.push(function(){var t=O,i=n._cfg.dbschema;Dn(t,r),Dn(i,r),O=W._dbSchema=i;var o=function(n,t){var e={del:[],add:[],change:[]};for(var r in n)t[r]||e.del.push(r);for(r in t){var i=n[r],o=t[r];if(i){var u={name:r,def:o,recreate:!1,del:[],add:[],change:[]};if(i.primKey.src!==o.primKey.src)u.recreate=!0,e.change.push(u);else{var a=i.idxByName,c=o.idxByName;for(var s in a)c[s]||u.del.push(s);for(s in c){var f=a[s],l=c[s];f?f.src!==l.src&&u.change.push(l):u.add.push(l)}(u.del.length>0||u.add.length>0||u.change.length>0)&&e.change.push(u)}}else e.add.push([r,o])}return e}(t,i);if(o.add.forEach(function(n){tn(r,n[0],n[1].primKey,n[1].indexes)}),o.change.forEach(function(n){if(n.recreate)throw new X.Upgrade("Not yet support for changing primary key");var t=r.objectStore(n.name);n.add.forEach(function(n){un(t,n)}),n.change.forEach(function(n){t.deleteIndex(n.name),un(t,n)}),n.del.forEach(function(n){t.deleteIndex(n)})}),n._cfg.contentUpgrade)return u=!0,Nn.follow(function(){n._cfg.contentUpgrade(e)})}),i.push(function(t){u&&Tt||function(n,t){for(var e=0;e<t.db.objectStoreNames.length;++e){var r=t.db.objectStoreNames[e];null==n[r]&&t.db.deleteObjectStore(r)}}(n._cfg.dbschema,t)})}),function n(){return i.length?Nn.resolve(i.shift()(e.idbtrans)).then(n):Nn.resolve()}().then(function(){!function(n,e){t(n).forEach(function(t){e.db.objectStoreNames.contains(t)||tn(e,t,n[t].primKey,n[t].indexes)})}(O,r)})}(n,i,e).catch(o)})}function tn(n,t,e,r){var i=n.db.createObjectStore(t,e.keyPath?{keyPath:e.keyPath,autoIncrement:e.auto}:{autoIncrement:e.auto});return r.forEach(function(n){un(i,n)}),i}function un(n,t){n.createIndex(t.name,t.keyPath,{unique:t.unique,multiEntry:t.multi})}function fn(n,t,e){this.name=n,this.schema=t,this._tx=e,this.hook=F[n]?F[n].hook:Pt(null,{creating:[an,en],reading:[on,rn],updating:[sn,en],deleting:[cn,en]})}function hn(n,t,e){return(e?zt:Ut)(function(e){n.push(e),t&&t()})}function dn(n,t,e,r,i){return new Nn(function(o,u){var a=e.length,c=a-1;if(0===a)return o();if(r){var s,f=zt(u),l=Rt(null);_(function(){for(var r=0;r<a;++r){s={onsuccess:null,onerror:null};var u=e[r];i.call(s,u[0],u[1],t);var h=n.delete(u[0]);h._hookCtx=s,h.onerror=f,h.onsuccess=r===c?Rt(o):l}},function(n){throw s.onerror&&s.onerror(n),n})}else for(var h=0;h<a;++h){var d=n.delete(e[h]);d.onerror=Ut(u),h===c&&(d.onsuccess=et(function(){return o()}))}})}function vn(n,t,e,r){var i=this;this.db=W,this.mode=n,this.storeNames=t,this.idbtrans=null,this.on=Pt(this,"complete","error","abort"),this.parent=r||null,this.active=!0,this._reculock=0,this._blockedFuncs=[],this._resolve=null,this._reject=null,this._waitingFor=null,this._waitingQueue=null,this._spinCount=0,this._completion=new Nn(function(n,t){i._resolve=n,i._reject=t}),this._completion.then(function(){i.active=!1,i.on.complete.fire()},function(n){var t=i.active;return i.active=!1,i.on.error.fire(n),i.parent?i.parent._reject(n):t&&i.idbtrans&&i.idbtrans.abort(),xt(n)})}function pn(n,t,e){this._ctx={table:n,index:":id"===t?null:t,or:e}}function yn(n,t){var e=null,r=null;if(t)try{e=t()}catch(u){r=u}var i=n._ctx,o=i.table;this._ctx={table:o,index:i.index,isPrimKey:!i.index||o.schema.primKey.keyPath&&i.index===o.schema.primKey.name,range:e,keysOnly:!1,dir:"next",unique:"",algorithm:null,filter:null,replayFilter:null,justLimit:!0,isMatch:null,offset:0,limit:1/0,error:r,or:i.or,valueMapper:o.hook.reading.fire}}function mn(n,t){return!(n.filter||n.algorithm||n.or)&&(t?n.justLimit:!n.replayFilter)}function gn(n,t){return n._cfg.version-t._cfg.version}function bn(n,t,e){t.forEach(function(t){var r=e[t];n.forEach(function(n){t in n||(n===vn.prototype||n instanceof vn?f(n,t,{get:function(){return this.table(t)}}):n[t]=new fn(t,r))})})}function wn(n,t,e,r,i,o){var u=et(o?function(n,t,r){return e(o(n),t,r)}:e,i);n.onerror||(n.onerror=Ut(i)),n.onsuccess=w(t?function(){var e=n.result;if(e){var o=function(){e.continue()};t(e,function(n){o=n},r,i)&&u(e.value,e,function(n){o=n}),o()}else r()}:function(){var t=n.result;if(t){var e=function(){t.continue()};u(t.value,t,function(n){e=n}),e()}else r()},i)}function xn(n,t){return P.cmp(n,t)}function Pn(n,t){return xn(n,t)>0?n:t}function jn(n,t){return P.cmp(n,t)}function En(n,t){return P.cmp(t,n)}function An(n,t){return n<t?-1:n===t?0:1}function On(n,t){return n>t?-1:n===t?0:1}function Sn(n,t){return n?t?function(){return n.apply(this,arguments)&&t.apply(this,arguments)}:n:t}function Dn(n,t){for(var e=t.db.objectStoreNames,i=0;i<e.length;++i){var o=e[i],u=t.objectStore(o);l="getAll"in u;for(var a=0;a<u.indexNames.length;++a){var c=u.indexNames[a],s=u.index(c).keyPath,f="string"==typeof s?s:"["+p(s).join("+")+"]";if(n[o]){var h=n[o].idxByName[f];h&&(h.name=c)}}}/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&r.WorkerGlobalScope&&r instanceof r.WorkerGlobalScope&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604&&(l=!1)}function In(n){W.on("blocked").fire(n),Ct.filter(function(n){return n.name===W.name&&n!==W&&!n._vcFired}).map(function(t){return t.on("versionchange").fire(n)})}this.version=function(n){if(M||R)throw new X.Schema("Cannot add version when database is open");this.verno=Math.max(this.verno,n);var t=I.filter(function(t){return t._cfg.version===n})[0];return t||(t=new Z(n),I.push(t),I.sort(gn),Y=!1,t)},i(Z.prototype,{stores:function(n){this._cfg.storesSource=this._cfg.storesSource?i(this._cfg.storesSource,n):n;var e={};I.forEach(function(n){i(e,n._cfg.storesSource)});var r=this._cfg.dbschema={};return this._parseStoresSpec(e,r),O=W._dbSchema=r,[F,W,vn.prototype].forEach(function(n){for(var t in n)n[t]instanceof fn&&delete n[t]}),bn([F,W,vn.prototype,this._cfg.tables],t(r),r),C=t(r),this},upgrade:function(n){return this._cfg.contentUpgrade=n,this},_parseStoresSpec:function(n,r){t(n).forEach(function(t){if(null!==n[t]){var i={},o=function(n){var t=[];return n.split(",").forEach(function(n){var r=(n=n.trim()).replace(/([&*]|\+\+)/g,""),i=/^\[/.test(r)?r.match(/^\[(.*)\]$/)[1].split("+"):r;t.push(new Qt(r,i||null,/\&/.test(n),/\*/.test(n),/\+\+/.test(n),e(i),/\./.test(n)))}),t}(n[t]),u=o.shift();if(u.multi)throw new X.Schema("Primary key cannot be multi-valued");u.keyPath&&x(i,u.keyPath,u.auto?0:u.keyPath),o.forEach(function(n){if(n.auto)throw new X.Schema("Only primary key can be marked as autoIncrement (++)");if(!n.keyPath)throw new X.Schema("Index must have a name and cannot be an empty string");x(i,n.keyPath,n.compound?n.keyPath.map(function(){return""}):"")}),r[t]=new Ht(t,u,o,i)}})}}),this._allTables=F,this._createTransaction=function(n,t,e,r){return new vn(n,t,e,r)},this._whenReady=function(n){return z||Tn.letThrough?n():new Nn(function(n,t){if(!R){if(!g)return void t(new X.DatabaseClosed);W.open().catch(en)}Q.then(n,t)}).then(n)},this.verno=0,this.open=function(){if(R||M)return Q.then(function(){return N?xt(N):W});B&&(H._stackHolder=q()),R=!0,N=null,z=!1;var e=u,r=null;return Nn.race([H,new Nn(function(e,i){if(!P)throw new X.MissingAPI("indexedDB API not found. If using IE10+, make sure to run your code on a server URL (not locally). If using old Safari versions, make sure to include indexedDB polyfill.");var o=Y?P.open(n):P.open(n,Math.round(10*W.verno));if(!o)throw new X.MissingAPI("IndexedDB API not available");o.onerror=Ut(i),o.onblocked=et(In),o.onupgradeneeded=et(function(t){if(r=o.transaction,Y&&!W._allowEmptyDB){o.onerror=Lt,r.abort(),o.result.close();var e=P.deleteDatabase(n);e.onsuccess=e.onerror=et(function(){i(new X.NoSuchDatabase("Database "+n+" doesnt exist"))})}else{r.onerror=Ut(i),nn((t.oldVersion>Math.pow(2,62)?0:t.oldVersion)/10,r,i)}},i),o.onsuccess=et(function(){if(r=null,M=o.result,Ct.push(W),Y)!function(){if(W.verno=M.version/10,W._dbSchema=O={},0===(C=p(M.objectStoreNames,0)).length)return;var n=M.transaction(Gt(C),"readonly");C.forEach(function(t){for(var e=n.objectStore(t),r=e.keyPath,i=r&&"string"==typeof r&&-1!==r.indexOf("."),o=new Qt(r,r||"",!1,!1,!!e.autoIncrement,r&&"string"!=typeof r,i),u=[],a=0;a<e.indexNames.length;++a){var c=e.index(e.indexNames[a]);r=c.keyPath,i=r&&"string"==typeof r&&-1!==r.indexOf(".");var s=new Qt(c.name,r,!!c.unique,!!c.multiEntry,!1,r&&"string"!=typeof r,i);u.push(s)}O[t]=new Ht(t,o,u,{})}),bn([F],t(O),O)}();else if(M.objectStoreNames.length>0)try{Dn(O,M.transaction(Gt(M.objectStoreNames),"readonly"))}catch(i){}M.onversionchange=et(function(n){W._vcFired=!0,W.on("versionchange").fire(n)}),$||"__dbnames"===n||jt.dbnames.put({name:n}).catch(en),e()},i)})]).then(function(){return V=[],Nn.resolve(Mt.vip(W.on.ready.fire)).then(function n(){if(V.length>0){var t=V.reduce(ln,en);return V=[],Nn.resolve(Mt.vip(t)).then(n)}})}).finally(function(){V=null}).then(function(){return R=!1,W}).catch(function(n){try{r&&r.abort()}catch(t){}return R=!1,W.close(),xt(N=n)}).finally(function(){z=!0,e()})},this.close=function(){var n=Ct.indexOf(W);if(n>=0&&Ct.splice(n,1),M){try{M.close()}catch(t){}M=null}g=!1,N=new X.DatabaseClosed,R&&s(N),Q=new Nn(function(n){u=n}),H=new Nn(function(n,t){s=t})},this.delete=function(){var t=arguments.length>0;return new Nn(function(e,r){if(t)throw new X.InvalidArgument("Arguments not allowed in db.delete()");function i(){W.close();var t=P.deleteDatabase(n);t.onsuccess=et(function(){$||jt.dbnames.delete(n).catch(en),e()}),t.onerror=Ut(r),t.onblocked=In}R?Q.then(i):i()})},this.backendDB=function(){return M},this.isOpen=function(){return null!==M},this.hasBeenClosed=function(){return N&&N instanceof X.DatabaseClosed},this.hasFailed=function(){return null!==N},this.dynamicallyOpened=function(){return Y},this.name=n,c(this,{tables:{get:function(){return t(F).map(function(n){return F[n]})}}}),this.on=Pt(this,"populate","blocked","versionchange",{ready:[ln,en]}),this.on.ready.subscribe=y(this.on.ready.subscribe,function(n){return function(t,e){Mt.vip(function(){z?(N||Nn.resolve().then(t),e&&n(t)):V?(V.push(t),e&&n(t)):(n(t),e||n(function n(){W.on.ready.unsubscribe(t),W.on.ready.unsubscribe(n)}))})}}),this.transaction=function(){var n=function(n,t,e){var r=arguments.length;if(r<2)throw new X.InvalidArgument("Too few arguments");var i=new Array(r-1);for(;--r;)i[r-1]=arguments[r];e=i.pop();var o=A(i);return[n,o,e]}.apply(this,arguments);return this._transaction.apply(this,n)},this._transaction=function(n,t,e){var r=Tn.trans;r&&r.db===W&&-1===n.indexOf("!")||(r=null);var i=-1!==n.indexOf("?");n=n.replace("!","").replace("?","");try{var o=t.map(function(n){var t=n instanceof fn?n.name:n;if("string"!=typeof t)throw new TypeError("Invalid table argument to Dexie.transaction(). Only Table or String are allowed");return t});if("r"==n||"readonly"==n)n="readonly";else{if("rw"!=n&&n!=L)throw new X.InvalidArgument("Invalid transaction mode: "+n);n=L}if(r){if("readonly"===r.mode&&n===L){if(!i)throw new X.SubTransaction("Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY");r=null}r&&o.forEach(function(n){if(r&&-1===r.storeNames.indexOf(n)){if(!i)throw new X.SubTransaction("Table "+n+" not included in parent transaction.");r=null}}),i&&r&&!r.active&&(r=null)}}catch(a){return r?r._promise(null,function(n,t){t(a)}):xt(a)}return r?r._promise(n,u,"lock"):Tn.trans?mt(Tn.transless,function(){return W._whenReady(u)}):W._whenReady(u);function u(){return Nn.resolve().then(function(){var t,i=Tn.transless||Tn,u=W._createTransaction(n,o,O,r),a={trans:u,transless:i};r?u.idbtrans=r.idbtrans:u.create(),e.constructor===kn&&ft();var c=Nn.follow(function(){if(t=e.call(u,u))if(t.constructor===_n){var n=lt.bind(null,null);t.then(n,n)}else"function"==typeof t.next&&"function"==typeof t.throw&&(t=Wt(t))},a);return(t&&"function"==typeof t.then?Nn.resolve(t).then(function(n){return u.active?n:xt(new X.PrematureCommit("Transaction committed too early. See http://bit.ly/2kdckMn"))}):c.then(function(){return t})).then(function(n){return r&&u._resolve(),u._completion.then(function(){return n})}).catch(function(n){return u._reject(n),xt(n)})})}},this.table=function(n){if(!a(F,n))throw new X.InvalidTable("Table "+n+" does not exist");return F[n]},c(fn.prototype,{_trans:function(n,t,e){var r=this._tx||Tn.trans;return r&&r.db===W?r===Tn.trans?r._promise(n,t,e):st(function(){return r._promise(n,t,e)},{trans:r,transless:Tn.transless||Tn}):function n(t,e,r){if(z||Tn.letThrough){var i=W._createTransaction(t,e,O);try{i.create()}catch(o){return xt(o)}return i._promise(t,function(n,t){return st(function(){return Tn.trans=i,r(n,t,i)})}).then(function(n){return i._completion.then(function(){return n})})}if(!R){if(!g)return xt(new X.DatabaseClosed);W.open().catch(en)}return Q.then(function(){return n(t,e,r)})}(n,[this.name],t)},_idbstore:function(n,t,e){var r=this.name;return this._trans(n,function(n,e,i){if(-1===i.storeNames.indexOf(r))throw new X.NotFound("Table"+r+" not part of transaction");return t(n,e,i.idbtrans.objectStore(r),i)},e)},get:function(n,t){if(n&&n.constructor===Object)return this.where(n).first(t);var e=this;return this._idbstore("readonly",function(t,r,i){var o=i.get(n);o.onerror=Ut(r),o.onsuccess=et(function(){t(e.hook.reading.fire(o.result))},r)}).then(t)},where:function(n){if("string"==typeof n)return new pn(this,n);if(e(n))return new pn(this,"["+n.join("+")+"]");var r=t(n);if(1===r.length)return this.where(r[0]).equals(n[r[0]]);var i=this.schema.indexes.concat(this.schema.primKey).filter(function(n){return n.compound&&r.every(function(t){return n.keyPath.indexOf(t)>=0})&&n.keyPath.every(function(n){return r.indexOf(n)>=0})})[0];if(i&&Ot!==At)return this.where(i.name).equals(i.keyPath.map(function(t){return n[t]}));i||console.warn("The query "+JSON.stringify(n)+" on "+this.name+" would benefit of a compound index ["+r.join("+")+"]");var o=this.schema.idxByName,u=r.reduce(function(t,e){return[t[0]||o[e],t[0]||!o[e]?Sn(t[1],function(t){return""+k(t,e)==""+n[e]}):t[1]]},[null,null]),a=u[0];return a?this.where(a.name).equals(n[a.keyPath]).filter(u[1]):i?this.filter(u[1]):this.where(r).equals("")},count:function(n){return this.toCollection().count(n)},offset:function(n){return this.toCollection().offset(n)},limit:function(n){return this.toCollection().limit(n)},reverse:function(){return this.toCollection().reverse()},filter:function(n){return this.toCollection().and(n)},each:function(n){return this.toCollection().each(n)},toArray:function(n){return this.toCollection().toArray(n)},orderBy:function(n){return new yn(new pn(this,e(n)?"["+n.join("+")+"]":n))},toCollection:function(){return new yn(new pn(this))},mapToClass:function(n,t){this.schema.mappedClass=n;var e=Object.create(n.prototype);t&&qt(e,t),this.schema.instanceTemplate=e;var r=function(t){if(!t)return t;var e=Object.create(n.prototype);for(var r in t)if(a(t,r))try{e[r]=t[r]}catch(i){}return e};return this.schema.readHook&&this.hook.reading.unsubscribe(this.schema.readHook),this.schema.readHook=r,this.hook("reading",r),n},defineClass:function(n){return this.mapToClass(Mt.defineClass(n),n)},bulkDelete:function(n){return this.hook.deleting.fire===en?this._idbstore(L,function(t,e,r,i){t(dn(r,i,n,!1,en))}):this.where(":id").anyOf(n).delete().then(function(){})},bulkPut:function(n,t){var e=this;return this._idbstore(L,function(r,i,o){if(!o.keyPath&&!e.schema.primKey.auto&&!t)throw new X.InvalidArgument("bulkPut() with non-inbound keys requires keys array in second argument");if(o.keyPath&&t)throw new X.InvalidArgument("bulkPut(): keys argument invalid on tables with inbound keys");if(t&&t.length!==n.length)throw new X.InvalidArgument("Arguments objects and keys must have the same length");if(0===n.length)return r();var u,a,c=function(n){0===s.length?r(n):i(new J(e.name+".bulkPut(): "+s.length+" of "+f+" operations failed",s))},s=[],f=n.length,l=e;if(e.hook.creating.fire===en&&e.hook.updating.fire===en){a=hn(s);for(var h=0,d=n.length;h<d;++h)(u=t?o.put(n[h],t[h]):o.put(n[h])).onerror=a;u.onerror=hn(s,c),u.onsuccess=Vt(c)}else{var v=t||o.keyPath&&n.map(function(n){return k(n,o.keyPath)}),p=v&&b(v,function(t,e){return null!=t&&[t,n[e]]});(v?l.where(":id").anyOf(v.filter(function(n){return null!=n})).modify(function(){this.value=p[this.primKey],p[this.primKey]=null}).catch(G,function(n){s=n.failures}).then(function(){for(var e=[],r=t&&[],i=v.length-1;i>=0;--i){var o=v[i];(null==o||p[o])&&(e.push(n[i]),t&&r.push(o),null!=o&&(p[o]=null))}return e.reverse(),t&&r.reverse(),l.bulkAdd(e,r)}).then(function(n){var t=v[v.length-1];return null!=t?t:n}):l.bulkAdd(n)).then(c).catch(J,function(n){s=s.concat(n.failures),c()}).catch(i)}},"locked")},bulkAdd:function(n,t){var e=this,r=this.hook.creating.fire;return this._idbstore(L,function(i,o,u,a){if(!u.keyPath&&!e.schema.primKey.auto&&!t)throw new X.InvalidArgument("bulkAdd() with non-inbound keys requires keys array in second argument");if(u.keyPath&&t)throw new X.InvalidArgument("bulkAdd(): keys argument invalid on tables with inbound keys");if(t&&t.length!==n.length)throw new X.InvalidArgument("Arguments objects and keys must have the same length");if(0===n.length)return i();function c(n){0===h.length?i(n):o(new J(e.name+".bulkAdd(): "+h.length+" of "+d+" operations failed",h))}var s,f,l,h=[],d=n.length;if(r!==en){var v,p=u.keyPath;f=hn(h,null,!0),l=Rt(null),_(function(){for(var e=0,i=n.length;e<i;++e){v={onerror:null,onsuccess:null};var o=t&&t[e],c=n[e],h=t?o:p?k(c,p):void 0,d=r.call(v,h,c,a);null==h&&null!=d&&(p?x(c=S(c),p,d):o=d),(s=null!=o?u.add(c,o):u.add(c))._hookCtx=v,e<i-1&&(s.onerror=f,v.onsuccess&&(s.onsuccess=l))}},function(n){throw v.onerror&&v.onerror(n),n}),s.onerror=hn(h,c,!0),s.onsuccess=Rt(c)}else{f=hn(h);for(var y=0,m=n.length;y<m;++y)(s=t?u.add(n[y],t[y]):u.add(n[y])).onerror=f;s.onerror=hn(h,c),s.onsuccess=Vt(c)}})},add:function(n,t){var e=this.hook.creating.fire;return this._idbstore(L,function(r,i,o,u){var a={onsuccess:null,onerror:null};if(e!==en){var c=null!=t?t:o.keyPath?k(n,o.keyPath):void 0,s=e.call(a,c,n,u);null==c&&null!=s&&(o.keyPath?x(n,o.keyPath,s):t=s)}try{var f=null!=t?o.add(n,t):o.add(n);f._hookCtx=a,f.onerror=zt(i),f.onsuccess=Rt(function(t){var e=o.keyPath;e&&x(n,e,t),r(t)})}catch(l){throw a.onerror&&a.onerror(l),l}})},put:function(n,t){var e=this,r=this.hook.creating.fire,i=this.hook.updating.fire;if(r!==en||i!==en){var o=this.schema.primKey.keyPath,u=void 0!==t?t:o&&k(n,o);return null==u?this.add(n):(n=S(n),this._trans(L,function(){return e.where(":id").equals(u).modify(function(){this.value=n}).then(function(r){return 0===r?e.add(n,t):u})},"locked"))}return this._idbstore(L,function(e,r,i){var o=void 0!==t?i.put(n,t):i.put(n);o.onerror=Ut(r),o.onsuccess=et(function(t){var r=i.keyPath;r&&x(n,r,t.target.result),e(o.result)})})},delete:function(n){return this.hook.deleting.subscribers.length?this.where(":id").equals(n).delete():this._idbstore(L,function(t,e,r){var i=r.delete(n);i.onerror=Ut(e),i.onsuccess=et(function(){t(i.result)})})},clear:function(){return this.hook.deleting.subscribers.length?this.toCollection().delete():this._idbstore(L,function(n,t,e){var r=e.clear();r.onerror=Ut(t),r.onsuccess=et(function(){n(r.result)})})},update:function(n,r){if("object"!=typeof r||e(r))throw new X.InvalidArgument("Modifications must be an object.");if("object"!=typeof n||e(n))return this.where(":id").equals(n).modify(r);t(r).forEach(function(t){x(n,t,r[t])});var i=k(n,this.schema.primKey.keyPath);return void 0===i?xt(new X.InvalidArgument("Given object does not contain its primary key")):this.where(":id").equals(i).modify(r)}}),c(vn.prototype,{_lock:function(){return m(!Tn.global),++this._reculock,1!==this._reculock||Tn.global||(Tn.lockOwnerFor=this),this},_unlock:function(){if(m(!Tn.global),0==--this._reculock)for(Tn.global||(Tn.lockOwnerFor=null);this._blockedFuncs.length>0&&!this._locked();){var n=this._blockedFuncs.shift();try{mt(n[1],n[0])}catch(t){}}return this},_locked:function(){return this._reculock&&Tn.lockOwnerFor!==this},create:function(n){var t=this;if(!this.mode)return this;if(m(!this.idbtrans),!n&&!M)switch(N&&N.name){case"DatabaseClosedError":throw new X.DatabaseClosed(N);case"MissingAPIError":throw new X.MissingAPI(N.message,N);default:throw new X.OpenFailed(N)}if(!this.active)throw new X.TransactionInactive;return m(null===this._completion._state),(n=this.idbtrans=n||M.transaction(Gt(this.storeNames),this.mode)).onerror=et(function(e){Lt(e),t._reject(n.error)}),n.onabort=et(function(e){Lt(e),t.active&&t._reject(new X.Abort(n.error)),t.active=!1,t.on("abort").fire(e)}),n.oncomplete=et(function(){t.active=!1,t._resolve()}),this},_promise:function(n,t,e){var r=this;if(n===L&&this.mode!==L)return xt(new X.ReadOnly("Transaction is readonly"));if(!this.active)return xt(new X.TransactionInactive);if(this._locked())return new Nn(function(i,o){r._blockedFuncs.push([function(){r._promise(n,t,e).then(i,o)},Tn])});if(e)return st(function(){var n=new Nn(function(n,e){r._lock();var i=t(n,e,r);i&&i.then&&i.then(n,e)});return n.finally(function(){return r._unlock()}),n._lib=!0,n});var i=new Nn(function(n,e){var i=t(n,e,r);i&&i.then&&i.then(n,e)});return i._lib=!0,i},_root:function(){return this.parent?this.parent._root():this},waitFor:function(n){var t=this._root();if(n=Nn.resolve(n),t._waitingFor)t._waitingFor=t._waitingFor.then(function(){return n});else{t._waitingFor=n,t._waitingQueue=[];var e=t.idbtrans.objectStore(t.storeNames[0]);!function n(){for(++t._spinCount;t._waitingQueue.length;)t._waitingQueue.shift()();t._waitingFor&&(e.get(-1/0).onsuccess=n)}()}var r=t._waitingFor;return new Nn(function(e,i){n.then(function(n){return t._waitingQueue.push(et(e.bind(null,n)))},function(n){return t._waitingQueue.push(et(i.bind(null,n)))}).finally(function(){t._waitingFor===r&&(t._waitingFor=null)})})},abort:function(){this.active&&this._reject(new X.Abort),this.active=!1},tables:{get:U("Transaction.tables",function(){return F})},table:function(n){return new fn(n,W.table(n).schema,this)}}),c(pn.prototype,function(){function n(n,t,e){var r=n instanceof pn?new yn(n):n;return r._ctx.error=e?new e(t):new TypeError(t),r}function t(n){return new yn(n,function(){return E.only("")}).limit(0)}function e(n,t,e,r,i,o){for(var u=Math.min(n.length,r.length),a=-1,c=0;c<u;++c){var s=t[c];if(s!==r[c])return i(n[c],e[c])<0?n.substr(0,c)+e[c]+e.substr(c+1):i(n[c],r[c])<0?n.substr(0,c)+r[c]+e.substr(c+1):a>=0?n.substr(0,a)+t[a]+e.substr(a+1):null;i(n[c],s)<0&&(a=c)}return u<r.length&&"next"===o?n+e.substr(n.length):u<n.length&&"prev"===o?n.substr(0,e.length):a<0?null:n.substr(0,a)+r[a]+e.substr(a+1)}function r(t,r,i,o){var u,a,c,s,f,l,h,d=i.length;if(!i.every(function(n){return"string"==typeof n}))return n(t,It);function v(n){u=function(n){return"next"===n?function(n){return n.toUpperCase()}:function(n){return n.toLowerCase()}}(n),a=function(n){return"next"===n?function(n){return n.toLowerCase()}:function(n){return n.toUpperCase()}}(n),c="next"===n?An:On;var t=i.map(function(n){return{lower:a(n),upper:u(n)}}).sort(function(n,t){return c(n.lower,t.lower)});s=t.map(function(n){return n.upper}),f=t.map(function(n){return n.lower}),l=n,h="next"===n?"":o}v("next");var p=new yn(t,function(){return E.bound(s[0],f[d-1]+o)});p._ondirectionchange=function(n){v(n)};var y=0;return p._addAlgorithm(function(n,t,i){var o=n.key;if("string"!=typeof o)return!1;var u=a(o);if(r(u,f,y))return!0;for(var v=null,p=y;p<d;++p){var m=e(o,u,s[p],f[p],c,l);null===m&&null===v?y=p+1:(null===v||c(v,m)>0)&&(v=m)}return t(null!==v?function(){n.continue(v+h)}:i),!1}),p}return{between:function(e,r,i,o){i=!1!==i,o=!0===o;try{return xn(e,r)>0||0===xn(e,r)&&(i||o)&&(!i||!o)?t(this):new yn(this,function(){return E.bound(e,r,!i,!o)})}catch(u){return n(this,Dt)}},equals:function(n){return new yn(this,function(){return E.only(n)})},above:function(n){return new yn(this,function(){return E.lowerBound(n,!0)})},aboveOrEqual:function(n){return new yn(this,function(){return E.lowerBound(n)})},below:function(n){return new yn(this,function(){return E.upperBound(n,!0)})},belowOrEqual:function(n){return new yn(this,function(){return E.upperBound(n)})},startsWith:function(t){return"string"!=typeof t?n(this,It):this.between(t,t+At,!0,!0)},startsWithIgnoreCase:function(n){return""===n?this.startsWith(n):r(this,function(n,t){return 0===n.indexOf(t[0])},[n],At)},equalsIgnoreCase:function(n){return r(this,function(n,t){return n===t[0]},[n],"")},anyOfIgnoreCase:function(){var n=T.apply(K,arguments);return 0===n.length?t(this):r(this,function(n,t){return-1!==t.indexOf(n)},n,"")},startsWithAnyOfIgnoreCase:function(){var n=T.apply(K,arguments);return 0===n.length?t(this):r(this,function(n,t){return t.some(function(t){return 0===n.indexOf(t)})},n,At)},anyOf:function(){var e=T.apply(K,arguments),r=jn;try{e.sort(r)}catch(u){return n(this,Dt)}if(0===e.length)return t(this);var i=new yn(this,function(){return E.bound(e[0],e[e.length-1])});i._ondirectionchange=function(n){r="next"===n?jn:En,e.sort(r)};var o=0;return i._addAlgorithm(function(n,t,i){for(var u=n.key;r(u,e[o])>0;)if(++o===e.length)return t(i),!1;return 0===r(u,e[o])||(t(function(){n.continue(e[o])}),!1)}),i},notEqual:function(n){return this.inAnyRange([[St,n],[n,Ot]],{includeLowers:!1,includeUppers:!1})},noneOf:function(){var t=T.apply(K,arguments);if(0===t.length)return new yn(this);try{t.sort(jn)}catch(r){return n(this,Dt)}var e=t.reduce(function(n,t){return n?n.concat([[n[n.length-1][1],t]]):[[St,t]]},null);return e.push([t[t.length-1],Ot]),this.inAnyRange(e,{includeLowers:!1,includeUppers:!1})},inAnyRange:function(e,r){if(0===e.length)return t(this);if(!e.every(function(n){return void 0!==n[0]&&void 0!==n[1]&&jn(n[0],n[1])<=0}))return n(this,"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower",X.InvalidArgument);var i=!r||!1!==r.includeLowers,o=r&&!0===r.includeUppers;var u,a=jn;function c(n,t){return a(n[0],t[0])}try{(u=e.reduce(function(n,t){for(var e=0,r=n.length;e<r;++e){var i=n[e];if(xn(t[0],i[1])<0&&xn(t[1],i[0])>0){i[0]=(o=i[0],u=t[0],xn(o,u)<0?o:u),i[1]=Pn(i[1],t[1]);break}}var o,u;return e===r&&n.push(t),n},[])).sort(c)}catch(v){return n(this,Dt)}var s=0,f=o?function(n){return jn(n,u[s][1])>0}:function(n){return jn(n,u[s][1])>=0},l=i?function(n){return En(n,u[s][0])>0}:function(n){return En(n,u[s][0])>=0};var h=f,d=new yn(this,function(){return E.bound(u[0][0],u[u.length-1][1],!i,!o)});return d._ondirectionchange=function(n){"next"===n?(h=f,a=jn):(h=l,a=En),u.sort(c)},d._addAlgorithm(function(n,t,e){for(var r=n.key;h(r);)if(++s===u.length)return t(e),!1;return!!function(n){return!f(n)&&!l(n)}(r)||0!==xn(r,u[s][1])&&0!==xn(r,u[s][0])&&(t(function(){a===jn?n.continue(u[s][0]):n.continue(u[s][1])}),!1)}),d},startsWithAnyOf:function(){var e=T.apply(K,arguments);return e.every(function(n){return"string"==typeof n})?0===e.length?t(this):this.inAnyRange(e.map(function(n){return[n,n+At]})):n(this,"startsWithAnyOf() only works with strings")}}}),c(yn.prototype,function(){function n(n,t){n.filter=Sn(n.filter,t)}function e(n,t,e){var r=n.replayFilter;n.replayFilter=r?function(){return Sn(r(),t())}:t,n.justLimit=e&&!r}function r(n,t){if(n.isPrimKey)return t;var e=n.table.schema.idxByName[n.index];if(!e)throw new X.Schema("KeyPath "+n.index+" on object store "+t.name+" is not indexed");return t.index(e.name)}function o(n,t){var e=r(n,t);return n.keysOnly&&"openKeyCursor"in e?e.openKeyCursor(n.range||null,n.dir+n.unique):e.openCursor(n.range||null,n.dir+n.unique)}function u(n,t,e,r,i){var u=n.replayFilter?Sn(n.filter,n.replayFilter()):n.filter;n.or?function(){var c={},s=0;function f(){2==++s&&e()}function l(n,e,i){if(!u||u(e,i,f,r)){var o=e.primaryKey,s=""+o;"[object ArrayBuffer]"===s&&(s=""+new Uint8Array(o)),a(c,s)||(c[s]=!0,t(n,e,i))}}n.or._iterate(l,f,r,i),wn(o(n,i),n.algorithm,l,f,r,!n.keysOnly&&n.valueMapper)}():wn(o(n,i),Sn(n.algorithm,u),t,e,r,!n.keysOnly&&n.valueMapper)}return{_read:function(n,t){var e=this._ctx;return e.error?e.table._trans(null,xt.bind(null,e.error)):e.table._idbstore("readonly",n).then(t)},_write:function(n){var t=this._ctx;return t.error?t.table._trans(null,xt.bind(null,t.error)):t.table._idbstore(L,n,"locked")},_addAlgorithm:function(n){var t=this._ctx;t.algorithm=Sn(t.algorithm,n)},_iterate:function(n,t,e,r){return u(this._ctx,n,t,e,r)},clone:function(n){var t=Object.create(this.constructor.prototype),e=Object.create(this._ctx);return n&&i(e,n),t._ctx=e,t},raw:function(){return this._ctx.valueMapper=null,this},each:function(n){var t=this._ctx;return this._read(function(e,r,i){u(t,n,e,r,i)})},count:function(n){var t=this._ctx;if(mn(t,!0))return this._read(function(n,e,i){var o=r(t,i),u=t.range?o.count(t.range):o.count();u.onerror=Ut(e),u.onsuccess=function(e){n(Math.min(e.target.result,t.limit))}},n);var e=0;return this._read(function(n,r,i){u(t,function(){return++e,!1},function(){n(e)},r,i)},n)},sortBy:function(n,t){var e=n.split(".").reverse(),r=e[0],i=e.length-1;function o(n,t){return t?o(n[e[t]],t-1):n[r]}var u="next"===this._ctx.dir?1:-1;function a(n,t){var e=o(n,i),r=o(t,i);return e<r?-u:e>r?u:0}return this.toArray(function(n){return n.sort(a)}).then(t)},toArray:function(n){var t=this._ctx;return this._read(function(n,e,i){if(l&&"next"===t.dir&&mn(t,!0)&&t.limit>0){var o=t.table.hook.reading.fire,a=r(t,i),c=t.limit<1/0?a.getAll(t.range,t.limit):a.getAll(t.range);c.onerror=Ut(e),c.onsuccess=Vt(o===rn?n:function(t){try{n(t.map(o))}catch(r){e(r)}})}else{var s=[];u(t,function(n){s.push(n)},function(){n(s)},e,i)}},n)},offset:function(n){var t=this._ctx;return n<=0?this:(t.offset+=n,mn(t)?e(t,function(){var t=n;return function(n,e){return 0===t||(1===t?(--t,!1):(e(function(){n.advance(t),t=0}),!1))}}):e(t,function(){var t=n;return function(){return--t<0}}),this)},limit:function(n){return this._ctx.limit=Math.min(this._ctx.limit,n),e(this._ctx,function(){var t=n;return function(n,e,r){return--t<=0&&e(r),t>=0}},!0),this},until:function(t,e){return n(this._ctx,function(n,r,i){return!t(n.value)||(r(i),e)}),this},first:function(n){return this.limit(1).toArray(function(n){return n[0]}).then(n)},last:function(n){return this.reverse().first(n)},filter:function(t){var e,r;return n(this._ctx,function(n){return t(n.value)}),e=this._ctx,r=t,e.isMatch=Sn(e.isMatch,r),this},and:function(n){return this.filter(n)},or:function(n){return new pn(this._ctx.table,n,this)},reverse:function(){return this._ctx.dir="prev"===this._ctx.dir?"next":"prev",this._ondirectionchange&&this._ondirectionchange(this._ctx.dir),this},desc:function(){return this.reverse()},eachKey:function(n){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each(function(t,e){n(e.key,e)})},eachUniqueKey:function(n){return this._ctx.unique="unique",this.eachKey(n)},eachPrimaryKey:function(n){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each(function(t,e){n(e.primaryKey,e)})},keys:function(n){var t=this._ctx;t.keysOnly=!t.isMatch;var e=[];return this.each(function(n,t){e.push(t.key)}).then(function(){return e}).then(n)},primaryKeys:function(n){var t=this._ctx;if(l&&"next"===t.dir&&mn(t,!0)&&t.limit>0)return this._read(function(n,e,i){var o=r(t,i),u=t.limit<1/0?o.getAllKeys(t.range,t.limit):o.getAllKeys(t.range);u.onerror=Ut(e),u.onsuccess=Vt(n)}).then(n);t.keysOnly=!t.isMatch;var e=[];return this.each(function(n,t){e.push(t.primaryKey)}).then(function(){return e}).then(n)},uniqueKeys:function(n){return this._ctx.unique="unique",this.keys(n)},firstKey:function(n){return this.limit(1).keys(function(n){return n[0]}).then(n)},lastKey:function(n){return this.reverse().firstKey(n)},distinct:function(){var t=this._ctx,e=t.index&&t.table.schema.idxByName[t.index];if(!e||!e.multi)return this;var r={};return n(this._ctx,function(n){var t=n.primaryKey.toString(),e=a(r,t);return r[t]=!0,!e}),this},modify:function(n){var e=this,r=this._ctx.table.hook,o=r.updating.fire,u=r.deleting.fire;return this._write(function(r,c,s,f){var l;if("function"==typeof n)l=o===en&&u===en?n:function(e){var r=S(e);if(!1===n.call(this,e,this))return!1;if(a(this,"value")){var i=D(r,this.value),c=o.call(this,i,this.primKey,r,f);c&&(e=this.value,t(c).forEach(function(n){x(e,n,c[n])}))}else u.call(this,this.primKey,e,f)};else if(o===en){var h=t(n),d=h.length;l=function(t){for(var e=!1,r=0;r<d;++r){var i=h[r],o=n[i];k(t,i)!==o&&(x(t,i,o),e=!0)}return e}}else{var v=n;n=j(v),l=function(e){var r=!1,u=o.call(this,n,this.primKey,S(e),f);return u&&i(n,u),t(n).forEach(function(t){var i=n[t];k(e,t)!==i&&(x(e,t,i),r=!0)}),u&&(n=j(v)),r}}var p=0,y=0,m=!1,g=[],b=[],w=null;function P(n){return n&&(g.push(n),b.push(w)),c(new G("Error modifying one or more objects",g,y,b))}function E(){m&&y+g.length===p&&(g.length>0?P():r(y))}e.clone().raw()._iterate(function(n,t){w=t.primaryKey;var e={primKey:t.primaryKey,value:n,onsuccess:null,onerror:null};function r(n){return g.push(n),b.push(e.primKey),E(),!0}if(!1!==l.call(e,n,e)){var i=!a(e,"value");++p,_(function(){var n=i?t.delete():t.update(e.value);n._hookCtx=e,n.onerror=zt(r),n.onsuccess=Rt(function(){++y,E()})},r)}else e.onsuccess&&e.onsuccess(e.value)},function(){m=!0,E()},P,s)})},delete:function(){var n=this,t=this._ctx,e=t.range,r=t.table.hook.deleting.fire,i=r!==en;if(!i&&mn(t)&&(t.isPrimKey&&!Bt||!e))return this._write(function(n,t,r){var i=Ut(t),o=e?r.count(e):r.count();o.onerror=i,o.onsuccess=function(){var u=o.result;_(function(){var t=e?r.delete(e):r.clear();t.onerror=i,t.onsuccess=function(){return n(u)}},function(n){return t(n)})}});var o=i?2e3:1e4;return this._write(function(e,u,a,c){var s=0,f=n.clone({keysOnly:!t.isMatch&&!i}).distinct().limit(o).raw(),l=[],h=function(){return f.each(i?function(n,t){l.push([t.primaryKey,t.value])}:function(n,t){l.push(t.primaryKey)}).then(function(){return i?l.sort(function(n,t){return jn(n[0],t[0])}):l.sort(jn),dn(a,c,l,i,r)}).then(function(){var n=l.length;return s+=n,l=[],n<o?s:h()})};e(h())})}}}),i(this,{Collection:yn,Table:fn,Transaction:vn,Version:Z,WhereClause:pn}),W.on("versionchange",function(n){n.newVersion>0?console.warn("Another connection wants to upgrade database '"+W.name+"'. Closing db now to resume the upgrade."):console.warn("Another connection wants to delete database '"+W.name+"'. Closing db now to resume the delete request."),W.close()}),W.on("blocked",function(n){!n.newVersion||n.newVersion<n.oldVersion?console.warn("Dexie.delete('"+W.name+"') was blocked"):console.warn("Upgrade '"+W.name+"' blocked by other connection holding version "+n.oldVersion/10)}),v.forEach(function(n){n(W)})}function Nt(n){if("function"==typeof n)return new n;if(e(n))return[Nt(n[0])];if(n&&"object"==typeof n){var t={};return qt(t,n),t}return n}function qt(n,e){return t(e).forEach(function(t){var r=Nt(e[t]);n[t]=r}),n}function Rt(n){return et(function(t){var e=t.target,r=e._hookCtx,i=r.value||e.result,o=r&&r.onsuccess;o&&o(i),n&&n(i)},n)}function Ut(n){return et(function(t){return Lt(t),n(t.target.error),!1})}function Vt(n){return et(function(t){n(t.target.result)})}function zt(n){return et(function(t){var e=t.target,r=e.error,i=e._hookCtx,o=i&&i.onerror;return o&&o(r),Lt(t),n(r),!1})}function Lt(n){n.stopPropagation&&n.stopPropagation(),n.preventDefault&&n.preventDefault()}function Wt(n){var t=function(t){return n.next(t)},r=o(t),i=o(function(t){return n.throw(t)});function o(n){return function(t){var o=n(t),u=o.value;return o.done?u:u&&"function"==typeof u.then?u.then(r,i):e(u)?Nn.all(u).then(r,i):r(u)}}return o(t)()}function Qt(n,t,e,r,i,o,u){this.name=n,this.keyPath=t,this.unique=e,this.multi=r,this.auto=i,this.compound=o,this.dotted=u;var a="string"==typeof t?t:t&&"["+[].join.call(t,"+")+"]";this.src=(e?"&":"")+(r?"*":"")+(i?"++":"")+a}function Ht(n,t,e,r){this.name=n,this.primKey=t||new Qt,this.indexes=e||[new Qt],this.instanceTemplate=r,this.mappedClass=null,this.idxByName=b(e,function(n){return[n.name,n]})}function Gt(n){return 1===n.length?n[0]:n}function Jt(n){var t=n&&(n.getDatabaseNames||n.webkitGetDatabaseNames);return t&&t.bind(n)}F(B,Ft),c(Mt,tn),c(Mt,{delete:function(n){var t=new Mt(n),e=t.delete();return e.onblocked=function(n){return t.on("blocked",n),this},e},exists:function(n){return new Mt(n).open().then(function(n){return n.close(),!0}).catch(Mt.NoSuchDatabaseError,function(){return!1})},getDatabaseNames:function(n){var t=Jt(Mt.dependencies.indexedDB);return t?new Nn(function(n,e){var r=t();r.onsuccess=function(t){n(p(t.target.result,0))},r.onerror=Ut(e)}).then(n):jt.dbnames.toCollection().primaryKeys(n)},defineClass:function(){return function(n){n&&i(this,n)}},applyStructure:qt,ignoreTransaction:function(n){return Tn.trans?mt(Tn.transless,n):n()},vip:function(n){return st(function(){return Tn.letThrough=!0,n()})},async:function(n){return function(){try{var t=Wt(n.apply(this,arguments));return t&&"function"==typeof t.then?t:Nn.resolve(t)}catch(e){return xt(e)}}},spawn:function(n,t,e){try{var r=Wt(n.apply(e,t||[]));return r&&"function"==typeof r.then?r:Nn.resolve(r)}catch(i){return xt(i)}},currentTransaction:{get:function(){return Tn.trans||null}},waitFor:function(n,t){var e=Nn.resolve("function"==typeof n?Mt.ignoreTransaction(n):n).timeout(t||6e4);return Tn.trans?Tn.trans.waitFor(e):e},Promise:Nn,debug:{get:function(){return B},set:function(n){F(n,"dexie"===n?function(){return!0}:Ft)}},derive:l,extend:i,props:c,override:y,Events:Pt,getByKeyPath:k,setByKeyPath:x,delByKeyPath:P,shallowClone:j,deepClone:S,getObjectDiff:D,asap:g,maxKey:Ot,minKey:St,addons:[],connections:Ct,MultiModifyError:X.Modify,errnames:Y,IndexSpec:Qt,TableSchema:Ht,dependencies:function(){try{return{indexedDB:r.indexedDB||r.mozIndexedDB||r.webkitIndexedDB||r.msIndexedDB,IDBKeyRange:r.IDBKeyRange||r.webkitIDBKeyRange}}catch(n){return{indexedDB:null,IDBKeyRange:null}}}(),semVer:Et,version:Et.split(".").map(function(n){return parseInt(n)}).reduce(function(n,t,e){return n+t/Math.pow(10,2*e)}),default:Mt,Dexie:Mt}),Nn.rejectionMapper=nn,(jt=new Mt("__dbnames")).version(1).stores({dbnames:"name"}),function(){try{void 0!==typeof localStorage&&void 0!==r.document&&(JSON.parse(localStorage.getItem("Dexie.DatabaseNames")||"[]").forEach(function(n){return jt.dbnames.put({name:n}).catch(en)}),localStorage.removeItem("Dexie.DatabaseNames"))}catch(n){}}();var Yt=Mt;exports.default=Yt;
},{}],"DrS6":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.groupFn=exports.groupCollapsedFn=exports.setLogLevel=exports.getLogLevel=exports.LOG_LEVELS=void 0,exports.LOG_LEVELS={ERROR:40,WARN:30,INFO:20,DEBUG:10,NOTSET:0};var r=exports.LOG_LEVELS.INFO;try{var o=window.localStorage.getItem("__fploglevel");if(o&&/^\d+$/.test(o)){var t=parseInt(o,10);r=t}}catch(u){}exports.getLogLevel=function(){return r},exports.setLogLevel=function(o){return r=o};var e=function(o,t,e){if(!(o<r)){e&&console[e]||(e=o<=exports.LOG_LEVELS.DEBUG?"log":o<=exports.LOG_LEVELS.INFO?"info":o<=exports.LOG_LEVELS.WARN?"warn":"error");try{(console[e]||console.log).apply(console,t)}catch(u){}}},n=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.DEBUG,r)};exports.default=n,n.debug=n,n.info=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.INFO,r)},n.warn=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.WARN,r)},n.error=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.ERROR,r)},n.group=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.DEBUG,r,"group")},n.groupCollapsed=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.DEBUG,r,"groupCollapsed")},n.groupEnd=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e(exports.LOG_LEVELS.DEBUG,r,"groupEnd")},n.j=function(r){if(void 0===r)return"undefined";try{return JSON.stringify(r)}catch(u){return r}},exports.groupCollapsedFn=function(r,o){return exports.groupFn(r,o,!0)},exports.groupFn=function(r,o,t){return function(){for(var e=[],L=0;L<arguments.length;L++)e[L]=arguments[L];n[!0===t?"groupCollapsed":"group"](r);try{var s=o.apply(this,e);if(p(s)){var f=!1;return s.then(function(r){return f=!0,n.groupEnd(),r}).catch(function(r){throw f||(f=!0,n.groupEnd()),r})}return n.groupEnd(),s}catch(u){throw n.groupEnd(),u}}};var p=function(r){return null!==r&&"object"==typeof r&&"function"==typeof r.then&&"function"==typeof r.catch};
},{}],"sBdG":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=r(require("./log"));function r(e){return e&&e.__esModule?e:{default:e}}const t=function(){window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem;let r=chrome.extension.getURL("/"),t={absPath:(e,t)=>{return`filesystem:${r}${t?"temporary":"persistent"}${e}`},requestFs:function(e,r){void 0===r&&"boolean"==typeof e&&(r=e,e=null),e=e||1048576;let t=!0===r?window.TEMPORARY:window.PERSISTENT;return new Promise((r,n)=>{window.requestFileSystem(t,e,e=>r(e),e=>n(e))})},requestFsError:function(r){e.default.error("ERROR requesting filesystem",r);showError("Unable to access filesystem","Something went wrong accessing the filesystem. Check your browser settings.",!0,!0)},getDirectory:function(e,r,t){let n=void 0===e.root?e:e.root;return new Promise((e,o)=>{n.getDirectory(r,t,r=>e(r),e=>o(e))})},getFile:function(e,r,t){let n=void 0===e.root?e:e.root;return new Promise((e,o)=>{n.getFile(r,t,r=>e(r),e=>o(e))})},getFiles:function(r,n,o){return Promise.all(n.map(n=>{let i=t.getFile(r,n);return!0===o&&(i=i.catch(r=>{e.default.error("Ignoring not found file:",r)})),i})).then(e=>e.filter(e=>void 0!==e))},writeFile:function(e,r){return new Promise((t,n)=>{e.createWriter(function(o){o.onwriteend=(()=>t(e)),o.onerror=(e=>n(e)),o.write(r)},n)})},moveFile:function(e,r,t){return new Promise((n,o)=>{e.moveTo(r,t,e=>n(e),o)})},removeFile:function(e){return new Promise((r,t)=>{e.remove(()=>r(),e=>t(e))})},lookupFiles:function(e,r){let n=void 0===e.root?e:e.root;return new Promise((e,o)=>{let i=n.createReader(),l=[];!function n(){i.readEntries(o=>{o.length?(l=l.concat(function(e){return Array.prototype.slice.call(e||[],0)}(o)),n()):(l.sort(),e(!0===r?l:t.filterToFiles(l)))},o)}()})},filterToFiles:function(e){return e.filter(e=>e.isFile)},loadMetadata:function(e){let r=e.map(e=>t.getMetadata(e));return Promise.all(r)},getMetadata:function(r){return new Promise((t,n)=>{r.getMetadata(function(e){r.metadata=e,t(r)},function(){e.default.error("error reading metadata",arguments),r.metadata=null,t(r)})})},rmFiles:function(r,t){return new Promise((n,o)=>{let i=r.length,l=[],s=[],u={succeeded:l,failed:s};function a(e,r,o){(o?l:s).push(e),t(e,r,i,o),l.length+s.length===i&&n(u)}0===i&&n(u),r.forEach(function(r,t){(0,e.default)("FILE",r,t),r.isFile?r.remove(()=>a(r,t,!0),()=>a(r,t,!1)):a(r,t,!1)})})},clearAllFiles:function(r,n){return new Promise((o,i)=>{n=n||function(){},t.lookupFiles(r).then(e=>t.rmFiles(e)).catch(r=>{let t=function(e){let r="";switch(e.code){case FileError.QUOTA_EXCEEDED_ERR:r="QUOTA_EXCEEDED_ERR";break;case FileError.NOT_FOUND_ERR:r="NOT_FOUND_ERR";break;case FileError.SECURITY_ERR:r="SECURITY_ERR";break;case FileError.INVALID_MODIFICATION_ERR:r="INVALID_MODIFICATION_ERR";break;case FileError.INVALID_STATE_ERR:r="INVALID_STATE_ERR";break;default:r="Unknown Error"}return r}(r);e.default.error("Encountered error",t),i(r)})})},_listeners:[],onUpdated:function(e){t._listeners.push(e)},triggerUpdated:function(e){e=e||{},t._listeners.forEach(r=>r(e))}};return t}();window._FSAPI=t;var n=t;exports.default=n;
},{"./log":"DrS6"}],"UTVA":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getImageFormat=exports.imageFormatsArray=void 0;var e={key:"png",ext:".png",canvas:"image/png",jspdf:"PNG",capture:{format:"png",quality:100}},a={key:"jpg",ext:".jpg",canvas:"image/jpeg",jspdf:"JPEG",capture:{format:"jpeg",quality:92}},r={png:e,jpg:a};exports.default=r,exports.imageFormatsArray=Object.values(r),exports.getImageFormat=function(e){var a=r[(e||"").toLowerCase()];if(void 0===a){var t=new Error('No format found for: "'+e+'"');throw t.name="InvalidImageFormatKey",t}return a};
},{}],"Mo85":[function(require,module,exports) {
var e={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Ấ":"A","Ắ":"A","Ẳ":"A","Ẵ":"A","Ặ":"A","Æ":"AE","Ầ":"A","Ằ":"A","Ȃ":"A","Ç":"C","Ḉ":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ế":"E","Ḗ":"E","Ề":"E","Ḕ":"E","Ḝ":"E","Ȇ":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ḯ":"I","Ȋ":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ố":"O","Ṍ":"O","Ṓ":"O","Ȏ":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","ấ":"a","ắ":"a","ẳ":"a","ẵ":"a","ặ":"a","æ":"ae","ầ":"a","ằ":"a","ȃ":"a","ç":"c","ḉ":"c","è":"e","é":"e","ê":"e","ë":"e","ế":"e","ḗ":"e","ề":"e","ḕ":"e","ḝ":"e","ȇ":"e","ì":"i","í":"i","î":"i","ï":"i","ḯ":"i","ȋ":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ố":"o","ṍ":"o","ṓ":"o","ȏ":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Ĉ":"C","ĉ":"c","Ċ":"C","ċ":"c","Č":"C","č":"c","C̆":"C","c̆":"c","Ď":"D","ď":"d","Đ":"D","đ":"d","Ē":"E","ē":"e","Ĕ":"E","ĕ":"e","Ė":"E","ė":"e","Ę":"E","ę":"e","Ě":"E","ě":"e","Ĝ":"G","Ǵ":"G","ĝ":"g","ǵ":"g","Ğ":"G","ğ":"g","Ġ":"G","ġ":"g","Ģ":"G","ģ":"g","Ĥ":"H","ĥ":"h","Ħ":"H","ħ":"h","Ḫ":"H","ḫ":"h","Ĩ":"I","ĩ":"i","Ī":"I","ī":"i","Ĭ":"I","ĭ":"i","Į":"I","į":"i","İ":"I","ı":"i","Ĳ":"IJ","ĳ":"ij","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","Ḱ":"K","ḱ":"k","K̆":"K","k̆":"k","Ĺ":"L","ĺ":"l","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ŀ":"L","ŀ":"l","Ł":"l","ł":"l","Ḿ":"M","ḿ":"m","M̆":"M","m̆":"m","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","ŉ":"n","N̆":"N","n̆":"n","Ō":"O","ō":"o","Ŏ":"O","ŏ":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","P̆":"P","p̆":"p","Ŕ":"R","ŕ":"r","Ŗ":"R","ŗ":"r","Ř":"R","ř":"r","R̆":"R","r̆":"r","Ȓ":"R","ȓ":"r","Ś":"S","ś":"s","Ŝ":"S","ŝ":"s","Ş":"S","Ș":"S","ș":"s","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","ț":"t","Ț":"T","Ť":"T","ť":"t","Ŧ":"T","ŧ":"t","T̆":"T","t̆":"t","Ũ":"U","ũ":"u","Ū":"U","ū":"u","Ŭ":"U","ŭ":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ȗ":"U","ȗ":"u","V̆":"V","v̆":"v","Ŵ":"W","ŵ":"w","Ẃ":"W","ẃ":"w","X̆":"X","x̆":"x","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Y̆":"Y","y̆":"y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","ſ":"s","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","Ǎ":"A","ǎ":"a","Ǐ":"I","ǐ":"i","Ǒ":"O","ǒ":"o","Ǔ":"U","ǔ":"u","Ǖ":"U","ǖ":"u","Ǘ":"U","ǘ":"u","Ǚ":"U","ǚ":"u","Ǜ":"U","ǜ":"u","Ứ":"U","ứ":"u","Ṹ":"U","ṹ":"u","Ǻ":"A","ǻ":"a","Ǽ":"AE","ǽ":"ae","Ǿ":"O","ǿ":"o","Þ":"TH","þ":"th","Ṕ":"P","ṕ":"p","Ṥ":"S","ṥ":"s","X́":"X","x́":"x","Ѓ":"Г","ѓ":"г","Ќ":"К","ќ":"к","A̋":"A","a̋":"a","E̋":"E","e̋":"e","I̋":"I","i̋":"i","Ǹ":"N","ǹ":"n","Ồ":"O","ồ":"o","Ṑ":"O","ṑ":"o","Ừ":"U","ừ":"u","Ẁ":"W","ẁ":"w","Ỳ":"Y","ỳ":"y","Ȁ":"A","ȁ":"a","Ȅ":"E","ȅ":"e","Ȉ":"I","ȉ":"i","Ȍ":"O","ȍ":"o","Ȑ":"R","ȑ":"r","Ȕ":"U","ȕ":"u","B̌":"B","b̌":"b","Č̣":"C","č̣":"c","Ê̌":"E","ê̌":"e","F̌":"F","f̌":"f","Ǧ":"G","ǧ":"g","Ȟ":"H","ȟ":"h","J̌":"J","ǰ":"j","Ǩ":"K","ǩ":"k","M̌":"M","m̌":"m","P̌":"P","p̌":"p","Q̌":"Q","q̌":"q","Ř̩":"R","ř̩":"r","Ṧ":"S","ṧ":"s","V̌":"V","v̌":"v","W̌":"W","w̌":"w","X̌":"X","x̌":"x","Y̌":"Y","y̌":"y","A̧":"A","a̧":"a","B̧":"B","b̧":"b","Ḑ":"D","ḑ":"d","Ȩ":"E","ȩ":"e","Ɛ̧":"E","ɛ̧":"e","Ḩ":"H","ḩ":"h","I̧":"I","i̧":"i","Ɨ̧":"I","ɨ̧":"i","M̧":"M","m̧":"m","O̧":"O","o̧":"o","Q̧":"Q","q̧":"q","U̧":"U","u̧":"u","X̧":"X","x̧":"x","Z̧":"Z","z̧":"z"},o=Object.keys(e).join("|"),u=new RegExp(o,"g"),a=new RegExp(o,""),A=function(o){return o.replace(u,function(o){return e[o]})},E=function(e){return!!e.match(a)};module.exports=A,module.exports.has=E,module.exports.remove=A;
},{}],"abex":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.genLastErrorFmt=void 0,exports.genLastErrorFmt=function(e){return function(r,t){return Object.assign({name:e,message:r.message},t?{via:t}:null)}};
},{}],"OFfI":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.isAllowedIncognitoAccess=exports.isAllowedFileSchemeAccess=exports.isFileUrl=void 0;var e=require("./chrome.util");const o=(0,e.genLastErrorFmt)("ChromeExtensionError"),s=e=>{if(!e)return!1;try{return"file:"===new URL(e).protocol}catch(o){throw o.message+=` ${typeof e} (${e})`,o}};exports.isFileUrl=s;const r=()=>new Promise((e,s)=>{chrome.extension.isAllowedFileSchemeAccess(r=>{let t=chrome.runtime.lastError;return t?s(o(t,"isAllowedFileSchemeAccess")):e(r)})});exports.isAllowedFileSchemeAccess=r;const t=()=>new Promise((e,s)=>{chrome.extension.isAllowedIncognitoAccess(r=>{let t=chrome.runtime.lastError;return t?s(o(t,"isAllowedIncognitoAccess")):e(r)})});exports.isAllowedIncognitoAccess=t;
},{"./chrome.util":"abex"}],"a7sk":[function(require,module,exports) {
"use strict";var n=this&&this.__awaiter||function(n,t,e,r){return new(e||(e=Promise))(function(o,i){function u(n){try{c(r.next(n))}catch(t){i(t)}}function a(n){try{c(r.throw(n))}catch(t){i(t)}}function c(n){var t;n.done?o(n.value):(t=n.value,t instanceof e?t:new e(function(n){n(t)})).then(u,a)}c((r=r.apply(n,t||[])).next())})},t=this&&this.__generator||function(n,t){var e,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(e)throw new TypeError("Generator is already executing.");for(;u;)try{if(e=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=(o=u.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=t.call(n,u)}catch(a){i=[6,a],r=0}finally{e=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}};function e(n){return n&&"object"==typeof n&&n.then&&"function"==typeof n.then}Object.defineProperty(exports,"__esModule",{value:!0}),exports.canvasToBlob=exports.serialMap=exports.loadImage=void 0,exports.loadImage=function(n){return new Promise(function(t,e){var r=new Image;r.onerror=function(n){return e(n)},r.onload=function(e){return t({img:r,src:n,width:r.width,height:r.height})},r.src=n})},exports.serialMap=function(r,o){return n(void 0,void 0,Promise,function(){var n,i,u;return t(this,function(t){return n=r.length,i=void 0===o,[2,(u=function(t,a){return t>=n?a:e(c=i?r[t]():o(r[t]))?c.then(function(n){return u(t+1,n)}):u(t+1,c);var c})(0,void 0)]})})},exports.canvasToBlob=function(n,t,e){return new Promise(function(r){n.toBlob(function(n){return r(n)},t||"image/png",e||1)})};
},{}],"pPWs":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.loadImage=exports.simpleLoadImage=exports.timeoutWrap=exports.sleep=exports.serialMap=void 0;var e=require("../editor/utils/promise");exports.serialMap=e.serialMap,exports.sleep=function(e,r){return new Promise(function(t){window.setTimeout(function(){return t(r)},e)})},exports.timeoutWrap=function(e,r,t,o){return new Promise(function(n,i){var s;r&&(s=window.setTimeout(function(){var e="Promise timed out after "+r+"ms";t&&(e+=" "+t);var n=new Error(e);(n.name="PromiseTimeout",o)?!1===o(n,r)&&i(n):i(n)},r)),e.then(function(e){window.clearTimeout(s),n(e)}).catch(i)})},exports.simpleLoadImage=function(e){return new Promise(function(r,t){var o=new Image;o.onerror=function(e){return t(e)},o.onload=function(){return r(o)},o.src=e})},exports.loadImage=function(e){return exports.simpleLoadImage(e).then(function(r){return{img:r,src:e,width:r.width,height:r.height}})};
},{"../editor/utils/promise":"a7sk"}],"t52Y":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.downloadAll=exports.downloadUrl=exports.search=exports.download=exports.ERROR_INCOGNITO_NETWORK_NAME=exports.ERROR_NAME=void 0;var r=require("./chrome.util"),o=require("./chrome.extension"),e=require("./promise");const t="ChromeDownloadsError";exports.ERROR_NAME=t;const n="AllowIncognitoNetworkError";exports.ERROR_INCOGNITO_NETWORK_NAME="AllowIncognitoNetworkError";const l=(0,r.genLastErrorFmt)(t),s=r=>new Promise((o,e)=>{chrome.downloads.download(r,r=>{let t=chrome.runtime.lastError;return t?e(l(t,"download")):o(r)})});exports.download=s;const c=r=>new Promise((o,e)=>{chrome.downloads.search(r,r=>{let t=chrome.runtime.lastError;return t?e(l(t,"search")):o(r)})});exports.search=c;const i=(r,e,t,n=!1)=>{let l={url:r,conflictAction:chrome.downloads.FilenameConflictAction.UNIQUIFY};return(t||e)&&(t=t||r.split("/").pop(),e&&(t=`${e}/${t}`),l.filename=t),n&&(l.saveAs=!0),s(l).then(r=>(0,o.isAllowedIncognitoAccess)().then(o=>{if(o)return c({id:r}).then(r=>{let o=r[0];if(o&&"interrupted"===o.state&&"NETWORK_FAILED"===o.error){let r=new Error("Download encountered network failed error with allow incognito");throw r.name="AllowIncognitoNetworkError",r}})}).then(()=>r))};exports.downloadUrl=i;const a=(r,o,t=!1)=>{let n=r.length,l=0,s=0,c=[];return r=r.map(r=>"string"==typeof r?{url:r}:r),(0,e.serialMap)(r,r=>i(r.url,o,r.filename,t).then(o=>{s+=1,c.push({url:r,id:o,success:!0})}).catch(o=>{l+=1,c.push({url:r,error:o,success:!1})})).then(()=>{if(l&&l===n){let r=c[0].error;throw r.message+=` (downloadAll: ${l} of ${n})`,r}return c})};exports.downloadAll=a;
},{"./chrome.util":"abex","./chrome.extension":"OFfI","./promise":"pPWs"}],"WlKW":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.removeDownloads=exports.containsDownloads=exports.requestDownloads=exports.requestIfNeededDownloads=exports.requestIfNeeded=exports.remove=exports.contains=exports.request=exports.PERMISSIONS=void 0;var e=require("./chrome.util"),r=e.genLastErrorFmt("ChromePermissionsError");exports.PERMISSIONS={DOWNLOADS:"downloads"},exports.request=function(e,o){return new Promise(function(n,s){var u=t(e,o);chrome.permissions.request(u,function(e){var o=chrome.runtime.lastError;return o?s(r(o,"request")):n(e)})})},exports.contains=function(e,o){return new Promise(function(n,s){var u=t(e,o);chrome.permissions.contains(u,function(e){var o=chrome.runtime.lastError;return o?s(r(o,"contains")):n(e)})})},exports.remove=function(e,o){return new Promise(function(n,s){var u=t(e,o);chrome.permissions.remove(u,function(e){var o=chrome.runtime.lastError;return o?s(r(o,"remove")):n(e)})})},exports.requestIfNeeded=function(e,r){return exports.contains(e,r).then(function(o){return!!o||exports.request(e,r)})},exports.requestIfNeededDownloads=function(){return exports.requestIfNeeded(exports.PERMISSIONS.DOWNLOADS)},exports.requestDownloads=function(){return exports.request(exports.PERMISSIONS.DOWNLOADS)},exports.containsDownloads=function(){return exports.contains(exports.PERMISSIONS.DOWNLOADS)},exports.removeDownloads=function(){return exports.remove(exports.PERMISSIONS.DOWNLOADS)};var o=function(e){return"string"==typeof e?[e]:e},t=function(e,r){return void 0===r&&"object"==typeof e?e:{permissions:e=o(e),origins:r=o(r)}};
},{"./chrome.util":"abex"}],"CRk1":[function(require,module,exports) {
"use strict";function e(e){return document.getElementById(e)}function t(t,n){var i=e(t);i.style.display=n&&"string"==typeof n?n:"block",i.style.visibility="visible"}function n(t){e(t).style.display="none"}function i(t){e(t).style.visibility="hidden"}function o(e,t){return Array.from((t||document).getElementsByClassName(e))}function r(e,t,n){return e.addEventListener(t,n,!1),e}function s(e,t,n){e.addEventListener(t,function i(o){return e.removeEventListener(t,i),n.call(this,o)},!1)}function f(e){var t=0,n=0;if(e!==window)for(var i=e;i;)t+=i.offsetLeft||0,n+=i.offsetTop||0,i=i.offsetParent;return{left:t,top:n}}function u(e,t){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}Object.defineProperty(exports,"__esModule",{value:!0}),exports.$=void 0,exports.$=Object.assign(e,{show:t,hide:n,hidden:i,findClass:o,on:r,once:s,offsets:f,containsElt:u});
},{}],"awqi":[function(require,module,exports) {
"use strict";var e=require("object-assign"),r="function"==typeof Symbol&&Symbol.for,t=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,u=r?Symbol.for("react.strict_mode"):60108,f=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,l=r?Symbol.for("react.context"):60110,i=r?Symbol.for("react.forward_ref"):60112,s=r?Symbol.for("react.suspense"):60113,a=r?Symbol.for("react.memo"):60115,p=r?Symbol.for("react.lazy"):60116,y="function"==typeof Symbol&&Symbol.iterator;function d(e){for(var r="https://reactjs.org/docs/error-decoder.html?invariant="+e,t=1;t<arguments.length;t++)r+="&args[]="+encodeURIComponent(arguments[t]);return"Minified React error #"+e+"; visit "+r+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var v={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h={};function m(e,r,t){this.props=e,this.context=r,this.refs=h,this.updater=t||v}function x(){}function b(e,r,t){this.props=e,this.context=r,this.refs=h,this.updater=t||v}m.prototype.isReactComponent={},m.prototype.setState=function(e,r){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(d(85));this.updater.enqueueSetState(this,e,r,"setState")},m.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},x.prototype=m.prototype;var S=b.prototype=new x;S.constructor=b,e(S,m.prototype),S.isPureReactComponent=!0;var _={current:null},k=Object.prototype.hasOwnProperty,$={key:!0,ref:!0,__self:!0,__source:!0};function g(e,r,n){var o,u={},f=null,c=null;if(null!=r)for(o in void 0!==r.ref&&(c=r.ref),void 0!==r.key&&(f=""+r.key),r)k.call(r,o)&&!$.hasOwnProperty(o)&&(u[o]=r[o]);var l=arguments.length-2;if(1===l)u.children=n;else if(1<l){for(var i=Array(l),s=0;s<l;s++)i[s]=arguments[s+2];u.children=i}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===u[o]&&(u[o]=l[o]);return{$$typeof:t,type:e,key:f,ref:c,props:u,_owner:_.current}}function w(e,r){return{$$typeof:t,type:e.type,key:r,ref:e.ref,props:e.props,_owner:e._owner}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===t}function E(e){var r={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,function(e){return r[e]})}var R=/\/+/g,P=[];function j(e,r,t,n){if(P.length){var o=P.pop();return o.result=e,o.keyPrefix=r,o.func=t,o.context=n,o.count=0,o}return{result:e,keyPrefix:r,func:t,context:n,count:0}}function O(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>P.length&&P.push(e)}function A(e,r,o,u){var f=typeof e;"undefined"!==f&&"boolean"!==f||(e=null);var c=!1;if(null===e)c=!0;else switch(f){case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case t:case n:c=!0}}if(c)return o(u,e,""===r?"."+U(e,0):r),1;if(c=0,r=""===r?".":r+":",Array.isArray(e))for(var l=0;l<e.length;l++){var i=r+U(f=e[l],l);c+=A(f,i,o,u)}else if(null===e||"object"!=typeof e?i=null:i="function"==typeof(i=y&&e[y]||e["@@iterator"])?i:null,"function"==typeof i)for(e=i.call(e),l=0;!(f=e.next()).done;)c+=A(f=f.value,i=r+U(f,l++),o,u);else if("object"===f)throw o=""+e,Error(d(31,"[object Object]"===o?"object with keys {"+Object.keys(e).join(", ")+"}":o,""));return c}function I(e,r,t){return null==e?0:A(e,"",r,t)}function U(e,r){return"object"==typeof e&&null!==e&&null!=e.key?E(e.key):r.toString(36)}function q(e,r){e.func.call(e.context,r,e.count++)}function F(e,r,t){var n=e.result,o=e.keyPrefix;e=e.func.call(e.context,r,e.count++),Array.isArray(e)?L(e,n,t,function(e){return e}):null!=e&&(C(e)&&(e=w(e,o+(!e.key||r&&r.key===e.key?"":(""+e.key).replace(R,"$&/")+"/")+t)),n.push(e))}function L(e,r,t,n,o){var u="";null!=t&&(u=(""+t).replace(R,"$&/")+"/"),I(e,F,r=j(r,u,n,o)),O(r)}var M={current:null};function D(){var e=M.current;if(null===e)throw Error(d(321));return e}var V={ReactCurrentDispatcher:M,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:_,IsSomeRendererActing:{current:!1},assign:e};exports.Children={map:function(e,r,t){if(null==e)return e;var n=[];return L(e,n,null,r,t),n},forEach:function(e,r,t){if(null==e)return e;I(e,q,r=j(null,null,r,t)),O(r)},count:function(e){return I(e,function(){return null},null)},toArray:function(e){var r=[];return L(e,r,null,function(e){return e}),r},only:function(e){if(!C(e))throw Error(d(143));return e}},exports.Component=m,exports.Fragment=o,exports.Profiler=f,exports.PureComponent=b,exports.StrictMode=u,exports.Suspense=s,exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,exports.cloneElement=function(r,n,o){if(null==r)throw Error(d(267,r));var u=e({},r.props),f=r.key,c=r.ref,l=r._owner;if(null!=n){if(void 0!==n.ref&&(c=n.ref,l=_.current),void 0!==n.key&&(f=""+n.key),r.type&&r.type.defaultProps)var i=r.type.defaultProps;for(s in n)k.call(n,s)&&!$.hasOwnProperty(s)&&(u[s]=void 0===n[s]&&void 0!==i?i[s]:n[s])}var s=arguments.length-2;if(1===s)u.children=o;else if(1<s){i=Array(s);for(var a=0;a<s;a++)i[a]=arguments[a+2];u.children=i}return{$$typeof:t,type:r.type,key:f,ref:c,props:u,_owner:l}},exports.createContext=function(e,r){return void 0===r&&(r=null),(e={$$typeof:l,_calculateChangedBits:r,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},exports.createElement=g,exports.createFactory=function(e){var r=g.bind(null,e);return r.type=e,r},exports.createRef=function(){return{current:null}},exports.forwardRef=function(e){return{$$typeof:i,render:e}},exports.isValidElement=C,exports.lazy=function(e){return{$$typeof:p,_ctor:e,_status:-1,_result:null}},exports.memo=function(e,r){return{$$typeof:a,type:e,compare:void 0===r?null:r}},exports.useCallback=function(e,r){return D().useCallback(e,r)},exports.useContext=function(e,r){return D().useContext(e,r)},exports.useDebugValue=function(){},exports.useEffect=function(e,r){return D().useEffect(e,r)},exports.useImperativeHandle=function(e,r,t){return D().useImperativeHandle(e,r,t)},exports.useLayoutEffect=function(e,r){return D().useLayoutEffect(e,r)},exports.useMemo=function(e,r){return D().useMemo(e,r)},exports.useReducer=function(e,r,t){return D().useReducer(e,r,t)},exports.useRef=function(e){return D().useRef(e)},exports.useState=function(e){return D().useState(e)},exports.version="16.13.1";
},{"object-assign":"J4Nk"}],"n8MK":[function(require,module,exports) {
"use strict";module.exports=require("./cjs/react.production.min.js");
},{"./cjs/react.production.min.js":"awqi"}],"QUfo":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.compareVersions=exports.getVersion=void 0;var e=require("react");const r=()=>{return chrome.runtime.getManifest().version||"0.0"};exports.getVersion=r;const t=(e,r)=>{let t=(e||"").split("."),s=(r||"").split("."),n=Math.max(t.length,s.length);for(let o=0;o<n;o++){if(t[o]===s[o]||!t[o]&&!s[o])continue;if(!s[o])return 1;if(!t[o])return-1;return parseInt(t[o])>parseInt(s[o])?1:-1}return 0};exports.compareVersions=t;
},{"react":"n8MK"}],"UWsR":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getIsWindows=exports.getIsMac=exports.noIframes=void 0,exports.noIframes=function(){var e=/Chrome\/([0-9]+)/.exec(navigator.userAgent);return e&&"49"===e[1]},exports.getIsMac=function(){return-1!==navigator.platform.toUpperCase().indexOf("MAC")},exports.getIsWindows=function(){return-1!==navigator.platform.toUpperCase().indexOf("WIN")};
},{}],"RalN":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.sync=exports.local=exports.StoragePromiseAPI=void 0;var r=require("./chrome.util"),e=function(){function e(e){this.areaName=e,this.storageArea=chrome.storage[e],this.formatError=r.genLastErrorFmt("Chrome"+e+"StorageError")}return e.prototype.get=function(r){var e=this;return new Promise(function(t,o){e.storageArea.get(r,function(r){var n=chrome.runtime.lastError;return n?o(e.formatError(n,"get")):t(r)})})},e.prototype.set=function(r){var e=this;return new Promise(function(t,o){e.storageArea.set(r,function(){var r=chrome.runtime.lastError;return r?o(e.formatError(r,"set")):t()})})},e.prototype.remove=function(r){var e=this;return new Promise(function(t,o){e.storageArea.remove(r,function(){var r=chrome.runtime.lastError;return r?o(e.formatError(r,"remove")):t()})})},e}();exports.StoragePromiseAPI=e;var t={local:new e("local"),sync:new e("sync")};exports.default=t,exports.local=t.local,exports.sync=t.sync;
},{"./chrome.util":"abex"}],"M9LT":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=s(require("assert-plus")),t=s(require("./chrome.storage.promise"));function s(e){return e&&e.__esModule?e:{default:e}}const i=t.default.local,a=t.default.sync;class r{constructor(e,t,s){this.namespace=e,this.fields=t||[],this.state={},this.storage=!0===s?a:i,this.areaName=!0===s?"sync":"local",this.fields.forEach(e=>{e.choices&&(e.choices=e.choices.map(e=>"string"==typeof e?{key:e,display:e}:e))});let r={};this.fields.forEach(e=>r[this._key(e.name)]=e),this.namespacedToField=r,this._listeners=[],this.fields.length&&this._startListening()}getField(e){return this.fields.find(t=>t.name===e)}_key(e){return this.namespace+"."+e}_keys(e){if(void 0===e)throw new Error("Cannot pass `undefined` as `keys`!");return"string"==typeof e&&(e=[e]),e.map(e=>this._key(e))}_stripKey(e){return e.substring(this.namespace.length+1)}set(e,t){return this.setObj({[e]:t})}setObj(t){let s={};return Object.entries(t).forEach(([t,i])=>{const a=this._key(t),r=this.namespacedToField[a];(0,e.default)(r,`no field found for ${a} (${t})`),s[a]=this._serializeField(r,i)}),this.storage.set(s).then(()=>{Object.assign(this.state,t)})}get(t){return t=this._keys(t),this.storage.get(t).then(t=>{let s={};return Object.entries(t).forEach(([t,i])=>{const a=this.namespacedToField[t];(0,e.default)(a,`no field found for ${t}`),s[this._stripKey(t)]=this._deserializeField(a,i)}),Object.assign(this.state,s),s})}remove(e){"string"==typeof e&&(e=[e]);const t=this._keys(e);return this.storage.remove(t).then(()=>{e.forEach(e=>{delete this.state[e]})})}clear(){let e=this.fields.map(e=>e.name);return this.remove(e)}load(e=!1){return this._loadProm&&!0!==e||(this._loadProm=this._load()),this._loadProm.then(()=>Object.assign({},this.state))}_load(){let e=this.fields.map(e=>e.name);return this.get(e).then(e=>(this.fields.forEach(t=>{if(void 0===e[t.name])void 0!==t.default&&(e[t.name]="function"==typeof t.default?t.default():t.default);else if(t.process){const s=t.process(e[t.name]).val;e[t.name]=s}}),Object.assign(this.state,e),e))}onUpdate(e){this._listeners.push(e)}_startListening(){return!this._listening&&(this._listening=!0,chrome.storage.onChanged.addListener((e,t)=>{if(t===this.areaName){const t=[];Object.entries(e).forEach(([e,s])=>{const i=this.namespacedToField[e];i&&(t.push(i.name),this.state[i.name]=this._deserializeField(i,s.newValue))}),t.length&&this._listeners.forEach(e=>e(t))}}),!0)}_serializeField(e,t){switch(e.type){case"json":return JSON.stringify(t);default:return t}}_deserializeField(e,t){if(void 0===t&&void 0!==e.default)return"function"==typeof e.default?e.default():e.default;switch(e.type){case"json":return JSON.parse(t);default:return t}}}var n=r;exports.default=n;
},{"assert-plus":"VDee","./chrome.storage.promise":"RalN"}],"qwST":[function(require,module,exports) {
"use strict";var e;Object.defineProperty(exports,"__esModule",{value:!0}),exports.NAMECHANGE_VERSION=exports.GA_VIA_NAV=exports.GA_VIA_DIALOG=exports.GA_VIA_KEYPRESS=exports.DL_TYPE_PDF=exports.DL_TYPE_IMAGE=exports.DEFAULT_PLAN_AMOUNT=exports.EVT_AB_IS_PREMIUM=exports.VALID_WEBSITE_ORIGINS=exports.REGISTER_SELF_URL=exports.INLINE_DEMO_EDITOR_URL=exports.DEMO_URL=exports.WEBCOMM_URL=exports.PREMIUM_URL=exports.IFRAME_SIGNUP_URL=exports.IFRAME_LOGIN_URL=exports.LOGIN_URL=exports.SERVER_ENDPOINT=exports.IS_EDGE=exports.IS_CHROME=exports.CUR_BROWSER_INFO=exports.CUR_BROWSER=exports.BROWSER_EDGE=exports.BROWSER_CHROME=void 0,exports.BROWSER_CHROME="chrome",exports.BROWSER_EDGE="edge";var o=((e={})[exports.BROWSER_CHROME]={name:"Chrome",store:"Chrome Web Store",store_short:"web store",store_url:"https://chrome.google.com/webstore/detail/full-page-screen-capture/fdpohaocaechififmbbbbbknoalclacl",store_reviews_url:"https://chrome.google.com/webstore/detail/full-page-screen-capture/fdpohaocaechififmbbbbbknoalclacl/reviews",browser_protocol:"chrome://"},e[exports.BROWSER_EDGE]={name:"Edge",store:"Edge Add-on Store",store_short:"add-on store",store_url:"https://microsoftedge.microsoft.com/addons/detail/gofullpage/hfaciehifhdcgoolaejkoncjciicbemc",store_reviews_url:"https://microsoftedge.microsoft.com/addons/detail/gofullpage/hfaciehifhdcgoolaejkoncjciicbemc",browser_protocol:"edge://"},e);exports.CUR_BROWSER=exports.BROWSER_CHROME,exports.CUR_BROWSER_INFO=o[exports.CUR_BROWSER],exports.IS_CHROME=exports.CUR_BROWSER===exports.BROWSER_CHROME,exports.IS_EDGE=exports.CUR_BROWSER===exports.BROWSER_EDGE,exports.SERVER_ENDPOINT="https://gofullpage.com",exports.LOGIN_URL=exports.SERVER_ENDPOINT+"/account",exports.IFRAME_LOGIN_URL=exports.SERVER_ENDPOINT+"/connect?signin",exports.IFRAME_SIGNUP_URL=exports.SERVER_ENDPOINT+"/connect",exports.PREMIUM_URL=exports.SERVER_ENDPOINT+"/premium",exports.WEBCOMM_URL=exports.SERVER_ENDPOINT+"/_extcomm",exports.DEMO_URL=exports.SERVER_ENDPOINT+"/demos#editor",exports.INLINE_DEMO_EDITOR_URL=exports.SERVER_ENDPOINT+"/demo-editor-inline",exports.REGISTER_SELF_URL="",exports.VALID_WEBSITE_ORIGINS=["https://localhost:1234","https://gofullpage.com","https://www.gofullpage.com","https://dev.d32cgdvim65k7p.amplifyapp.com","https://master.d32cgdvim65k7p.amplifyapp.com","https://********:1234"],exports.EVT_AB_IS_PREMIUM="EVT_AB_IS_PREMIUM",exports.DEFAULT_PLAN_AMOUNT=1200,exports.DL_TYPE_IMAGE="img",exports.DL_TYPE_PDF="pdf",exports.GA_VIA_KEYPRESS="keypress",exports.GA_VIA_DIALOG="click_dialog",exports.GA_VIA_NAV="nav_btn",exports.NAMECHANGE_VERSION="7.0";
},{}],"fdRK":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.makePremiumUrl=exports.makeLoginUrl=void 0;var e=require("./constants"),t=require("./util");const o=t=>n(e.LOGIN_URL,t);exports.makeLoginUrl=o;const r=t=>n(e.PREMIUM_URL,t);exports.makePremiumUrl=r;const n=(e,o)=>{o=o||window.location.pathname+(window.location.search||"")+(window.location.hash||"");const r=chrome.extension.getURL("/p/index.html")+"?"+(0,t.toQueryString)([{key:"p",val:o}]);return`${e}?${(0,t.toQueryString)([{key:"next",val:r}])}`};
},{"./constants":"qwST","./util":"wT1R"}],"L836":[function(require,module,exports) {
var define;
var global = arguments[3];
var e,r=arguments[3];!function(r,t){"object"==typeof exports?module.exports=t(r):"function"==typeof e&&e.amd?e([],t.bind(r,r)):t(r)}(void 0!==r?r:this,function(e){if(e.CSS&&e.CSS.escape)return e.CSS.escape;var r=function(e){if(0==arguments.length)throw new TypeError("`CSS.escape` requires an argument.");for(var r,t=String(e),n=t.length,o=-1,S="",a=t.charCodeAt(0);++o<n;)0!=(r=t.charCodeAt(o))?S+=r>=1&&r<=31||127==r||0==o&&r>=48&&r<=57||1==o&&r>=48&&r<=57&&45==a?"\\"+r.toString(16)+" ":(0!=o||1!=n||45!=r)&&(r>=128||45==r||95==r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122)?t.charAt(o):"\\"+t.charAt(o):S+="�";return S};return e.CSS||(e.CSS={}),e.CSS.escape=r,r});
},{}],"KZs9":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e={a:"ȧ",A:"Ȧ",b:"ƀ",B:"Ɓ",c:"ƈ",C:"Ƈ",d:"ḓ",D:"Ḓ",e:"ḗ",E:"Ḗ",f:"ƒ",F:"Ƒ",g:"ɠ",G:"Ɠ",h:"ħ",H:"Ħ",i:"ī",I:"Ī",j:"ĵ",J:"Ĵ",k:"ķ",K:"Ķ",l:"ŀ",L:"Ŀ",m:"ḿ",M:"Ḿ",n:"ƞ",N:"Ƞ",o:"ǿ",O:"Ǿ",p:"ƥ",P:"Ƥ",q:"ɋ",Q:"Ɋ",r:"ř",R:"Ř",s:"ş",S:"Ş",t:"ŧ",T:"Ŧ",v:"ṽ",V:"Ṽ",u:"ŭ",U:"Ŭ",w:"ẇ",W:"Ẇ",x:"ẋ",X:"Ẋ",y:"ẏ",Y:"Ẏ",z:"ẑ",Z:"Ẑ"},t={a:"ɐ",A:"∀",b:"q",B:"Ԑ",c:"ɔ",C:"Ↄ",d:"p",D:"ᗡ",e:"ǝ",E:"Ǝ",f:"ɟ",F:"Ⅎ",g:"ƃ",G:"⅁",h:"ɥ",H:"H",i:"ı",I:"I",j:"ɾ",J:"ſ",k:"ʞ",K:"Ӽ",l:"ʅ",L:"⅂",m:"ɯ",M:"W",n:"u",N:"N",o:"o",O:"O",p:"d",P:"Ԁ",q:"b",Q:"Ò",r:"ɹ",R:"ᴚ",s:"s",S:"S",t:"ʇ",T:"⊥",u:"n",U:"∩",v:"ʌ",V:"Ʌ",w:"ʍ",W:"M",x:"x",X:"X",y:"ʎ",Y:"⅄",z:"z",Z:"Z"},r={accented:{prefix:"",postfix:"",map:e,elongate:!0},bidi:{prefix:"‮",postfix:"‬",map:t,elongate:!1}},a=function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).strategy,a=r[void 0===t?"accented":t],o="",i=!0,n=!1,p=void 0;try{for(var f,l=e[Symbol.iterator]();!(i=(f=l.next()).done);i=!0){var s=f.value;if(a.map[s]){var d=s.toLowerCase();!a.elongate||"a"!==d&&"e"!==d&&"o"!==d&&"u"!==d?o+=a.map[s]:o+=a.map[s]+a.map[s]}else o+=s}}catch(u){n=!0,p=u}finally{try{i||null==l.return||l.return()}finally{if(n)throw p}}return o.startsWith(a.prefix)&&o.endsWith(a.postfix)?o:a.prefix+o+a.postfix},o=a;exports.default=o;
},{}],"z5ot":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),Object.defineProperty(exports,"localize",{enumerable:!0,get:function(){return e.default}}),exports.default=void 0;var e=t(require("./localize.js"));function t(e){return e&&e.__esModule?e:{default:e}}var r=function(){var t={blacklistedNodeNames:["STYLE"]},r=null,o={characterData:!0,childList:!0,subtree:!0},a=function(e){return e&&"string"==typeof e},n=function(r){var o=function(e){for(var r,o=document.createTreeWalker(e,NodeFilter.SHOW_TEXT,function(e){return/[^\s]/.test(e.nodeValue)?t.blacklistedNodeNames.includes(e.parentElement.nodeName)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}),a=[];r=o.nextNode();)a.push(r);return a}(r),n=!0,d=!1,l=void 0;try{for(var i,u=o[Symbol.iterator]();!(n=(i=u.next()).done);n=!0){var c=i.value,s=c.nodeValue;a(s)&&(c.nodeValue=(0,e.default)(s,t))}}catch(f){d=!0,l=f}finally{try{n||null==u.return||u.return()}finally{if(d)throw l}}},d=function(d){var l=!0,i=!1,u=void 0;try{for(var c,s=d[Symbol.iterator]();!(l=(c=s.next()).done);l=!0){var f=c.value;if("childList"===f.type&&f.addedNodes.length>0)r.disconnect(),f.addedNodes.forEach(n),r.observe(document.body,o);else if("characterData"===f.type){var v=f.target.nodeValue,y=t.blacklistedNodeNames.includes(f.target.parentElement.nodeName);a(v)&&!y&&(r.disconnect(),f.target.nodeValue=(0,e.default)(v,t),r.observe(document.body,o))}}}catch(N){i=!0,u=N}finally{try{l||null==s.return||s.return()}finally{if(i)throw u}}};return{start:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.strategy,l=void 0===a?"accented":a,i=e.blacklistedNodeNames,u=void 0===i?t.blacklistedNodeNames:i;t.blacklistedNodeNames=u,t.strategy=l,n(document.body),(r=new MutationObserver(d)).observe(document.body,o)},stop:function(){r&&r.disconnect()},localize:e.default}}(),o=r;exports.default=o;
},{"./localize.js":"KZs9"}],"zh2v":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.ATTR_NAME="data-e7n",exports.FN_NAME="tr";
},{}],"vhgn":[function(require,module,exports) {
"use strict";var e=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function u(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(u,a)}c((r=r.apply(e,t||[])).next())})},t=this&&this.__generator||function(e,t){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=(o=u.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=t.call(e,u)}catch(a){i=[6,a],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.normalize=function(e){return e.trim().replace(/[\s\-_]+/g,"_").replace(/[^A-Za-z0-9-]/g,"")},exports.asKey=function(e){return"_"+exports.normalize(e)},exports.promiseSerialMap=function(n,r){return e(void 0,void 0,void 0,function(){var e,o,i;return t(this,function(t){return e=n.length,o=void 0===r,[2,(i=function(t,u){if(t>=e)return u;var a=o?n[t]():r(n[t]);return a&&"object"==typeof a&&a.then?a.then(function(e){return i(t+1,e)}):i(t+1,a)})(0,void 0)]})})};
},{}],"EdLt":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),require("css.escape");var e=require("pseudo-localization"),t=require("./constants"),r=require("./util");exports.options={pseudoloc:!1,disabled:!1},exports.tr=function(e,t,i,a,u){if(t=t||r.asKey(e),exports.options.disabled)return n(e,i,a);var s=chrome.i18n.getMessage(t,i)||n(e,i,a);return exports.options.pseudoloc&&(i&&a?(s=e.split(/(\$[A-Za-z][A-Za-z0-9_]*\$)/).map(function(e,t){return t%2==0?o(e,u):e}).join(""),s=n(s,i,a)):s=o(s,u)),s};var n=function(e,t,r){return t&&r&&e&&Object.entries(r).forEach(function(r){for(var n=r[0],o=r[1].content.split(/\$(\d+)/),i=1;i<o.length;i+=2){parseInt(o[i],10);o[i]=String(t[i-1])}e=e.replace(new RegExp("\\$"+n+"\\$","gi"),o.join(""))}),e};exports.updateHtml=function(e,r){void 0===e&&(e=null),void 0===r&&(r=t.ATTR_NAME),(e=e||document).querySelectorAll("["+CSS.escape(r)+"]").forEach(function(e){exports.updateElt(e,e.getAttribute(r)||void 0,r)})},exports.updateElt=function(e,n,o){void 0===o&&(o=t.ATTR_NAME);var i=e.getAttribute(o+"-html"),a=null!=i,u=(a?e.innerHTML:e.innerText).trim();n=n||r.asKey(u);var s=exports.tr(u,n,void 0,void 0,a);s&&s!==u&&(a?e.innerHTML=s:e.textContent=s)};var o=function(t,r){return r?i(t).map(function(t){var r=t.type,n=t.content;return"html"===r?n:e.localize(n)}).join(""):e.localize(t)},i=function(e){for(var t=[],r=0,n=0,o=!1,i=e.length,a=function(){n!==r&&t.push({type:o?"html":"text",content:e.substring(r,n)}),o=!o,r=n};n<i;n++){var u=e[n];if("<"===u){if(o)throw(s=new Error("Invalid HTML extra < character: "+e)).name="InvalidHTMLError",s;a()}else if(">"===u){var s;if(!o)throw(s=new Error("Invalid HTML extra > character: "+e)).name="InvalidHTMLError",s;a()}}return a(),t};
},{"css.escape":"L836","pseudo-localization":"z5ot","./constants":"zh2v","./util":"vhgn"}],"f0tH":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.updateHtml=exports.tr=void 0;var e=require("e7n");Object.defineProperty(exports,"updateHtml",{enumerable:!0,get:function(){return e.updateHtml}}),Object.defineProperty(exports,"tr",{enumerable:!0,get:function(){return e.tr}}),e.options.disabled=!0;
},{"e7n":"EdLt"}],"qa4v":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.isString=exports.isBoolean=exports.isType=exports.PromiseTrue=void 0;const e=()=>new Promise((e,o)=>e(!0));exports.PromiseTrue=e;const o=(e,o)=>new Promise((s,r)=>s(typeof e===o));exports.isType=o;const s=e=>o(e,"boolean");exports.isBoolean=s;const r=e=>o(e,"string");exports.isString=r;
},{}],"m7pw":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.PDF_FORMATS=exports.PDF_FORMAT_A4_LANDSCAPE=exports.PDF_FORMAT_LEGAL_LANDSCAPE=exports.PDF_FORMAT_LETTER_LANDSCAPE=exports.PDF_FORMAT_FULL=exports.PDF_FORMAT_A4=exports.PDF_FORMAT_LEGAL=exports.PDF_FORMAT_LETTER=void 0;var e=c(require("../store")),t=require("../auth-urls"),a=p(require("../chrome.permissions")),r=require("../e7n-util"),o=require("../image-formats"),s=require("../sniff"),i=p(require("../validate")),l=require("../constants");function n(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return n=function(){return e},e}function p(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n();if(t&&t.has(e))return t.get(e);var a={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var s=r?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(a,o,s):a[o]=e[o]}return a.default=e,t&&t.set(e,a),a}function c(e){return e&&e.__esModule?e:{default:e}}const u=o.imageFormatsArray.map(e=>e.key),d=(0,r.tr)("Capture"),h=(0,r.tr)("PDF"),m=(0,r.tr)("Download"),f="letter";exports.PDF_FORMAT_LETTER="letter";const g="legal";exports.PDF_FORMAT_LEGAL="legal";const y="a4";exports.PDF_FORMAT_A4="a4";const b="full";exports.PDF_FORMAT_FULL="full";const _="letter-landscape";exports.PDF_FORMAT_LETTER_LANDSCAPE="letter-landscape";const A="legal-landscape";exports.PDF_FORMAT_LEGAL_LANDSCAPE="legal-landscape";const F="a4-landscape";exports.PDF_FORMAT_A4_LANDSCAPE="a4-landscape";const P=[{key:"letter",display:"US letter portrait"},{key:"letter-landscape",display:"US letter landscape"},{key:"legal",display:"US legal portrait"},{key:"legal-landscape",display:"US legal landscape"},{key:"a4",display:"A4 portrait"},{key:"a4-landscape",display:"A4 landscape"},{key:"full",display:(0,r.tr)("full image")}];exports.PDF_FORMATS=P;const w=new e.default("options",[{name:"fmt",type:"choice",group:d,choices:u,default:"png",label:(0,r.tr)("Image format:"),help:(0,r.tr)("PNG 是无损的，应该与屏幕上看到的完全匹配，但通常比使用压缩的 JPG 具有更大的文件大小")},{name:"fixed_elts",type:"bool",group:d,default:!0,label:`${(0,r.tr)("Adjust repeating elements")} (beta)`,hidden:!0,help:(0,r.tr)("Sometimes elements, like a “back to top” button or a sticky footer will repeat on the page. This tries to position them absolutely to stop them from repeating in a screeshot.")},{name:"adv_scroll",type:"bool",group:d,default:!0,label:`${(0,r.tr)("Advanced scrolling")} (beta)`,hidden:!0,help:(0,r.tr)("Look for an element within the page with its own scroll bars and scroll that item during the capture.")},{name:"pdf_format",type:"choice",group:h,choices:P,default:"letter",label:(0,r.tr)("Paper size:"),help:(0,r.tr)("指定导出为 PDF 时的 PDF 格式大小.")},{name:"pdf_smart_page",type:"bool",group:h,default:!0,label:`${(0,r.tr)("Smart page splitting (Premium)")} ⚡️`,help:(0,r.tr)("Perform extra checks when splitting a PDF across multiple pages to try to prevent it from cutting lines of text in half. Otherwise, the screenshot is split at the paper size height regardless of its content."),premiumInfo:{text:"Prevent lines of text from getting split in half during export-to-PDF with smart page splitting.",cta1:"Upgrade your account",cta1LinkFn:()=>(0,t.makePremiumUrl)(),cta2:"login",cta2LinkFn:()=>(0,t.makeLoginUrl)(),basicDefault:!1},disabled:!0},{name:"pdf_url_and_date",type:"bool",group:h,default:!1,label:`${(0,r.tr)("Add URL and date (Premium)")} ⚡️`,help:(0,r.tr)("Add your screenshot’s URL and capture date when exporting to PDF from the main view screenshot page. You can add URL and capture date in the Editor from the “More” menu."),premiumInfo:{text:"Enable this feature by signing up for a premium account.",cta1:"Upgrade your account",cta1LinkFn:()=>(0,t.makePremiumUrl)(),cta2:"login",cta2LinkFn:()=>(0,t.makeLoginUrl)(),basicDefault:!1},disabled:!0},{name:"pdf_insert_links",type:"bool",group:h,default:!1,label:`${(0,r.tr)("Active Links (Premium)")} ⚡️ (beta)`,help:(0,r.tr)("Activate all the links on the page when downloading as a PDF. Clicking on the link in the PDF will open a page in your browser.\n\n**This is an experimental feature that is still in development—please report any issues to customer support using the “Report issue” flag icon above.**"),premiumInfo:{text:"Enable this feature by signing up for a premium account.",cta1:"Upgrade your account",cta1LinkFn:()=>(0,t.makePremiumUrl)(),cta2:"login",cta2LinkFn:()=>(0,t.makeLoginUrl)(),basicDefault:!1},experimental:!0,disabled:!0},{name:"dir",type:"text",group:m,default:"",label:(0,r.tr)("Directory:"),help:(0,r.tr)("Specify a directory underneath your downloads directory to save your screenshots, such as “screencaptures”. Defaults to the downloads directory if blank. (Please limit it to letters, numbers, dashes, underscores, and slashes—invalid characters will automatically be removed)."),process:e=>{let t="";e=e.trim();const a=/^[A-Za-z]:[\/\\]/;return(a.test(e)||e.startsWith("../")||e.startsWith("/"))&&(t=(0,r.tr)("The browser prevents extensions from saving files outside of the default downloads directory. The path you entered has been updated accordingly. Sorry!")+" 😟"),{val:e=e.replace(a,"").replace(/\\/g,"/").replace(/[^a-z0-9\-_\/ ]*/gi,"").replace(/\s+/g," ").replace(/\/\/+/g,"/").replace(/^\//,"").replace(/\/$/,"").trim(),msg:t}},validate:e=>i.isString(e).then(t=>t&&(!e||a.requestIfNeededDownloads()))},{name:"save_as",type:"bool",group:m,default:!1,label:(0,r.tr)("Save as"),help:`${(0,r.tr)('Automatically show a "save as" dialog when downloading your screenshots. If the “Ask where to save each file before downloading” option is enabled in your browser’s settings, then that takes priority.')}${(0,s.getIsMac)()&&l.IS_CHROME?"\n\n"+(0,r.tr)('On Mac Chrome there are issues with "save as" for auto-download and when downloading multiple files, so it is disabled in those scenarios.'):""}`,validate:e=>i.isBoolean(e)},{name:"auto_dl2",type:"bool",group:m,default:!1,label:(0,r.tr)("Auto-download files"),help:(0,r.tr)('Automatically download your screenshot as an image instead of opening it in a new window.This means the extension capture tab will not open. You can return back here by right-clicking on the extension and selecting "Options".'),validate:e=>i.isBoolean(e).then(t=>t&&(!0!==e||a.requestIfNeededDownloads()))},{name:"fit_copies",type:"bool",group:m,default:!0,label:(0,r.tr)("Fit copies to Google Docs limits"),help:(0,r.tr)("When copying a screenshot to your clipboard, auto-resize it if it exceeds the Google Docs copy-paste limit (25,000,000 pixels). You may still experience issues pasting very large images into other applications, for example Google Docs sometimes truncates the image unless you resize it much smaller.")},{name:"frame_persist",type:"bool",group:d,default:!0,label:(0,r.tr)("Persist iframe permissions"),help:(0,r.tr)("Save this permission (if accepted) for future captures. It can still be revoked later from the options page."),validate:e=>i.isBoolean(e),hidden:!0}]);var D=w;exports.default=D;
},{"../store":"M9LT","../auth-urls":"fdRK","../chrome.permissions":"WlKW","../e7n-util":"f0tH","../image-formats":"UTVA","../sniff":"UWsR","../validate":"qa4v","../constants":"qwST"}],"ZYcs":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e="application/x-postmate-v1+json",t=5,n=0,a=function(){return++n},i=function(){var e;return p.debug?(e=console).log.apply(e,arguments):null},r=function(e){var t=document.createElement("a");t.href=e;var n=t.protocol.length>4?t.protocol:window.location.protocol,a=t.host.length?"80"===t.port||"443"===t.port?t.hostname:t.host:window.location.host;return t.origin||n+"//"+a},o={handshake:1,"handshake-reply":1,call:1,emit:1,reply:1,request:1},s=function(t,n){return("string"!=typeof n||t.origin===n)&&(!!t.data&&(("object"!=typeof t.data||"postmate"in t.data)&&(t.data.type===e&&!!o[t.data.postmate])))},d=function(e,t){var n="function"==typeof e[t]?e[t]():e[t];return p.Promise.resolve(n)},l=function(){function t(e){var t=this;this.parent=e.parent,this.frame=e.frame,this.child=e.child,this.childOrigin=e.childOrigin,this.events={},this.listener=function(e){if(!s(e,t.childOrigin))return!1;var n=((e||{}).data||{}).value||{},a=n.data,i=n.name;"emit"===e.data.postmate&&i in t.events&&t.events[i].call(t,a)},this.parent.addEventListener("message",this.listener,!1)}var n=t.prototype;return n.get=function(t){var n=this;return new p.Promise(function(i){var r=a();n.parent.addEventListener("message",function e(t){t.data.uid===r&&"reply"===t.data.postmate&&(n.parent.removeEventListener("message",e,!1),i(t.data.value))},!1),n.child.postMessage({postmate:"request",type:e,property:t,uid:r},n.childOrigin)})},n.call=function(t,n){this.child.postMessage({postmate:"call",type:e,property:t,data:n},this.childOrigin)},n.on=function(e,t){this.events[e]=t},n.destroy=function(){window.removeEventListener("message",this.listener,!1),this.frame.parentNode.removeChild(this.frame)},t}(),h=function(){function t(t){var n=this;this.model=t.model,this.parent=t.parent,this.parentOrigin=t.parentOrigin,this.child=t.child,this.child.addEventListener("message",function(t){if(s(t,n.parentOrigin)){0;var a=t.data,i=a.property,r=a.uid,o=a.data;"call"!==t.data.postmate?d(n.model,i).then(function(n){return t.source.postMessage({property:i,postmate:"reply",type:e,uid:r,value:n},t.origin)}):i in n.model&&"function"==typeof n.model[i]&&n.model[i](o)}})}return t.prototype.emit=function(t,n){this.parent.postMessage({postmate:"emit",type:e,value:{name:t,data:n}},this.parentOrigin)},t}(),p=function(){function n(e){var t=e.container,n=void 0===t?void 0!==n?n:document.body:t,a=e.model,i=e.url,r=e.name,o=e.classListArray,s=void 0===o?[]:o;return this.parent=window,this.frame=document.createElement("iframe"),this.frame.name=r||"",this.frame.classList.add.apply(this.frame.classList,s),n.appendChild(this.frame),this.child=this.frame.contentWindow||this.frame.contentDocument.parentWindow,this.model=a||{},this.sendHandshake(i)}return n.prototype.sendHandshake=function(a){var i,o=this,d=r(a),h=0;return new n.Promise(function(n,r){o.parent.addEventListener("message",function e(t){return!!s(t,d)&&("handshake-reply"===t.data.postmate?(clearInterval(i),o.parent.removeEventListener("message",e,!1),o.childOrigin=t.origin,n(new l(o))):r("Failed handshake"))},!1);var p=function(){h++,o.child.postMessage({postmate:"handshake",type:e,model:o.model},d),h===t&&clearInterval(i)},c=function(){p(),i=setInterval(p,500)};o.frame.attachEvent?o.frame.attachEvent("onload",c):o.frame.onload=c,o.frame.src=a})},n}();p.debug=!1,p.Promise=function(){try{return window?window.Promise:Promise}catch(e){return null}}(),p.Model=function(){function t(e){return this.child=window,this.model=e,this.parent=this.child.parent,this.sendHandshakeReply()}return t.prototype.sendHandshakeReply=function(){var t=this;return new p.Promise(function(n,a){t.child.addEventListener("message",function i(r){if(r.data.postmate){if("handshake"===r.data.postmate){t.child.removeEventListener("message",i,!1),r.source.postMessage({postmate:"handshake-reply",type:e},r.origin),t.parentOrigin=r.origin;var o=r.data.model;return o&&Object.keys(o).forEach(function(e){t.model[e]=o[e]}),n(new h(t))}return a("Handshake Reply Failed")}},!1)})},t}();var c=p;exports.default=c;
},{}],"qcVt":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=require("../constants"),t=o(require("../store"));function o(e){return e&&e.__esModule?e:{default:e}}const a=new t.default("user",[{name:"rejected_dl",type:"bool",default:!1},{name:"login_data",type:"json",default:()=>({})},{name:"ab",type:"json",default:()=>({})},{name:"show_editor_tour",type:"bool",default:!0},{name:"has_logged_in",type:"bool",default:!1},{name:"last_dl_type",type:"string",default:e.DL_TYPE_IMAGE},{name:"ab_req_token",type:"string"},{name:"show_experimental",type:"bool",default:!1}]);window._showTour=(()=>a.remove("show_editor_tour"));var r=a;exports.default=r;
},{"../constants":"qwST","../store":"M9LT"}],"wvbk":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.resetUser=exports.default=exports.SUB_OK=exports.SUB_NEEDS_UPDATE=exports.SUB_BAD=void 0;var e=require("luxon"),t=a(require("postmate")),r=require("./constants"),n=a(require("./log")),s=a(require("./stores/user-store"));function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t,r,n,s,a,o){try{var u=e[a](o),i=u.value}catch(c){return void r(c)}u.done?t(i):Promise.resolve(i).then(n,s)}function u(e){return function(){var t=this,r=arguments;return new Promise(function(n,s){var a=e.apply(t,r);function u(e){o(a,n,s,u,i,"next",e)}function i(e){o(a,n,s,u,i,"throw",e)}u(void 0)})}}const i="trialing",c="active",d="past_due",l="canceled",p="unpaid",f="",g="sub-none";exports.SUB_BAD=g;const b="sub-needs-update";exports.SUB_NEEDS_UPDATE=b;const h="sub-ok";exports.SUB_OK=h;const _={trialing:h,active:h,past_due:b,canceled:g,unpaid:b,[f]:g},x=(e,...t)=>(0,n.default)(`%c[\\USER//] ${e}`,"color: #0B3;background:#EEFFF3",...t);class S{constructor(){this._listeners=[],s.default.onUpdate(e=>{x(`Updated names: ${JSON.stringify(e)}`),this.loginStateProm().then(e=>{this._listeners.forEach(t=>t(e))}).catch(e=>{x("Error loading subscription state!"),n.default.error(e)})})}addListener(e){this._listeners.push(e)}removeListener(e){this._listeners=this._listeners.filter(t=>t!==e)}loginStateProm(t){var r=this;return u(regeneratorRuntime.mark(function a(){var o,u,i,c,d;return regeneratorRuntime.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return x(`loginStateProm(${JSON.stringify(t)})`),a.next=3,s.default.load();case 3:if(o=a.sent,x("got items"),(0,n.default)(o),u=r._asLoginState(o.login_data,o),x("converted data:"),(0,n.default)(u),u.subState!==g){a.next=12;break}return x(`BAD SUBSTATE ${u.subState}`),a.abrupt("return",u);case 12:if(i=e.DateTime.now(),c=i.diff(u.updated,"days").days,x(`days since: ${c}`),!(t&&c>=31&&navigator.onLine)){a.next=30;break}return a.prev=16,x("do extra login!!!!"),a.next=20,r._login();case 20:d=a.sent,x("got data!!!!"),(0,n.default)(d),a.next=29;break;case 25:throw a.prev=25,a.t0=a.catch(16),n.default.error("Error logging in!"),a.t0;case 29:d&&(u=r._asLoginState(d,o));case 30:return x("RETURNING DATA!!!"),(0,n.default)(u),a.abrupt("return",u);case 33:case"end":return a.stop()}},a,null,[[16,25]])}))()}setWebsiteABState(){var e=this;return u(regeneratorRuntime.mark(function t(){var r,a,o,u,i,c,d,l,p,f,g;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return x("[User.setWebsiteABState] start"),t.next=3,s.default.load();case 3:if(r=t.sent,a=r.ab,"{}"!==JSON.stringify(a)){t.next=7;break}return t.abrupt("return",!0);case 7:return o=r.login_data,u=!(!o||!o.userId),i=Object.assign({hasUser:u},a),c=JSON.stringify(i),t.next=13,e._getHandshake();case 13:(d=t.sent).call("setAbJson",c),x("[User.setWebsiteABState] did setAbJson"),l=2,p=null;case 18:if(!(l>0)){t.next=40;break}return l--,t.next=22,d.get("getAbJson");case 22:f=t.sent,x(`[User.setWebsiteABState] did getAbJson: ${f}`),t.prev=24,p=JSON.parse(f),t.next=33;break;case 28:return t.prev=28,t.t0=t.catch(24),x("[User -> postmate.getAbJson -> JSON.parse]"),n.default.error(t.t0),t.abrupt("continue",18);case 33:if(p&&"object"==typeof p){t.next=37;break}return x(`[User -> postmate.getAbJson -> object] not object! ${p}`),p=null,t.abrupt("continue",18);case 37:return t.abrupt("break",40);case 40:return p=p||{},g=Object.keys(i).every(e=>e in p),x(`[User.setWebsiteABState] isSet? ${g}: ${JSON.stringify(p)}`),t.abrupt("return",g);case 44:case"end":return t.stop()}},t,null,[[24,28]])}))()}_asLoginState(t,r){t&&"object"==typeof t||n.default.error("INVALID DATA",t);const s=Object.assign({},t);["expires","updated"].forEach(t=>s[t]=e.DateTime.fromSeconds(s[t]||0)),s.status=s.status||f;const a=e.DateTime.now();(!s.userId||a>s.expires)&&(s.status=f);let o=_[s.status];return s.subState=o,s.store=r,s}_getHandshake(){return this._handshake||(this._handshake=new t.default({container:document.body,url:r.WEBCOMM_URL,classListArray:["h"]})),this._handshake}_login(){var e=this;return u(regeneratorRuntime.mark(function t(){var r,s;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e._getHandshake();case 2:return r=t.sent,t.next=5,r.get("authState");case 5:return s=t.sent,(0,n.default)("[PARENT] authState",s),t.next=9,e.storeLoginData(s);case 9:return t.abrupt("return",s);case 10:case"end":return t.stop()}},t)}))()}storeLoginData(t){let r=t&&t.show_experimental;null!=r&&(delete t.show_experimental,"boolean"!=typeof r&&(r=null)),t.updated=Math.floor(e.DateTime.now().toSeconds());const n={login_data:t};return t.userId&&(n.has_logged_in=!0),null!=r&&(n.show_experimental=r),s.default.setObj(n)}}const v=new S;var m=v;exports.default=m;const A=()=>s.default.set("login_data",{});exports.resetUser=A;
},{"luxon":"eqBM","postmate":"ZYcs","./constants":"qwST","./log":"DrS6","./stores/user-store":"qcVt"}],"wT1R":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),Object.defineProperty(exports,"$",{enumerable:!0,get:function(){return n.$}}),exports.scrollEltTo=exports.fullServiceDownload=exports.addViaToError=exports.toQueryString=exports.parseQueryString=exports.getQueryString=exports.asArray=exports.asFilename=exports.errorToString=exports.reportLink=exports.b64=exports.GESTURE_TIMEOUT=void 0;var e=require("luxon"),r=f(require("remove-accents")),t=p(require("./chrome.downloads")),o=p(require("./chrome.permissions")),n=require("./domutil"),s=require("./extension"),a=f(require("./log")),i=require("./sniff"),c=f(require("./stores/options-store")),u=require("./User");function l(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return l=function(){return e},e}function p(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=l();if(r&&r.has(e))return r.get(e);var t={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var s=o?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(t,n,s):t[n]=e[n]}return t.default=e,r&&r.set(e,t),t}function f(e){return e&&e.__esModule?e:{default:e}}function y(e,r,t,o,n,s,a){try{var i=e[s](a),c=i.value}catch(u){return void t(u)}i.done?r(c):Promise.resolve(c).then(o,n)}function d(e){return function(){var r=this,t=arguments;return new Promise(function(o,n){var s=e.apply(r,t);function a(e){y(s,o,n,a,i,"next",e)}function i(e){y(s,o,n,a,i,"throw",e)}a(void 0)})}}const v="https://screencapture.mrcoles.com/",g=1600,m=5e3;exports.GESTURE_TIMEOUT=5e3;const h=e=>{var r=!1;try{e=btoa(e),r=!0}catch(t){}if(r)for(;"="===e.charAt(e.length-1);)e=e.substring(0,e.length-1);return e};exports.b64=h;const x=(e,r,t,o,n,i)=>{const c=e=>{try{return Object.entries(e).map(([e,r])=>`${e}=${r}`).join(";")}catch(r){a.default.error("Error parsing obj!",r)}try{return JSON.stringify(e)}catch(r){a.default.error("Error JSONifying obj!",r)}return""+e},l=[{key:"v",val:h((0,s.getVersion)())}];if(e&&l.push({key:"u",val:h(e)}),o&&l.push({key:"i",val:h(o)}),n&&n!==u.SUB_BAD&&l.push({key:"s",val:h(n)}),i){const e=c(i);l.push({key:"m",val:h(e)})}if(t){const e=c(t);l.push({key:"o",val:h(e)})}r&&l.push({key:"e",val:h(r)});let p=k(l);const f=1600-v.length-1;return p.length>=f&&(p=p.substring(0,f)),v+(p?"?"+p:"")};exports.reportLink=x;const b=e=>{let r=void 0;return"string"==typeof e?e:(e&&(r=[e.name,e.via?`(${e.via})`:"",e.message,e.stack].filter(e=>e).join(" ")),r)};exports.errorToString=b;const w=(t,o)=>{try{t=decodeURIComponent(t)}catch(s){}let n=r.default.remove(t).split("?")[0].split("#")[0];return`screencapture${n=n?"-"+(n=n.replace(/^https?:\/\/(www\.)?/,"").replace(/^([^\/]+)\.com/,"$1").replace(/[^A-Za-z0-9]+/g,"-").replace(/-+/g,"-").replace(/^[_\-]+/,"").replace(/[_\-]+$/,"")):""}-${e.DateTime.now().toFormat("yyyy-MM-dd-HH_mm_ss")}${o}`};exports.asFilename=w;const T=e=>Array.prototype.slice.call(e,0);exports.asArray=T;const j=()=>O(window.location.search.substring(1));exports.getQueryString=j;const O=e=>{var r={};return(e||"").split("&").forEach(function(e){if(e){var t=e.split("=").map(decodeURIComponent);r[t.shift()]=t.join("=")}}),r};exports.parseQueryString=O;const k=e=>{const r=(e,r)=>r?null==e?"":`=${encodeURIComponent(e)}`:encodeURIComponent(e);return(Array.isArray(e)?e.map(({key:e,val:t})=>r(e)+r(t,!0)):Object.entries(e).map(([e,t])=>r(e)+r(t,!0))).join("&")};exports.toQueryString=k;const S=(e,r)=>{e.via=(e.via?`${e.via} > `:"")+r};exports.addViaToError=S;const _=function(){var e=d(regeneratorRuntime.mark(function e(r,n=!1,s=!1){var a,u,l,p,f,y;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.containsDownloads();case 2:if(!(a=e.sent)){e.next=16;break}return e.next=6,c.default.load();case 6:return u=e.sent,l=(!(0,i.getIsMac)()||!(n||r.length>1))&&(s||u.save_as),e.next=10,t.downloadAll(r,u.dir,l);case 10:return p=e.sent,f=p.reduce((e,r)=>e+(!1===r.success?1:0),0),y=p.length-f,e.abrupt("return",{hasPerms:a,results:p,failCount:f,successCount:y});case 16:return e.abrupt("return",{hasPerms:a,results:r.map(e=>({url:e,success:!1}))});case 17:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}();exports.fullServiceDownload=_;const E=(e,r,t)=>{e.scrollTo&&"function"==typeof e.scrollTo?e.scrollTo(r,t):(e.scrollLeft=r,e.scrollTop=t)};exports.scrollEltTo=E;
},{"luxon":"eqBM","remove-accents":"Mo85","./chrome.downloads":"t52Y","./chrome.permissions":"WlKW","./domutil":"CRk1","./extension":"QUfo","./log":"DrS6","./sniff":"UWsR","./stores/options-store":"m7pw","./User":"wvbk"}],"f9V3":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Capture=exports.db=void 0;var e=u(require("assert-plus")),t=u(require("dexie")),r=require("luxon"),a=u(require("./filesystem")),i=require("./image-formats"),s=u(require("./log")),d=require("./util");function u(e){return e&&e.__esModule?e:{default:e}}const n=new t.default("Test4");exports.db=n,n.version(1).stores({captures:"++id, domain, time"});class o{static from(e,t,r,a,d,u,n){t=t||"",a=a||1;let l=(0,i.getImageFormat)(r);r!==l.key&&(s.default.warn(`Wrong case for image format key ${r} vs ${l.key}`),r=l.key),p(Array.isArray(d),`Files object is not an array! ${typeof d}`);let c={url:e,domain:h(e),title:t,format:r,scaleMultiplier:a,images:d.map(e=>e.filename),sizes:d.map(e=>e.size),time:n||new Date,metadata:u},f=new o;return Object.entries(c).forEach(([e,t])=>{f[e]=t}),f}get pdfFilename(){return this.images.length?this.images[0].replace(/\.[^\.]+$/,".pdf"):"screenshot.pdf"}get fullPaths(){return this.images.map(e=>a.default.absPath(`/${e}`))}get fileEntryPaths(){return this.images.map(e=>`/${e}`)}get imageFormat(){return(0,i.getImageFormat)(this.format)}get dateTime(){return r.DateTime.fromJSDate(this.time)}get displayURL(){return chrome.extension.getURL("/capture.html")+"?"+(0,d.toQueryString)([{key:"id",val:this.id},{key:"url",val:this.url}])}getEditorURL(e=0){return chrome.extension.getURL("/editor.html")+"?"+(0,d.toQueryString)([{key:"id",val:this.id},{key:"e",val:e}])}static count(){return n.captures.count()}save(){return n.captures.put(this).then(e=>(this.id=e,this))}hackUpdateCreated(e){const t=new Date,r={},a=["created","updated"];return a.forEach(a=>r[`edits.${e}.${a}`]=t),n.captures.update(this.id,r).then(r=>{if(!r){const e=new Error(`Capture not found for update scale (${this.id})`);throw e.name="UpdateCaptureNotFound",e}return a.forEach(r=>this.edits[e][r]=t),this})}updateScaleMultiplier(){(0,e.default)(!this.scaleMultiplier,`Should not set scaleMultiplier when already have (${this.id}: ${this.scaleMultiplier})`);const t={scaleMultiplier:window.devicePixelRatio||1,guessedScale:!0};return n.captures.update(this.id,t).then(e=>{if(!e){const e=new Error(`Capture not found for update scale (${this.id})`);throw e.name="UpdateCaptureNotFound",e}return Object.entries(t).forEach(([e,t])=>this[e]=t),this})}addEdit(t,r=0){e.default.number(t,"Capture.addEdit(editId)"),e.default.number(r,"Capture.addEdit(..., imageId)"),e.default.equal(t,r,`Must make editId (${t}) and imageId (${r}) the same!`);const a=new Date,i={id:t,imageId:r,created:a,updated:a};let s,d;return this.edits?((0,e.default)(!this.edits[t],`Cannot overwrite existing edit ${t} -> ${JSON.stringify(this.edits[t])}`),s=`edits.${t}`,d=i):(s="edits",(d=[])[t]=i),n.captures.update(this.id,{[s]:d}).then(e=>{if(!e){const e=new Error(`Capture not found for add (${this.id})`);throw e.name="AddCaptureEditNotFound",e}let t=this;return s.split(".").forEach((e,r,a)=>{e=/^\d+$/.test(e)?parseInt(e):e,r===a.length-1?t[e]=d:t=this[e]}),this})}static updateEditState(e,t,r){return n.captures.update(e,{[`edits.${t}.undos`]:r,[`edits.${t}.updated`]:new Date}).then(t=>{if(!t){const t=new Error(`Capture not found for update (${e})`);throw t.name="UpdateCaptureEditNotFound",t}})}remove(){return a.default.requestFs().then(e=>a.default.getFiles(e,this.fileEntryPaths,!0)).then(e=>a.default.rmFiles(e,e=>(0,s.default)(e))).then(({succeeded:e,failed:t})=>{if(t.length){let e=t.map(e=>e.toURL()),r=[],a=[];this.fullPaths.forEach((t,i)=>{-1!==e.indexOf(t)&&(r.push(this.images[i]),a.push(this.sizes[i]))}),this.save();let i=new Error(`Unable to delete: ${e.join(", ")}`);throw i.name="CaptureRemove",i}return n.captures.delete(this.id)})}static lookup(e){return n.captures.get(l(e))}static lookupIds(e){return e=e.map(e=>l(e)),n.captures.where("id").anyOf(e).toArray()}static findSrc(e){return n.captures.toCollection().filter(t=>~t.images.indexOf(e)).toArray().then(e=>e[0])}static all(){return n.captures.orderBy("time").toArray()}}exports.Capture=o,n.captures.mapToClass(o);const l=e=>"string"==typeof e&&/^\d+$/.test(e)?parseInt(e,10):e,p=(e,t,r)=>{if(!e){void 0===r&&(r=t,t=void 0);let e=new Error(r);throw t&&(e.name=t),e}},h=e=>{let t=/^https?:\/\//;return t.test(e)&&(e=e.replace(t,"")),e.split(":")[0]};window.Capture=o;
},{"assert-plus":"VDee","dexie":"nHYk","luxon":"eqBM","./filesystem":"sBdG","./image-formats":"UTVA","./log":"DrS6","./util":"wT1R"}],"GZY7":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.oncePromise=exports.off=exports.on=exports.trigger=void 0;var r={},o={};exports.trigger=function(t,n){[r,o].forEach(function(r){return e(r,t,[]).forEach(function(r){return r(n)})}),o[t]=[]},exports.on=function(o,t){e(r,o,[]).push(t)},exports.off=function(o,e){var t=r[o];t&&(r[o]=t.filter(function(r){return r!==e}))},exports.oncePromise=function(r){return new Promise(function(t){e(o,r,[]).push(t)})};var e=function(r,o,e){return void 0===r[o]&&(r[o]=e),r[o]};
},{}],"kp1E":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.isValidMessage=exports.ERR_MSG_COPY=exports.ERR_TITLE_COPY=exports.ERR_MSG_IF_PERSISTS=void 0;var e=require("./e7n-util");exports.ERR_MSG_IF_PERSISTS=e.tr("If this persists, please report it via the flag reporting button above."),exports.ERR_TITLE_COPY=e.tr("Unable to copy image"),exports.ERR_MSG_COPY=e.tr("Please try again and wait to see the “Image copied to clipboard” message before switching to another window.");var r={capture:"capture",captureError:"captureError",captureFrame:"captureFrame"};exports.default=r;var t=[];for(var a in r)t.push(r[a]);exports.isValidMessage=function(e){return t.indexOf(e)>-1};
},{"./e7n-util":"f0tH"}],"ku0T":[function(require,module,exports) {
"use strict";var t=this&&this.__awaiter||function(t,e,r,n){return new(r||(r=Promise))(function(o,a){function i(t){try{u(n.next(t))}catch(e){a(e)}}function l(t){try{u(n.throw(t))}catch(e){a(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r(function(t){t(e)})).then(i,l)}u((n=n.apply(t,e||[])).next())})},e=this&&this.__generator||function(t,e){var r,n,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(a){return function(l){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=e.call(t,i)}catch(l){a=[6,l],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,l])}}},r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.revokeObjectUrl=exports.blobToUrl=exports.dataToBlob=exports.dataURIToBlob=exports.canvasToBlob=void 0;var n=r(require("./log"));function o(r,n,o){return t(this,void 0,Promise,function(){return e(this,function(t){return n=n||"image/png",o=o||1,window.HTMLCanvasElement&&HTMLCanvasElement.prototype.toBlob?[2,new Promise(function(t){return r.toBlob(function(e){return t(e)},n,o)})]:[2,new Promise(function(t){t(a(r.toDataURL(n,o)))})]})})}function a(t){return i(atob(t.split(",")[1]),t.split(",")[0].split(":")[1].split(";")[0])}function i(t,e){for(var r=new ArrayBuffer(t.length),n=new Uint8Array(r),o=t.length;o--;)n[o]=t.charCodeAt(o);return new Blob([r],{type:e})}function l(t){return URL.createObjectURL(t)}function u(t){if(t&&URL.revokeObjectURL)try{URL.revokeObjectURL(t)}catch(e){n.default.error("Unable to revoke object URL"),n.default.error(e)}}exports.canvasToBlob=o,exports.dataURIToBlob=a,exports.dataToBlob=i,exports.blobToUrl=l,exports.revokeObjectUrl=u;
},{"./log":"DrS6"}],"L1Zq":[function(require,module,exports) {
"use strict";var t=this&&this.__assign||function(){return(t=Object.assign||function(t){for(var e,i=1,r=arguments.length;i<r;i++)for(var a in e=arguments[i])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)},e=this&&this.__spreadArrays||function(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;var r=Array(t),a=0;for(e=0;e<i;e++)for(var o=arguments[e],n=0,h=o.length;n<h;n++,a++)r[a]=o[n];return r},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(exports,"__esModule",{value:!0});var r=require("./blob"),a=i(require("./log")),o=28800,n=9e3,h=o*n,s=function(){function i(t){this.exportFormat=t,this.canvasObjs=[],this.bgRegions=[],this.scaleMultiplier=1,this.totalWidth=0,this.totalHeight=0,this.metadata={},this._debugCounter=0,this.scaleMultiplier=1,this.totalWidth=0,this.totalHeight=0,this.metadata={},this._debugCounter=0}return i.prototype.size=function(){return this.canvasObjs.length},i.prototype.isEmpty=function(){return 0===this.canvasObjs.length},i.prototype.updateMetadata=function(e){this.metadata=t(t({},this.metadata),e)},i.prototype.appendMetadataLinks=function(i){if(i){var r=this.metadata.links||[],a=e(r,i);this.metadata=t(t({},this.metadata),{links:a})}},i.prototype.sortLinks=function(){this.metadata.links&&this.metadata.links.sort(function(t,e){return t.bounds[0].y===e.bounds[0].y?t.bounds[0].x-e.bounds[0].x:t.bounds[0].y-e.bounds[0].y})},i.prototype.setScaleMultiplier=function(t){this.scaleMultiplier=t},i.prototype.setObjs=function(t,e,i){this.totalWidth=t,this.totalHeight=e;var r,a,s,c,l,u=e>o||t>o||e*t>h,f=t>e,p=u?f?o:n:t,g=u?f?n:o:e,d=Math.ceil(t/p),y=Math.ceil(e/g),v=0,x=[];for(r=0;r<y;r++)for(a=0;a<d;a++){(s=document.createElement("canvas")).width=a==d-1&&t%p||p,s.height=r==y-1&&e%g||g;var b=s.getContext("2d");i&&(b.fillStyle=i,b.fillRect(0,0,s.width,s.height)),c=a*p,l=r*g,x.push({canvas:s,ctx:b,index:v,left:c,right:c+s.width,top:l,bottom:l+s.height,width:s.width,height:s.height}),v++}this.canvasObjs=x},i.prototype.toDataURLs=function(){var t=this;return this.canvasObjs.map(function(e){return e.canvas.toDataURL(t.exportFormat)})},i.prototype.toBlobs=function(){var t=this;return Promise.all(this.canvasObjs.map(function(e){return r.canvasToBlob(e.canvas,t.exportFormat)}))},i.prototype.fillRect=function(t,e,i,r,a){var o=this;this._filter(e,i,r,a).forEach(function(n){var h=o._shift(n,e,i);n.ctx.fillStyle=t,n.ctx.fillRect(h.x,h.y,r,a)})},i.prototype.strokeRect=function(t,e,i,r,a){var o=this;this._filter(e,i,r,a).forEach(function(n){var h=o._shift(n,e,i);n.ctx.strokeStyle=t,n.ctx.strokeRect(h.x,h.y,r,a)})},i.prototype.drawImage=function(e,i,r,a,o,n,h){var s=this;this._debugCounter++,this._filter(i,r,a,o).forEach(function(c){var l=s._shift(c,i,r),u=h?t(t({},h),s._shift(c,h.x,h.y)):void 0,f=c.ctx;if(f.save(),u){var p=new Path2D,g=u.x,d=u.y,y=u.width,v=u.height,x=Math.floor(u.x),b=Math.floor(u.y),m=Math.ceil(u.width),_=Math.ceil(u.height);x+m<g+y&&(m+=1),b+_<d+v&&(_+=1),p.rect(x,b,m,_),f.clip(p)}if(f.drawImage(e,l.x,l.y),f.restore(),n&&"object"==typeof n&&!0===n.debug){var w=h||{x:l.x,y:l.y,width:a,height:o},M=Math.max(0,w.x),R=Math.max(0,w.y),O=w.x<0?w.width+w.x:w.width,j=w.y<0?w.height+w.y:w.height;f.save(),f.strokeStyle="#0F0",f.lineWidth=3,f.strokeRect(M,R,O,j);f.font="15px Arial",f.textAlign="left",f.textBaseline="top";var k="#"+s._debugCounter+": "+s._str(n),E=f.measureText(k).width;f.fillStyle="rgba(0, 0, 0, .4)",f.fillRect(M,R,E+10,25),f.fillStyle="#FFF",f.fillText(k,M+5,R+5)}})},i.prototype._str=function(t){try{if("capture"===t.msg)return JSON.stringify({x:t.x,y:t.y,clip:t.clip,capture:t.capture})}catch(e){}try{return JSON.stringify(t)}catch(i){return""+t}},i.prototype._filter=function(t,e,i,r){var a=t+i,o=e+r;return this.canvasObjs.filter(function(i){return t<i.right&&a>i.left&&e<i.bottom&&o>i.top})},i.prototype._shift=function(t,e,i){return{x:Math.round(e-t.left),y:Math.round(i-t.top)}},i.prototype._constrain=function(t,e,i,r,a){var o=this._shift(t,e,i),n=o.x,h=n+r,s=o.y,c=s+a,l=Math.max(0,n),u=Math.max(0,s);return{x:l,y:u,width:Math.min(t.height,h-l),height:Math.min(t.height,c-u)}},i.prototype.scale=function(t){return(t||0)*this.scaleMultiplier},i.prototype.scaleAll=function(t,e){var i=this;e.forEach(function(e){if("*"===e)Object.keys(t).forEach(function(e){return t[e]=i.scale(t[e])});else{var r=e.indexOf(".");-1===r?t[e]=i.scale(t[e]):i.scaleAll(t[e.substring(0,r)],[e.substring(r+1)])}})},i.prototype.setBgRegions=function(t){this.bgRegions=t},i.prototype.applyBgRegions=function(){var t=this;this.bgRegions.forEach(function(e,i){var r=e.sample,o=e.fill,n=new Map;t._filter(r.x,r.y,r.width,r.height).forEach(function(e){var i=t._constrain(e,r.x,r.y,r.width,r.height);if(i.width>0&&i.height>0){var o=document.createElement("canvas");o.width=i.width,o.height=i.height;var h=o.getContext("2d");h.drawImage(e.canvas,-i.x,-i.y);var s=void 0;try{s=h.getImageData(0,0,i.width,i.height)}catch(c){a.default.error(c)}s&&t._getHisto(s.data,n)}});var h=0,s=0;if(n.forEach(function(t,e){t>h&&(h=t,s=e)}),0!==s){var c="rgb("+t._toRgb(s).join(", ")+")";t.fillRect(c,o.x,o.y,o.width,o.height)}})},i.prototype._getHisto=function(t,e){e=e||new Map;for(var i=0,r=t.length;i<r;i+=4)if(255===t[i+3]){var a=this._toInt(t[i],t[i+1],t[i+2]);e.set(a,(e.get(a)||0)+1)}return e},i.prototype._toInt=function(t,e,i){return(t<<16)+(e<<8)+i},i.prototype._toRgb=function(t){var e=255&t,i=255&(t>>=8);return[255&(t>>=8),i,e]},i}();exports.default=s;
},{"./blob":"ku0T","./log":"DrS6"}],"aDCn":[function(require,module,exports) {
"use strict";var e=this&&this.__awaiter||function(e,r,t,n){return new(t||(t=Promise))(function(o,i){function u(e){try{c(n.next(e))}catch(r){i(r)}}function s(e){try{c(n.throw(e))}catch(r){i(r)}}function c(e){var r;e.done?o(e.value):(r=e.value,r instanceof t?r:new t(function(e){e(r)})).then(u,s)}c((n=n.apply(e,r||[])).next())})},r=this&&this.__generator||function(e,r){var t,n,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,n=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=(o=u.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=r.call(e,u)}catch(s){i=[6,s],n=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.openInTabs=exports.windowsCreate=exports.create=exports.getCurrentTab=exports.query=exports.sendMessage=exports.isExecuteScriptChromeError=exports.executeScript=exports.isCaptureVisibleTabQuotaError=exports.captureVisibleTab=exports.getZoom=exports.ERROR_TYPE=void 0;var t=require("./chrome.util");exports.ERROR_TYPE="ChromeTabsError";var n=t.genLastErrorFmt(exports.ERROR_TYPE);exports.getZoom=function(e){return new Promise(function(r,t){chrome.tabs.getZoom(e,function(e){var o=chrome.runtime.lastError;return o?t(n(o,"getZoom")):r(e)})})},exports.captureVisibleTab=function(e,r){return new Promise(function(t,o){chrome.tabs.captureVisibleTab(e,r,function(e){var r=chrome.runtime.lastError;return r?o(n(r,"captureVisibleTab")):t(e)})})},exports.isCaptureVisibleTabQuotaError=function(e){return e&&"object"==typeof e&&e.name===exports.ERROR_TYPE&&"captureVisibleTab"===e.via&&e.message&&-1!==e.message.indexOf("MAX_CAPTURE_VISIBLE_TAB_CALLS_PER_SECOND")},exports.executeScript=function(e,r){return new Promise(function(t,o){chrome.tabs.executeScript(e,r,function(e){var r=chrome.runtime.lastError;return r?o(n(r,"executeScript")):t(e)})})},exports.isExecuteScriptChromeError=function(e){return e&&"object"==typeof e&&e.name===exports.ERROR_TYPE&&"executeScript"===e.via&&e.message&&-1!==e.message.indexOf('"chrome-error://')},exports.sendMessage=function(e,r,t){return new Promise(function(o,i){chrome.tabs.sendMessage(e,r,t,function(e){var r=chrome.runtime.lastError;return r?i(n(r,"sendMessage")):o(e)})})},exports.query=function(e){return new Promise(function(r,t){chrome.tabs.query(e,function(e){var o=chrome.runtime.lastError;return o?t(n(o,"query")):r(e)})})},exports.getCurrentTab=function(){return exports.query({active:!0,currentWindow:!0}).then(function(e){return e[0]})},exports.create=function(e){return new Promise(function(r,t){chrome.tabs.create(e,function(e){var o=chrome.runtime.lastError;return o?t(n(o,"create")):r(e)})})},exports.windowsCreate=function(e){return new Promise(function(r,t){chrome.windows.create(e,function(e){var o=chrome.runtime.lastError;return o?t(n(o,"windowsCreate")):r(e)})})},exports.openInTabs=function(t,n){return e(void 0,void 0,Promise,function(){var e,o,i;return r(this,function(r){return e=void 0,o=n.length,[2,(i=function(r){if(r>=n.length)return n;var u=n[r],s=r===o-1;return t.incognito&&0===r?exports.windowsCreate({url:u,incognito:!1,focused:s}).then(function(t){return e=t.id,i(r+1)}):exports.create({url:u,active:s,windowId:e,openerTabId:t.id,index:(t.incognito?0:t.index)+1+r}).then(function(e){return i(r+1)})})(0)]})})};
},{"./chrome.util":"abex"}],"Ce6c":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getAllFrames=exports.ERROR_NAME=void 0;var r=require("./chrome.util");exports.ERROR_NAME="ChromeWebNavigationError";var e=r.genLastErrorFmt(exports.ERROR_NAME);exports.getAllFrames=function(r){return new Promise(function(t,o){chrome.webNavigation.getAllFrames(r,function(r){var s=chrome.runtime.lastError;return s?o(e(s,"getAllFrames")):t(r)})})};
},{"./chrome.util":"abex"}],"cZF8":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.captureToMultiCanvas=exports.captureToBlobs=exports.captureToFiles=exports.filesToRecord=exports.FRAME_PERMS_CONTAINS=exports.FRAME_PERMS=exports.ERROR_TYPES=void 0;var e=require("./db"),t=d(require("./events")),r=p(require("./filesystem")),n=p(require("./log")),a=require("./promise"),i=p(require("./messages")),o=d(require("./multicanvas")),s=d(require("./chrome.extension")),u=d(require("./chrome.permissions")),l=d(require("./chrome.tabs")),c=d(require("./chrome.webnavigation"));function p(e){return e&&e.__esModule?e:{default:e}}function f(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return f=function(){return e},e}function d(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=f();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){var i=n?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}return r.default=e,t&&t.set(e,r),r}function h(e,t){return v(e)||b(e,t)||g(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return w(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(e,t):void 0}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function b(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,a=!1,i=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(u){a=!0,i=u}finally{try{n||null==s.return||s.return()}finally{if(a)throw i}}return r}}function v(e){if(Array.isArray(e))return e}function x(e,t,r,n,a,i,o){try{var s=e[i](o),u=s.value}catch(l){return void r(l)}s.done?t(u):Promise.resolve(u).then(n,a)}function y(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){x(i,n,a,o,s,"next",e)}function s(e){x(i,n,a,o,s,"throw",e)}o(void 0)})}}const E={UNKNOWN_MESSAGE:"UnknownMessage",TIMED_OUT:"TimedOut",CHROME_TABS:l.ERROR_TYPE,MISSING_BLOB:"MissingBlob",CONTENT_SCRIPT:"ContentScript"};exports.ERROR_TYPES=E;const R={permissions:["tabs","webNavigation"],origins:["<all_urls>"]};exports.FRAME_PERMS=R;const k={permissions:["tabs","webNavigation"],origins:["https://*/*","http://*/*"]};exports.FRAME_PERMS_CONTAINS=k;const M=(t,r,n,a,i,o)=>{return e.Capture.from(t,r,n,a,i,o).save()};exports.filesToRecord=M;const S=(e,t,r,n,a)=>I(e,r,n,a).then(({blobs:e,scaleMultiplier:r,metadata:n})=>Promise.all(e.map((e,r)=>W(e,t,r))).then(e=>({files:e,scaleMultiplier:r,metadata:n})));exports.captureToFiles=S;const I=(e,t,r,n)=>_(e,t,r,n).then(t=>(n.add_url&&t.canvasObjs.forEach(({ctx:r,width:n,height:a})=>{let i=t.scaleMultiplier;r.scale(i,i),r.fillStyle="#00000055",r.fillRect(0,0,n/i,40),r.fillStyle="#fff",r.font="20px Arial",r.textBaseline="middle",r.fillText(e.url,20,20)}),t.toBlobs().then(e=>({blobs:e,scaleMultiplier:t.scaleMultiplier,metadata:t.metadata}))));exports.captureToBlobs=I;const _=function(){var e=y(regeneratorRuntime.mark(function e(t,r,a,s){var u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=r||function(){},""===t.url||"about:blank"===t.url||t.url.startsWith("about:blank#")?u=Promise.resolve().then(()=>{const e=document.createElement("canvas");e.width=t.width,e.height=t.height;const r=e.getContext("2d");return r.fillStyle="#fff",r.fillRect(0,0,e.width,e.height),e.toDataURL()}):"chrome://newtab/"===t.url||"chrome://apps/"===t.url||"edge://newtab/"===t.url||"edge://apps/"===t.url||t.url.startsWith("https://ntp.msn.com/edge/ntp")?u=l.captureVisibleTab(t.windowId,s.fmt_details.capture):t.url.startsWith("data:image/")&&(u=Promise.resolve(t.url)),!u){e.next=4;break}return e.abrupt("return",O(u,r,s.fmt_details.canvas));case 4:return e.abrupt("return",new Promise((e,u)=>{const l=[new o.default(s.fmt_details.canvas)];let c=[],p=void 0,f=void 0;chrome.runtime.onMessage.addListener((e,d,h)=>{var m;try{if(void 0===p&&void 0!==e.windowWidth&&(p=e.windowWidth),void 0!==e.complete&&void 0!==e.canvasId&&l.length>1){let t=l.length,r=(t-e.canvasId-1)/t+e.complete/t;e.complete=r}switch(e.msg){case i.default.capture:r(e.complete);let d=e.canvasId;if(!V(d)){let e=new Error(`Bad canvasId in capture request: ${d}`);return e.name="CaptureRequestError",u(e)}let g=c[d];if(g&&(e.clip.x+=g.left,e.clip.y+=g.top),0===d){const t={ww:e.windowWidth,wh:e.windowHeight,dpr:e.devicePixelRatio};l[d].updateMetadata(t)}return(null===(m=e.links)||void 0===m?void 0:m.length)&&l[d].appendMetadataLinks(e.links),C(t.windowId,e,p,l[d],a,e.isFrame?f:void 0).then(e=>{h(e||!0)}).catch(u),!0;case i.default.captureError:return u({name:E.CONTENT_SCRIPT,message:e.name+": "+e.message,stack:e.stack}),!1;case i.default.captureFrame:let w=l.length;l.push(new o.default(s.fmt_details.canvas));let b=["top","left","width","height","windowWidth"].filter(t=>!V(e[t]));if(b.length){let e=new Error(`Bad props on ${i.default.captureFrame} request: ${b.join(", ")}`);return e.name="CaptureFrameRequestError",u(e)}return c[w]=e,B(e.url,e.width,e.height,t,r,s,w,l[w],e.tagName).then(e=>{if(!e||0===e.canvases.length)return h({skip:!0});const t=e.scaleMultiplier,r=e.pageWidth,n=e.pageHeight,a=c[w],i=[["left","left"],["right","left"],["top","top"],["bottom","top"]];e.canvases.forEach(e=>{i.forEach(([r,n])=>{if(e[r]+=a[n]*t,isNaN(e[r])){const e=new Error(`attr isNaN: ${r}`);throw e.name="CanvasObjAttrError",e}})});const o=l[w];(o.metadata.links||[]).forEach(e=>{e.bounds.forEach(e=>{e.x+=a.left,e.y+=a.top})}),l[w-1].appendMetadataLinks(o.metadata.links),f=e,h({width:r,height:n})}).catch(u),!0;default:let v="Unknown message received from content script: "+e.msg;return n.default.error(v),u({name:E.UNKNOWN_MESSAGE,message:v}),!1}}catch(g){n.default.error(g),u(g)}}),P(t,null,r,s,0,l[0],!0).then(()=>{const t=l[0];t.sortLinks(),e(t)}).catch(e=>u(e))}));case 5:case"end":return e.stop()}},e)}));return function(t,r,n,a){return e.apply(this,arguments)}}();exports.captureToMultiCanvas=_;const O=function(){var e=y(regeneratorRuntime.mark(function e(t,r,n,i){var s,u,l,c,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r(0),e.next=3,t;case 3:return s=e.sent,r(.5),e.next=7,(0,a.loadImage)(s);case 7:return u=e.sent,l=u.img,c=u.width,p=u.height,r(1),(i=i||new o.default(n)).setObjs(c,p),i.drawImage(l,0,0,c,p),e.abrupt("return",i);case 16:case"end":return e.stop()}},e)}));return function(t,r,n,a){return e.apply(this,arguments)}}(),P=function(){var e=y(regeneratorRuntime.mark(function e(t,r,n,i,o,s,u){var c,p,f;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return c=6e3,e.next=3,A(t.url);case 3:return e.prev=3,e.next=6,(0,a.timeoutWrap)(H(t,r,"js/page/index.js"),c,"api._executeScript");case 6:e.next=16;break;case 8:if(e.prev=8,e.t0=e.catch(3),!u||!l.isExecuteScriptChromeError(e.t0)){e.next=15;break}return p=l.captureVisibleTab(t.windowId,i.fmt_details.capture),e.next=14,O(p,n,i.fmt_details.capture,s);case 14:return e.abrupt("return",T(s));case 15:throw e.t0;case 16:return n(0),f={msg:"scrollPage",canvasId:o,opts:i},r=r||0,e.next=21,l.sendMessage(t.id,f,{frameId:r});case 21:return s.applyBgRegions(),e.abrupt("return",T(s));case 23:case"end":return e.stop()}},e,null,[[3,8]])}));return function(t,r,n,a,i,o,s){return e.apply(this,arguments)}}(),T=e=>{const t=e.canvasObjs,r=e.scaleMultiplier||1;return{canvases:t,pageWidth:e.totalWidth/r,pageHeight:e.totalHeight/r,scaleMultiplier:r}};function A(e){return N.apply(this,arguments)}function N(){return(N=y(regeneratorRuntime.mark(function e(r){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!s.isFileUrl(r)){e.next=8;break}return e.next=4,s.isAllowedFileSchemeAccess();case 4:if(e.sent){e.next=8;break}return t.trigger("needFilePermsClick"),e.abrupt("return",new Promise(()=>{}));case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function C(e,t,r,n,a,i){return j.apply(this,arguments)}function j(){return(j=y(regeneratorRuntime.mark(function e(t,r,i,o,s,u){var c,p,f,d,h,m,g,w,b,v,x,y,E,R,k;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!u){e.next=3;break}return u.canvases.forEach(({canvas:e,left:t,top:r,width:n,height:a})=>{o.drawImage(e,t,r,n,a)}),e.abrupt("return",r);case 3:c="";case 4:return e.prev=5,e.next=8,l.captureVisibleTab(t,{format:"png"});case 8:return c=e.sent,e.abrupt("break",23);case 12:if(e.prev=12,e.t0=e.catch(5),!l.isCaptureVisibleTabQuotaError(e.t0)){e.next=20;break}return n.default.debug("Encountered capture quota error"),e.next=18,(0,a.sleep)(50);case 18:e.next=21;break;case 20:throw e.t0;case 21:e.next=4;break;case 23:if(c){e.next=27;break}throw(p=new Error("No dataURI generated for visible tab")).name="APICaptureStepError",p;case 27:return e.next=29,(0,a.loadImage)(c);case 29:return f=e.sent,d=f.img,h=f.width,m=f.height,r.image={width:h,height:m},g=i===h?1:h/i,o.setScaleMultiplier(g),1!==g&&o.scaleAll(r,["x","y","totalWidth","totalHeight","capture.*","clip.*"]),o.isEmpty()&&(o.setObjs(r.totalWidth,r.totalHeight,r.canvasBg),o.size()>1&&s&&s(o.size()),r.bgRegions&&(r.bgRegions.forEach(e=>o.scaleAll(e,["sample.*","fill.*"])),o.setBgRegions(r.bgRegions))),w=r.x+r.capture.x,b=r.y+r.capture.y,v=r.clip.width,x=r.clip.height,y=w-r.clip.x,E=b-r.clip.y,R=r.image.width,k=r.image.height,0===v||0===x?n.default.warn("Zero area mask!",JSON.stringify(r)):0===R||0===k?n.default.warn("Zero area for image!",JSON.stringify(r)):o.drawImage(d,y,E,R,k,r,{x:w,y:b,width:v,height:x}),e.abrupt("return",r);case 48:case"end":return e.stop()}},e,null,[[5,12]])}))).apply(this,arguments)}function B(e,r,a,i,o,s,p,f,d){let h=i.id;return Promise.resolve().then(()=>u.contains(k)).then(e=>!!e||(t.trigger("needFramePermsClick",{tagName:d}),t.oncePromise("clicked").then(e=>"ok"===e.action&&u.request(R)))).then(t=>t?c.getAllFrames({tabId:h}).then(t=>{if((t=t.filter(e=>0===e.parentFrameId)).length<=1)return t[0];let i=t.filter(t=>t.url===e);if(i.length>=1){if(1!==i.length){let t=`multiple frames found with url: ${e}`;n.default.warn(t)}return i[0]}return F(h,t,r,a)}).then(e=>e?P(i,e.frameId,o,s,p,f).catch(e=>{if(l.isExecuteScriptChromeError(e))return null;throw e}):null).then(e=>s.frame_persist?e:u.remove(R).then(()=>e)):null)}function F(e,t,r,n){let a=r*n;return Promise.all(t.map(t=>l.executeScript(e,{frameId:t.frameId,code:"[window.innerWidth, window.innerHeight]",matchAboutBlank:!0}).then(e=>{let t=h(e[0],2),i=t[0],o=t[1];return Math.abs(a+i*o-Math.min(r,i)*Math.min(n,o)*2)}))).then(e=>{let r=void 0,n=void 0;return e.forEach((e,a)=>{(void 0===r||e<r)&&(r=e,n=t[a])}),n})}function q(e){let t=atob(e.split(",")[1]),r=e.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(t.length),a=new Uint8Array(n);for(let i=0;i<t.length;i++)a[i]=t.charCodeAt(i);return new Blob([n],{type:r})}function W(e,t,n){if(!e){let e=new Error(`_saveBlobToFs got no blob: ${t}, ${n}`);return e.name=E.MISSING_BLOB,Promise.reject(e)}t=U(t,n);let a=e.size+2048;return r.default.requestFs(a).then(e=>r.default.getFile(e,t,{create:!0})).then(t=>r.default.writeFile(t,e)).then(()=>({filename:t,size:e.size}))}function U(e,t){if(!t)return e;let r=e.split("."),n=r.pop();return r.join(".")+"-"+(t+1)+"."+n}function L(e){let t=chrome.extension.getURL("");return e.url.substring(0,t.length)===t}function H(e,t,r){let a=""===e.url;if(L(e)){let t,a=chrome.extension.getURL(r);try{t=chrome.extension.getViews({type:"tab",tabId:e.id})}catch(i){t=chrome.extension.getViews({type:"tab"})}let o=t.filter(t=>t.location.href===e.url);return o.length||n.default.error("No matching window found for: "+e.url),Promise.all(o.map(e=>new Promise((t,r)=>{let n=e.document.createElement("script");n.src=a,n.addEventListener("load",t,!1),e.document.body.appendChild(n)})))}{let n={file:r};return"number"==typeof t&&(n.frameId=t,t>0&&(n.matchAboutBlank=!0)),a&&(n.matchAboutBlank=!0),l.executeScript(e.id,n)}}function $(){return(new Error).stack}const V=e=>"number"==typeof e;
},{"./db":"f9V3","./events":"GZY7","./filesystem":"sBdG","./log":"DrS6","./promise":"pPWs","./messages":"kp1E","./multicanvas":"L1Zq","./chrome.extension":"OFfI","./chrome.permissions":"WlKW","./chrome.tabs":"aDCn","./chrome.webnavigation":"Ce6c"}],"CyAK":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=require("./chrome.downloads"),t=require("./chrome.tabs"),o=require("./constants"),r=require("./util");const n={[e.ERROR_INCOGNITO_NETWORK_NAME]:{title:"Network error downloading file",body:"You might be experiencing a bug that was introduced to the Chrome browser in v68. In the meantime, you can try either of the following.\n\n1. right-click and “Save image as” or just click and drag the image to your desktop (if you are not using auto-download)\n\n2. disable “Allow in incognito” from the Chrome settings page for this extension (you can return to this capture from the history view within this extension):",buttons:()=>{let e=`${o.CUR_BROWSER_INFO.browser_protocol}extensions?id=${chrome.runtime.id}`,n=document.createElement("a");return n.href="#",n.innerText=e,Object.assign(n.style,{display:"block",fontWeight:"bold",margin:"1em 0"}),r.$.on(n,"click",o=>{o.preventDefault(),(0,t.getCurrentTab)().then(o=>(0,t.openInTabs)(o,[e]))}),[n]}}};var i=n;exports.default=i;
},{"./chrome.downloads":"t52Y","./chrome.tabs":"aDCn","./constants":"qwST","./util":"wT1R"}],"P6tM":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;const e=()=>{Array.prototype.slice.call(document.querySelectorAll("a[data-href]")).forEach(e=>{e.href=e.dataset.href})};var t=e;exports.default=t;
},{}],"lIDf":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){var e="chrome-extension://"+chrome.runtime.id,s=[{title:"Extensions gallery",explanation:"由于安全风险，Chrome 政策不允许扩展程序访问网上商店扩展程序库",starts_with:"https://chrome.google.com/webstore/",examples:["https://chrome.google.com/webstore/detail/full-page-screen-capture/fdpohaocaechififmbbbbbknoalclacl?hl=en-US"],error_message:"The extensions gallery cannot be scripted."},{title:"Chrome URL",explanation:"Chrome policy does not allow extensions to access URLs that start with “chrome://” because of security risks.",starts_with:"chrome://",examples:["chrome://extensions/","chrome://downloads/","chrome://apps/"],error_message:"Cannot access a chrome:// URL"},{title:"Own extension",explanation:"A recent release of Chrome has removed the ability from extensions to take screenshots of themselves. This is under review and may change. Stay tuned!",starts_with:e,examples:[e],error_message:"Issue screenshotting own chrome URL"},{title:"Another extension",explanation:"由于安全风险，Chrome 政策不允许扩展程序访问网上商店扩展程序库",starts_with:"chrome-extension://",examples:["chrome-extension://poompllcagmhgifahnbpaofdeikafkan/index.html"],error_message:"Cannot access a chrome-extension:// URL of different extension"},{title:"Extension file",explanation:"Chrome policy does not allow this extension to capture the current file. Is it already an image?",starts_with:"filesystem:chrome-extension://",examples:["filesystem:chrome-extension://poompllcagmhgifahnbpaofdeikafkan/temporary/screencapture-developer-chrome-extensions-manifest-web_accessible_resources-1517807774768.png"],error_message:"Cannot access contents of the page. Extension manifest must request permission to access the respective host."},{title:"Microsoft Add-ons Store",explanation:"The browser does not allow extensions to access the add-on store because of security risks.",starts_with:"https://microsoftedge.microsoft.com/",examples:["https://microsoftedge.microsoft.com/addons/detail/grammarly-for-microsoft-e/cnlefmmeadmemmdciolhbnfeacpdfbkd"],error_message:"The extensions gallery cannot be scripted."},{title:"Edge URL",explanation:"The browser does not allow extensions to access URLs that start with edge://” because of security risks.",starts_with:"edge://",examples:["edge://extensions","edge://downloads","edge://apps"],error_message:"Cannot access an edge:// URL"},{title:"Another extension",explanation:"The browser does not allow extensions to access to other extensions because of security risks.",starts_with:"extension://",examples:["extension://poompllcagmhgifahnbpaofdeikafkan/index.html"],error_message:"Cannot access an extension:// URL of different extension"},{title:"Extension file",explanation:"The browser is not allowing this extension to capture the current file. Is it already an image?",starts_with:"filesystem:extension://",examples:["filesystem:extension://poompllcagmhgifahnbpaofdeikafkan/temporary/screencapture-developer-chrome-extensions-manifest-web_accessible_resources-1517807774768.png"],error_message:"Cannot access contents of the page. Extension manifest must request permission to access the respective host."}];return{URLS:s,getMatch:function(e){return s.find(function(s){return e.startsWith(s.starts_with)})},isOwnUrl:function(s){return s.startsWith(e)}}}();exports.default=e;
},{}],"eLqP":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getReportUrl=p,exports.getReportUrlAsync=f,exports.update=d;var e=n(require("./log")),r=require("./util"),t=n(require("./stores/options-store"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function u(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?o(Object(t),!0).forEach(function(r){a(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function a(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function c(e,r,t,n,o,u,a){try{var c=e[u](a),i=c.value}catch(s){return void t(s)}c.done?r(i):Promise.resolve(i).then(n,o)}function i(e){return function(){var r=this,t=arguments;return new Promise(function(n,o){var u=e.apply(r,t);function a(e){c(u,n,o,a,i,"next",e)}function i(e){c(u,n,o,a,i,"throw",e)}a(void 0)})}}t.default.load().catch(r=>e.default.error(r));const s=new Set(["url","error","type","opts","userId","subState","metadata"]);function p(e){for(var n=0,o=Object.keys(e);n<o.length;n++){const r=o[n];if(!s.has(r)){const t=new Error(`Unexpected report link key: ${r} (${e[r]})`);throw t.name="UnexpectedKeyError",t}}let u=e.url,a=e.error,c=e.type,i=e.metadata,p=e.opts,f=e.userId,l=e.subState;u=u||window.location.href,p=p||t.default.state;let d=a?(0,r.errorToString)(a):void 0;return c&&(d=`[${c}]${d?" "+d:""}`),(0,r.reportLink)(u,d,p,f,l,i)}function f(e){return l.apply(this,arguments)}function l(){return(l=i(regeneratorRuntime.mark(function n(o){var a;return regeneratorRuntime.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,t.default.load();case 3:a=n.sent,n.next=10;break;case 6:n.prev=6,n.t0=n.catch(0),e.default.error(n.t0),a={error:(0,r.errorToString)(n.t0)};case 10:return n.abrupt("return",p(u(u({},o),{},{opts:y(a)})));case 11:case"end":return n.stop()}},n,null,[[0,6]])}))).apply(this,arguments)}function d(e,r){return b.apply(this,arguments)}function b(){return(b=i(regeneratorRuntime.mark(function t(n,o){var u,a,c,i;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return u=o&&o.metadata||void 0,t.next=3,f({url:window.location.href,error:n,metadata:u});case 3:return a=t.sent,c="btn-report",(i=(0,r.$)(c))?(i.href=a,r.$.show("btn-report")):e.default.error(`No reportElt #${c}`),t.abrupt("return",a);case 8:case"end":return t.stop()}},t)}))).apply(this,arguments)}const y=e=>(e=u({},e),t.default.fields.forEach(r=>{const t="function"==typeof r.default?r.default():r.default;e[r.name]===t&&delete e[r.name]}),e);
},{"./log":"DrS6","./util":"wT1R","./stores/options-store":"m7pw"}],"BoDy":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.wait=o;const e=100;let t=!1,n=[];function o(e){return new Promise((o,r)=>{if(t)return o();e&&e(),n.push(o)})}window.addEventListener("load",function(){window.setTimeout(()=>{t=!0,n.forEach(e=>e())},100)});
},{}],"n94p":[function(require,module,exports) {
!function(){var t=function(t){this.w=t||[]};t.prototype.set=function(t){this.w[t]=!0},t.prototype.encode=function(){for(var t=[],e=0;e<this.w.length;e++)this.w[e]&&(t[Math.floor(e/6)]^=1<<e%6);for(e=0;e<t.length;e++)t[e]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charAt(t[e]||0);return t.join("")+"~"};var e=new t;function n(t){e.set(t)}var i=function(n){n=a(n),n=new t(n);for(var i=e.w.slice(),r=0;r<n.w.length;r++)i[r]=i[r]||n.w[r];return new t(i).encode()},a=function(t){return t=t.get(Se),o(t)||(t=[]),t},r=function(t){return"function"==typeof t},o=function(t){return"[object Array]"==Object.prototype.toString.call(Object(t))},s=function(t){return null!=t&&-1<(t.constructor+"").indexOf("String")},c=function(t,e){return 0==t.indexOf(e)},u=function(t){return t?t.replace(/^[\s\xa0]+|[\s\xa0]+$/g,""):""},l=function(){for(var t=C.navigator.userAgent+(I.cookie?I.cookie:"")+(I.referrer?I.referrer:""),e=t.length,n=C.history.length;0<n;)t+=n--^e++;return[ht()^2147483647&Ui(t),Math.round((new Date).getTime()/1e3)].join(".")},f=function(t){var e=I.createElement("img");return e.width=1,e.height=1,e.src=t,e},g=function(){},h=function(t){return encodeURIComponent instanceof Function?encodeURIComponent(t):(n(28),t)},d=function(t,e,i,a){try{t.addEventListener?t.addEventListener(e,i,!!a):t.attachEvent&&t.attachEvent("on"+e,i)}catch(r){n(27)}},p=/^[\w\-:/.?=&%!]+$/,v=function(t,e,n){t&&(n?(n="",e&&p.test(e)&&(n=' id="'+e+'"'),p.test(t)&&I.write("<script"+n+' src="'+t+'"><\/script>')):((n=I.createElement("script")).type="text/javascript",n.async=!0,n.src=t,e&&(n.id=e),(t=I.getElementsByTagName("script")[0]).parentNode.insertBefore(n,t)))},m=function(t,e){return w(I.location[e?"href":"search"],t)},w=function(t,e){return(t=t.match("(?:&|#|\\?)"+h(e).replace(/([.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")+"=([^&#]*)"))&&2==t.length?t[1]:""},y=function(){var t=""+I.location.hostname;return 0==t.indexOf("www.")?t.substring(4):t},b=function(t,e){var n=t.indexOf(e);return(5==n||6==n)&&("/"==(t=t.charAt(n+e.length))||"?"==t||""==t||":"==t)},_=function(t,e){if(1==e.length&&null!=e[0]&&"object"==typeof e[0])return e[0];for(var n={},i=Math.min(t.length+1,e.length),a=0;a<i;a++){if("object"==typeof e[a]){for(var r in e[a])e[a].hasOwnProperty(r)&&(n[r]=e[a][r]);break}a<t.length&&(n[t[a]]=e[a])}return n},k=function(){this.keys=[],this.values={},this.m={}};k.prototype.set=function(t,e,n){this.keys.push(t),n?this.m[":"+t]=e:this.values[":"+t]=e},k.prototype.get=function(t){return this.m.hasOwnProperty(":"+t)?this.m[":"+t]:this.values[":"+t]},k.prototype.map=function(t){for(var e=0;e<this.keys.length;e++){var n=this.keys[e],i=this.get(n);i&&t(n,i)}};var O,x,T,S,j,C=window,I=document,R=function(t,e){return setTimeout(t,e)},A=window,E=document,N=function(t){var e=A._gaUserPrefs;if(e&&e.ioo&&e.ioo()||t&&!0===A["ga-disable-"+t])return!0;try{var n=A.external;if(n&&n._gaUserPrefs&&"oo"==n._gaUserPrefs)return!0}catch(r){}t=[],e=E.cookie.split(";"),n=/^\s*AMP_TOKEN=\s*(.*?)\s*$/;for(var i=0;i<e.length;i++){var a=e[i].match(n);a&&t.push(a[1])}for(e=0;e<t.length;e++)if("$OPT_OUT"==decodeURIComponent(t[e]))return!0;return!1},$=function(t){var e=[],n=I.cookie.split(";");t=new RegExp("^\\s*"+t+"=\\s*(.*?)\\s*$");for(var i=0;i<n.length;i++){var a=n[i].match(t);a&&e.push(a[1])}return e},D=function(t,e,n,i,a,r){if(!(a=!N(a)&&!(L.test(I.location.hostname)||"/"==n&&P.test(i))))return!1;if(e&&1200<e.length&&(e=e.substring(0,1200)),n=t+"="+e+"; path="+n+"; ",r&&(n+="expires="+new Date((new Date).getTime()+r).toGMTString()+"; "),i&&"none"!==i&&(n+="domain="+i+";"),i=I.cookie,I.cookie=n,!(i=i!=I.cookie))t:{for(t=$(t),i=0;i<t.length;i++)if(e==t[i]){i=!0;break t}i=!1}return i},M=function(t){return encodeURIComponent?encodeURIComponent(t).replace(/\(/g,"%28").replace(/\)/g,"%29"):t},P=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,L=/(^|\.)doubleclick\.net$/i,U=/^.*Version\/?(\d+)[^\d].*$/i,G=function(){if(void 0!==C.__ga4__)return C.__ga4__;if(void 0===O){var t=C.navigator.userAgent;if(t){var e=t;try{e=decodeURIComponent(t)}catch(n){}(t=!(0<=e.indexOf("Chrome")||0<=e.indexOf("CriOS")||!(0<=e.indexOf("Safari/")||0<=e.indexOf("Safari,"))))&&(t=11<=((e=U.exec(e))?Number(e[1]):-1)),O=t}else O=!1}return O},V=/^https?:\/\/[^/]*cdn\.ampproject\.org\//,F=[],H=function(){$i.D([g])},q=function t(e,i,a){if(!window.JSON)return n(58),!1;var r=C.XMLHttpRequest;if(!r)return n(59),!1;var o=new r;return"withCredentials"in o?(o.open("POST",(a||"https://ampcid.google.com/v1/publisher:getClientId")+"?key=AIzaSyA65lEHUEizIsNtlbNo-l2K18dT680nsaM",!0),o.withCredentials=!0,o.setRequestHeader("Content-Type","text/plain"),o.onload=function(){if(x=!1,4==o.readyState){try{200!=o.status&&(n(61),K("","$ERROR",3e4));var r=JSON.parse(o.responseText);r.optOut?(n(63),K("","$OPT_OUT",31536e6)):r.clientId?K(r.clientId,r.securityToken,31536e6):!a&&r.alternateUrl?(T&&clearTimeout(T),x=!0,t(e,i,r.alternateUrl)):(n(64),K("","$NOT_FOUND",36e5))}catch(s){n(65),K("","$ERROR",3e4)}o=null}},r={originScope:"AMP_ECID_GOOGLE"},e&&(r.securityToken=e),o.send(JSON.stringify(r)),T=R(function(){n(66),K("","$ERROR",3e4)},1e4),!0):(n(60),!1)},z=function(){x=!1},B=function(t,e){if(void 0===S){S="";for(var n=Ln(),i=0;i<n.length;i++){var a=n[i];if(D("AMP_TOKEN",encodeURIComponent(t),"/",a,"",e))return void(S=a)}}D("AMP_TOKEN",encodeURIComponent(t),"/",S,"",e)},K=function(t,e,n){for(T&&clearTimeout(T),e&&B(e,n),j=t,e=F,F=[],n=0;n<e.length;n++)e[n](t)},X=function(){return(At||"https:"==I.location.protocol?"https:":"http:")+"//www.google-analytics.com"},W=function(t){this.name="len",this.message=t+"-8192"},Y=function(t,e,n){if(n=n||g,2036>=e.length)Z(t,e,n);else{if(!(8192>=e.length))throw et("len",e.length),new W(e.length);tt(t,e,n)||Q(t,e,n)||Z(t,e,n)}},J=function(t,e,n,i){Q(t+"?"+e,"",i=i||g,n)},Z=function(t,e,n){var i=f(t+"?"+e);i.onload=i.onerror=function(){i.onload=null,i.onerror=null,n()}},Q=function(t,e,n,i){var a=C.XMLHttpRequest;if(!a)return!1;var r=new a;return"withCredentials"in r&&(t=t.replace(/^http:/,"https:"),r.open("POST",t,!0),r.withCredentials=!0,r.setRequestHeader("Content-Type","text/plain"),r.onreadystatechange=function(){if(4==r.readyState){if(i)try{var t=r.responseText;if(1>t.length)et("xhr","ver","0"),n();else if("1"!=t.charAt(0))et("xhr","ver",String(t.length)),n();else if(3<i.count++)et("xhr","tmr",""+i.count),n();else if(1==t.length)n();else{var e=t.charAt(1);if("d"==e)J("https://stats.g.doubleclick.net/j/collect",i.U,i,n);else if("g"==e){var a="https://www.google.%/ads/ga-audiences".replace("%","com");Z(a,i.google,n);var o=t.substring(2);if(o)if(/^[a-z.]{1,6}$/.test(o)){var s="https://www.google.%/ads/ga-audiences".replace("%",o);Z(s,i.google,g)}else et("tld","bcc",o)}else et("xhr","brc",e),n()}}catch(c){et("xhr","rsp"),n()}else n();r=null}},r.send(e),!0)},tt=function(t,e,n){return!!C.navigator.sendBeacon&&(!!C.navigator.sendBeacon(t,e)&&(n(),!0))},et=function(t,e,n){1<=100*Math.random()||N("?")||(t=["t=error","_e="+t,"_v=j68","sr=1"],e&&t.push("_f="+e),n&&t.push("_m="+h(n.substring(0,100))),t.push("aip=1"),t.push("z="+ht()),Z("https://www.google-analytics.com/u/d",t.join("&"),g))},nt=function(t){var e=C.gaData=C.gaData||{};return e[t]=e[t]||{}},it=function(){this.M=[]};function at(t){if(100!=t.get(sn)&&Ui(yt(t,ze))%1e4>=100*bt(t,sn))throw"abort"}function rt(t){if(N(yt(t,We)))throw"abort"}function ot(){var t=I.location.protocol;if("http:"!=t&&"https:"!=t)throw"abort"}function st(t){try{C.navigator.sendBeacon?n(42):C.XMLHttpRequest&&"withCredentials"in new C.XMLHttpRequest&&n(40)}catch(a){}t.set(Te,i(t),!0),t.set(Gt,bt(t,Gt)+1);var e=[];mt.map(function(n,i){i.F&&(null!=(n=t.get(n))&&n!=i.defaultValue&&("boolean"==typeof n&&(n*=1),e.push(i.F+"="+h(""+n))))}),e.push("z="+dt()),t.set(Pt,e.join("&"),!0)}function ct(t){var e=yt(t,wn)||X()+"/collect",n=t.get(bn),i=yt(t,Ut);if(!i&&t.get(Lt)&&(i="beacon"),n)J(e,yt(t,Pt),n,t.get(Mt));else if(i){n=i,i=yt(t,Pt);var a=t.get(Mt);a=a||g,"image"==n?Z(e,i,a):"xhr"==n&&Q(e,i,a)||"beacon"==n&&tt(e,i,a)||Y(e,i,a)}else Y(e,yt(t,Pt),t.get(Mt));e=t.get(We),n=(e=nt(e)).hitcount,e.hitcount=n?n+1:1,e=t.get(We),delete nt(e).pending_experiments,t.set(Mt,g,!0)}function ut(t){(C.gaData=C.gaData||{}).expId&&t.set(ve,(C.gaData=C.gaData||{}).expId),(C.gaData=C.gaData||{}).expVar&&t.set(me,(C.gaData=C.gaData||{}).expVar);var e=t.get(We);if(e=nt(e).pending_experiments){var n=[];for(i in e)e.hasOwnProperty(i)&&e[i]&&n.push(encodeURIComponent(i)+"."+encodeURIComponent(e[i]));var i=n.join("!")}else i=void 0;i&&t.set(we,i,!0)}function lt(){if(C.navigator&&"preview"==C.navigator.loadPurpose)throw"abort"}function ft(t){var e=C.gaDevIds;o(e)&&0!=e.length&&t.set("&did",e.join(","),!0)}function gt(t){if(!t.get(We))throw"abort"}it.prototype.add=function(t){this.M.push(t)},it.prototype.D=function(t){try{for(var e=0;e<this.M.length;e++){var n=t.get(this.M[e]);n&&r(n)&&n.call(C,t)}}catch(i){}(e=t.get(Mt))!=g&&r(e)&&(t.set(Mt,g,!0),setTimeout(e,10))};var ht=function(){return Math.round(2147483647*Math.random())},dt=function(){try{var t=new Uint32Array(1);return C.crypto.getRandomValues(t),2147483647&t[0]}catch(e){return ht()}};function pt(t){var e=bt(t,ke);500<=e&&n(15);var i=yt(t,Dt);if("transaction"!=i&&"item"!=i){i=bt(t,xe);var a=(new Date).getTime(),r=bt(t,Oe);if(0==r&&t.set(Oe,a),0<(r=Math.round(2*(a-r)/1e3))&&(i=Math.min(i+r,20),t.set(Oe,a)),0>=i)throw"abort";t.set(xe,--i)}t.set(ke,++e)}var vt=function(){this.data=new k},mt=new k,wt=[];vt.prototype.get=function(t){var e=Ot(t),n=this.data.get(t);return e&&null==n&&(n=r(e.defaultValue)?e.defaultValue():e.defaultValue),e&&e.Z?e.Z(this,t,n):n};var yt=function(t,e){return null==(t=t.get(e))?"":""+t},bt=function(t,e){return null==(t=t.get(e))||""===t?0:1*t};vt.prototype.set=function(t,e,n){if(t)if("object"==typeof t)for(var i in t)t.hasOwnProperty(i)&&_t(this,i,t[i],n);else _t(this,t,e,n)};var _t=function(t,e,n,i){if(null!=n)switch(e){case We:di.test(n)}var a=Ot(e);a&&a.o?a.o(t,e,n,i):t.data.set(e,n,i)},kt=function(t,e,n,i,a){this.name=t,this.F=e,this.Z=i,this.o=a,this.defaultValue=n},Ot=function(t){var e=mt.get(t);if(!e)for(var n=0;n<wt.length;n++){var i=wt[n],a=i[0].exec(t);if(a){e=i[1](a),mt.set(e.name,e);break}}return e},xt=function(t,e,n,i,a){return t=new kt(t,e,n,i,a),mt.set(t.name,t),t.name},Tt=function(t,e){wt.push([new RegExp("^"+t+"$"),e])},St=function(t,e,n){return xt(t,e,n,void 0,jt)},jt=function(){},Ct=s(window.GoogleAnalyticsObject)&&u(window.GoogleAnalyticsObject)||"ga",It=/^(?:utma\.)?\d+\.\d+$/,Rt=/^amp-[\w.-]{22,64}$/,At=!1,Et=St("apiVersion","v"),Nt=St("clientVersion","_v");xt("anonymizeIp","aip");var $t=xt("adSenseId","a"),Dt=xt("hitType","t"),Mt=xt("hitCallback"),Pt=xt("hitPayload");xt("nonInteraction","ni"),xt("currencyCode","cu"),xt("dataSource","ds");var Lt=xt("useBeacon",void 0,!1),Ut=xt("transport");xt("sessionControl","sc",""),xt("sessionGroup","sg"),xt("queueTime","qt");var Gt=xt("_s","_s");xt("screenName","cd");var Vt=xt("location","dl",""),Ft=xt("referrer","dr"),Ht=xt("page","dp","");xt("hostname","dh");var qt=xt("language","ul"),zt=xt("encoding","de");xt("title","dt",function(){return I.title||void 0}),Tt("contentGroup([0-9]+)",function(t){return new kt(t[0],"cg"+t[1])});var Bt=xt("screenColors","sd"),Kt=xt("screenResolution","sr"),Xt=xt("viewportSize","vp"),Wt=xt("javaEnabled","je"),Yt=xt("flashVersion","fl");xt("campaignId","ci"),xt("campaignName","cn"),xt("campaignSource","cs"),xt("campaignMedium","cm"),xt("campaignKeyword","ck"),xt("campaignContent","cc");var Jt=xt("eventCategory","ec"),Zt=xt("eventAction","ea"),Qt=xt("eventLabel","el"),te=xt("eventValue","ev"),ee=xt("socialNetwork","sn"),ne=xt("socialAction","sa"),ie=xt("socialTarget","st"),ae=xt("l1","plt"),re=xt("l2","pdt"),oe=xt("l3","dns"),se=xt("l4","rrt"),ce=xt("l5","srt"),ue=xt("l6","tcp"),le=xt("l7","dit"),fe=xt("l8","clt"),ge=xt("timingCategory","utc"),he=xt("timingVar","utv"),de=xt("timingLabel","utl"),pe=xt("timingValue","utt");xt("appName","an"),xt("appVersion","av",""),xt("appId","aid",""),xt("appInstallerId","aiid",""),xt("exDescription","exd"),xt("exFatal","exf");var ve=xt("expId","xid"),me=xt("expVar","xvar"),we=xt("exp","exp"),ye=xt("_utma","_utma"),be=xt("_utmz","_utmz"),_e=xt("_utmht","_utmht"),ke=xt("_hc",void 0,0),Oe=xt("_ti",void 0,0),xe=xt("_to",void 0,20);Tt("dimension([0-9]+)",function(t){return new kt(t[0],"cd"+t[1])}),Tt("metric([0-9]+)",function(t){return new kt(t[0],"cm"+t[1])}),xt("linkerParam",void 0,void 0,function(t){var e=t.get(ze),i=t.get(ln)||"";if(e="_ga=2."+h(Xn(i+e,0)+"."+i+"-"+e),(i=t.get(hn))&&t.get(mn)){var a=bt(t,dn);1e3*a+bt(t,pn)<=(new Date).getTime()?(n(76),t=""):(n(44),t="&_gac=1."+h([Xn(i,0),a,i].join(".")))}else t="";return e+t},jt);var Te=xt("usage","_u"),Se=xt("_um");xt("forceSSL",void 0,void 0,function(){return At},function(t,e,i){n(34),At=!!i});var je=xt("_j1","jid"),Ce=xt("_j2","gjid");Tt("\\&(.*)",function(t){var e=new kt(t[0],t[1]),n=function(t){var e;return mt.map(function(n,i){i.F==t&&(e=i)}),e&&e.name}(t[0].substring(1));return n&&(e.Z=function(t){return t.get(n)},e.o=function(t,e,i,a){t.set(n,i,a)},e.F=void 0),e});var Ie=St("_oot"),Re=xt("previewTask"),Ae=xt("checkProtocolTask"),Ee=xt("validationTask"),Ne=xt("checkStorageTask"),$e=xt("historyImportTask"),De=xt("samplerTask"),Me=xt("_rlt"),Pe=xt("buildHitTask"),Le=xt("sendHitTask"),Ue=xt("ceTask"),Ge=xt("devIdTask"),Ve=xt("timingTask"),Fe=xt("displayFeaturesTask"),He=xt("customTask"),qe=St("name"),ze=St("clientId","cid"),Be=St("clientIdTime"),Ke=St("storedClientId"),Xe=xt("userId","uid"),We=St("trackingId","tid"),Ye=St("cookieName",void 0,"_ga"),Je=St("cookieDomain"),Ze=St("cookiePath",void 0,"/"),Qe=St("cookieExpires",void 0,63072e3),tn=St("cookieUpdate",void 0,!0),en=St("legacyCookieDomain"),nn=St("legacyHistoryImport",void 0,!0),an=St("storage",void 0,"cookie"),rn=St("allowLinker",void 0,!1),on=St("allowAnchor",void 0,!0),sn=St("sampleRate","sf",100),cn=St("siteSpeedSampleRate",void 0,1),un=St("alwaysSendReferrer",void 0,!1),ln=St("_gid","_gid"),fn=St("_gcn"),gn=St("useAmpClientId"),hn=St("_gclid"),dn=St("_gt"),pn=St("_ge",void 0,7776e6),vn=St("_gclsrc"),mn=St("storeGac",void 0,!0),wn=xt("transportUrl"),yn=xt("_r","_r"),bn=xt("_dp"),_n=xt("allowAdFeatures",void 0,!0);function kn(t,e,i,a){e[t]=function(){try{return a&&n(a),i.apply(this,arguments)}catch(e){throw et("exc",t,e&&e.name),e}}}var On=function(){this.V=100,this.$=this.fa=!1,this.oa="detourexp",this.groups=1};var xn=function(t){var e=C.performance||C.webkitPerformance;if(!(e=e&&e.timing))return!1;var n=e.navigationStart;return 0!=n&&(t[ae]=e.loadEventStart-n,t[oe]=e.domainLookupEnd-e.domainLookupStart,t[ue]=e.connectEnd-e.connectStart,t[ce]=e.responseStart-e.requestStart,t[re]=e.responseEnd-e.responseStart,t[se]=e.fetchStart-n,t[le]=e.domInteractive-n,t[fe]=e.domContentLoadedEventStart-n,!0)},Tn=function(t){if(C.top!=C)return!1;var e=C.external,n=e&&e.onloadT;return e&&!e.isValidLoadTime&&(n=void 0),2147483648<n&&(n=void 0),0<n&&e.setPageReadyTime(),null!=n&&(t[ae]=n,!0)},Sn=function(t,e){var n=t[e];(isNaN(n)||1/0==n||0>n)&&(t[e]=void 0)},jn=function(t){return function(e){if("pageview"==e.get(Dt)&&!t.I){t.I=!0;var n=function(t){var e=Math.min(bt(t,cn),100);return!(Ui(yt(t,ze))%100>=e)}(e),i=0<w(e.get(Vt),"gclid").length;(n||i)&&function t(e){var n={};if(xn(n)||Tn(n)){var i=n[ae];null==i||1/0==i||isNaN(i)||(0<i?(Sn(n,oe),Sn(n,ue),Sn(n,ce),Sn(n,re),Sn(n,se),Sn(n,le),Sn(n,fe),R(function(){e(n)},10)):d(C,"load",function(){t(e)},!1))}}(function(e){n&&t.send("timing",e),i&&t.send("adtiming",e)})}}},Cn=!1,In=function(t){if("cookie"==yt(t,an)){if(t.get(tn)||yt(t,Ke)!=yt(t,ze)){var e=1e3*bt(t,Qe);Rn(t,ze,Ye,e)}if(Rn(t,ln,fn,864e5),t.get(mn)){var i=t.get(hn);if(i){var a=Math.min(bt(t,pn),1e3*bt(t,Qe));a=Math.min(a,1e3*bt(t,dn)+a-(new Date).getTime()),t.data.set(pn,a),e={};var r=t.get(dn),o=t.get(vn),s=Un(yt(t,Ze)),c=Pn(yt(t,Je)),u=yt(t,We);o&&"aw.ds"!=o?e&&(e.ua=!0):(i=["1",r,M(i)].join("."),0<a&&(e&&(e.ta=!0),D("_gac_"+M(u),i,s,c,u,a))),Vn(e)}}else n(75);(t="none"===Pn(yt(t,Je)))&&(t=I.location.hostname,t=L.test(t)||P.test(t)),t&&n(30)}},Rn=function(t,e,i,a){var r=Nn(t,e);if(r){i=yt(t,i);var o=Un(yt(t,Ze)),s=Pn(yt(t,Je)),c=yt(t,We);if("auto"!=s)D(i,r,o,s,c,a)&&(Cn=!0);else{n(32);for(var u=Ln(),l=0;l<u.length;l++)if(s=u[l],t.data.set(Je,s),r=Nn(t,e),D(i,r,o,s,c,a))return void(Cn=!0);t.data.set(Je,"auto")}}},An=function(t){if("cookie"==yt(t,an)&&!Cn&&(In(t),!Cn))throw"abort"},En=function(t){if(t.get(nn)){var e=yt(t,Je),i=yt(t,en)||y(),a=Fn("__utma",i,e);a&&(n(19),t.set(_e,(new Date).getTime(),!0),t.set(ye,a.R),(e=Fn("__utmz",i,e))&&a.hash==e.hash&&t.set(be,e.R))}},Nn=function(t,e){e=M(yt(t,e));var n=Pn(yt(t,Je)).split(".").length;return 1<(t=Gn(yt(t,Ze)))&&(n+="-"+t),e?["GA1",n,e].join("."):""},$n=function(t,e){return Dn(e,yt(t,Je),yt(t,Ze))},Dn=function(t,e,i){if(!t||1>t.length)n(12);else{for(var a=[],r=0;r<t.length;r++){var o=t[r],s=o.split("."),c=s.shift();("GA1"==c||"1"==c)&&1<s.length?(1==(o=s.shift().split("-")).length&&(o[1]="1"),o[0]*=1,o[1]*=1,s={H:o,s:s.join(".")}):s=Rt.test(o)?{H:[0,0],s:o}:void 0,s&&a.push(s)}if(1==a.length)return n(13),a[0].s;if(0!=a.length)return n(14),1==(a=Mn(a,Pn(e).split(".").length,0)).length?a[0].s:(1<(a=Mn(a,Gn(i),1)).length&&n(41),a[0]&&a[0].s);n(12)}},Mn=function(t,e,n){for(var i,a=[],r=[],o=0;o<t.length;o++){var s=t[o];s.H[n]==e?a.push(s):null==i||s.H[n]<i?(r=[s],i=s.H[n]):s.H[n]==i&&r.push(s)}return 0<a.length?a:r},Pn=function(t){return 0==t.indexOf(".")?t.substr(1):t},Ln=function(){var t=[],e=y().split(".");if(4==e.length){var n=e[e.length-1];if(parseInt(n,10)==n)return["none"]}for(n=e.length-2;0<=n;n--)t.push(e.slice(n).join("."));return t.push("none"),t},Un=function(t){return t?(1<t.length&&t.lastIndexOf("/")==t.length-1&&(t=t.substr(0,t.length-1)),0!=t.indexOf("/")&&(t="/"+t),t):"/"},Gn=function(t){return"/"==(t=Un(t))?1:t.split("/").length},Vn=function(t){t.ta&&n(77),t.na&&n(74),t.pa&&n(73),t.ua&&n(69)};function Fn(t,e,n){"none"==e&&(e="");var i=[],a=$(t);t="__utma"==t?6:2;for(var r=0;r<a.length;r++){var o=(""+a[r]).split(".");o.length>=t&&i.push({hash:o[0],R:a[r],O:o})}if(0!=i.length)return 1==i.length?i[0]:Hn(e,i)||Hn(n,i)||Hn(null,i)||i[0]}function Hn(t,e){if(null==t)var n=t=1;else n=Ui(t),t=Ui(c(t,".")?t.substring(1):"."+t);for(var i=0;i<e.length;i++)if(e[i].hash==n||e[i].hash==t)return e[i]}var qn=new RegExp(/^https?:\/\/([^\/:]+)/),zn=/(.*)([?&#])(?:_ga=[^&#]*)(?:&?)(.*)/,Bn=/(.*)([?&#])(?:_gac=[^&#]*)(?:&?)(.*)/;function Kn(t,e){var n=new Date,i=C.navigator,a=i.plugins||[];for(t=[t,i.userAgent,n.getTimezoneOffset(),n.getYear(),n.getDate(),n.getHours(),n.getMinutes()+e],e=0;e<a.length;++e)t.push(a[e].description);return Ui(t.join("."))}function Xn(t,e){var n=new Date,i=C.navigator,a=n.getHours()+Math.floor((n.getMinutes()+e)/60);return Ui([t,i.userAgent,i.language||"",n.getTimezoneOffset(),n.getYear(),n.getDate()+Math.floor(a/24),(24+a)%24,(60+n.getMinutes()+e)%60].join("."))}var Wn=function(t){n(48),this.target=t,this.T=!1};Wn.prototype.ca=function(t,e){if(t.tagName){if("a"==t.tagName.toLowerCase())return void(t.href&&(t.href=Yn(this,t.href,e)));if("form"==t.tagName.toLowerCase())return Jn(this,t)}if("string"==typeof t)return Yn(this,t,e)};var Yn=function(t,e,n){var i=zn.exec(e);i&&3<=i.length&&(e=i[1]+(i[3]?i[2]+i[3]:"")),(i=Bn.exec(e))&&3<=i.length&&(e=i[1]+(i[3]?i[2]+i[3]:"")),t=t.target.get("linkerParam");var a=e.indexOf("?");return i=e.indexOf("#"),n?e+=(-1==i?"#":"&")+t:(n=-1==a?"?":"&",e=-1==i?e+(n+t):e.substring(0,i)+n+t+e.substring(i)),(e=e.replace(/&+_ga=/,"&_ga=")).replace(/&+_gac=/,"&_gac=")},Jn=function(t,e){if(e&&e.action)if("get"==e.method.toLowerCase()){t=t.target.get("linkerParam").split("&");for(var n=0;n<t.length;n++){var i=t[n].split("="),a=i[1];i=i[0];for(var r=e.childNodes||[],o=!1,s=0;s<r.length;s++)if(r[s].name==i){r[s].setAttribute("value",a),o=!0;break}o||((r=I.createElement("input")).setAttribute("type","hidden"),r.setAttribute("name",i),r.setAttribute("value",a),e.appendChild(r))}}else"post"==e.method.toLowerCase()&&(e.action=Yn(t,e.action))};function Zn(t,e){if(e==I.location.hostname)return!1;for(var n=0;n<t.length;n++)if(t[n]instanceof RegExp){if(t[n].test(e))return!0}else if(0<=e.indexOf(t[n]))return!0;return!1}function Qn(t,e){return e!=Kn(t,0)&&e!=Kn(t,-1)&&e!=Kn(t,-2)&&e!=Xn(t,0)&&e!=Xn(t,-1)&&e!=Xn(t,-2)}Wn.prototype.S=function(t,e,i){function a(i){try{i=i||C.event;t:{var a=i.target||i.srcElement;for(i=100;a&&0<i;){if(a.href&&a.nodeName.match(/^a(?:rea)?$/i)){var o=a;break t}a=a.parentNode,i--}o={}}("http:"==o.protocol||"https:"==o.protocol)&&Zn(t,o.hostname||"")&&o.href&&(o.href=Yn(r,o.href,e))}catch(s){n(26)}}var r=this;this.T||(this.T=!0,d(I,"mousedown",a,!1),d(I,"keyup",a,!1)),i&&d(I,"submit",function(e){if((e=(e=e||C.event).target||e.srcElement)&&e.action){var n=e.action.match(qn);n&&Zn(t,n[1])&&Jn(r,e)}})};var ti,ei=/^(GTM|OPT)-[A-Z0-9]+$/,ni=/;_gaexp=[^;]*/g,ii=/;((__utma=)|([^;=]+=GAX?\d+\.))[^;]*/g,ai=/^https?:\/\/[\w\-.]+\.google.com(:\d+)?\/optimize\/opt-launch\.html\?.*$/,ri=function(t,e,n){this.aa=e,(e=n)||(e=(e=yt(t,qe))&&"t0"!=e?fi.test(e)?"_gat_"+M(yt(t,We)):"_gat_"+M(e):"_gat"),this.Y=e,this.ra=null},oi=function(t,e,n){!1===e.get(_n)||e.get(n)||("1"==$(t.Y)[0]?e.set(n,"",!0):e.set(n,""+ht(),!0))},si=function(t,e){ci(e)&&D(t.Y,"1",e.get(Ze),e.get(Je),e.get(We),6e4)},ci=function(t){return!!t.get(je)&&t.get(_n)},ui=function(t,e,n){var a=new k,r=function(t){Ot(t).F&&a.set(Ot(t).F,e.get(t))};r(Et),r(Nt),r(We),r(ze),r(je),0!=n&&1!=n||(r(Xe),r(Ce),r(ln)),a.set(Ot(Te).F,i(e));var o="";return a.map(function(t,e){o+=h(t)+"=",o+=h(""+e)+"&"}),o+="z="+ht(),0==n?o=t.aa+o:1==n?o="t=dc&aip=1&_r=3&"+o:2==n&&(o="t=sr&aip=1&_r=4&slf_rd=1&"+o),o},li=function(t,e){return null===t.ra&&(t.ra=1===function(t){var e,n=new On;if(n.fa&&n.$)return 0;if(n.$=!0,t){if(n.oa&&void 0!==t.get(n.oa))return bt(t,n.oa);if(0==t.get(cn))return 0}return 0==n.V?0:(void 0===e&&(e=dt()),0==e%n.V?Math.floor(e/n.V)%n.groups+1:0)}(e),t.ra&&n(33)),t.ra},fi=/^gtm\d+$/,gi=function(e,i){if(!(e=e.b).get("dcLoaded")){var r,o=new t(a(e));o.set(29),e.set(Se,o.w),(i=i||{})[Ye]&&(r=M(i[Ye])),function(t,e){var i=e.get(Pe);e.set(Pe,function(e){oi(t,e,je),oi(t,e,Ce);var n=i(e);return si(t,e),n});var a=e.get(Le);e.set(Le,function(e){var i=a(e);if(ci(e)){if(G()!==li(t,e)){n(80);var r={U:ui(t,e,1),google:ui(t,e,2),count:0};J("https://stats.g.doubleclick.net/j/collect",r.U,r)}else f(ui(t,e,0));e.set(je,"",!0)}return i})}(i=new ri(e,"https://stats.g.doubleclick.net/r/collect?t=dc&aip=1&_r=3&",r),e),e.set("dcLoaded",!0)}},hi=function(t){if(!t.get("dcLoaded")&&"cookie"==t.get(an)){var e=new ri(t);if(oi(e,t,je),oi(e,t,Ce),si(e,t),ci(t)){var i=G()!==li(e,t);t.set(yn,1,!0),i?(n(79),t.set(wn,X()+"/j/collect",!0),t.set(bn,{U:ui(e,t,1),google:ui(e,t,2),count:0},!0)):t.set(wn,X()+"/r/collect",!0)}}},di=/^(UA|YT|MO|GP)-(\d+)-(\d+)$/,pi=function(t){function e(t,e){i.b.data.set(t,e)}function n(t,n){e(t,n),i.filters.add(t)}var i=this;this.b=new vt,this.filters=new it,e(qe,t[qe]),e(We,u(t[We])),e(Ye,t[Ye]),e(Je,t[Je]||y()),e(Ze,t[Ze]),e(Qe,t[Qe]),e(tn,t[tn]),e(en,t[en]),e(nn,t[nn]),e(rn,t[rn]),e(on,t[on]),e(sn,t[sn]),e(cn,t[cn]),e(un,t[un]),e(an,t[an]),e(Xe,t[Xe]),e(Be,t[Be]),e(gn,t[gn]),e(mn,t[mn]),e(Et,1),e(Nt,"j68"),n(Ie,rt),n(He,g),n(Re,lt),n(Ae,ot),n(Ee,gt),n(Ne,An),n($e,En),n(De,at),n(Me,pt),n(Ue,ut),n(Ge,ft),n(Fe,hi),n(Pe,st),n(Le,ct),n(Ve,jn(this)),mi(this.b),vi(this.b,t[ze]),this.b.set($t,function(){var t=C.gaGlobal=C.gaGlobal||{};return t.hid=t.hid||ht()}()),function(t,e,n){if(!ti){var i=I.location.hash,a=C.name,r=/^#?gaso=([^&]*)/;(a=(i=(i=i&&i.match(r)||a&&a.match(r))?i[1]:$("GASO")[0]||"")&&i.match(/^(?:!([-0-9a-z.]{1,40})!)?([-.\w]{10,1200})$/i))&&(D("GASO",""+i,n,e,t,0),window._udo||(window._udo=e),window._utcp||(window._utcp=n),t=a[1],v("https://www.google.com/analytics/web/inpage/pub/inpage.js?"+(t?"prefix="+t+"&":"")+ht(),"_gasojs")),ti=!0}}(this.b.get(We),this.b.get(Je),this.b.get(Ze))},vi=function(t,e){var i=yt(t,Ye);if(t.data.set(fn,"_ga"==i?"_gid":i+"_gid"),"cookie"==yt(t,an)){if(Cn=!1,i=$(yt(t,Ye)),!(i=$n(t,i))){i=yt(t,Je);var a=yt(t,en)||y();null!=(i=Fn("__utma",a,i))?(n(10),i=i.O[1]+"."+i.O[2]):i=void 0}if(i&&(Cn=!0),a=i&&!t.get(tn))if(2!=(a=i.split(".")).length)a=!1;else if(a=Number(a[1])){var r=bt(t,Qe);a=a+r<(new Date).getTime()/1e3}else a=!1;if(a&&(i=void 0),i&&(t.data.set(Ke,i),t.data.set(ze,i),i=$(yt(t,fn)),(i=$n(t,i))&&t.data.set(ln,i)),t.get(mn)&&(i=t.get(hn),a=t.get(vn),!i||a&&"aw.ds"!=a)){if(i={},I){a=[],r=I.cookie.split(";");for(var o=/^\s*_gac_(UA-\d+-\d+)=\s*(.+?)\s*$/,s=0;s<r.length;s++){var c=r[s].match(o);c&&a.push({ja:c[1],value:c[2]})}if(r={},a&&a.length)for(o=0;o<a.length;o++)"1"!=(s=a[o].value.split("."))[0]||3!=s.length?i&&(i.na=!0):s[1]&&(r[a[o].ja]?i&&(i.pa=!0):r[a[o].ja]=[],r[a[o].ja].push({timestamp:s[1],qa:s[2]}));a=r}else a={};a=a[yt(t,We)],Vn(i),a&&0!=a.length&&(i=a[0],t.data.set(dn,i.timestamp),t.data.set(hn,i.qa))}}if(t.get(tn))t:if(a=m("_ga",t.get(on)))if(t.get(rn))if(-1==(i=a.indexOf(".")))n(22);else{if(r=a.substring(0,i),i=(o=a.substring(i+1)).indexOf("."),a=o.substring(0,i),o=o.substring(i+1),"1"==r){if(Qn(i=o,a)){n(23);break t}}else{if("2"!=r){n(22);break t}if(r="",0<(i=o.indexOf("-"))?(r=o.substring(0,i),i=o.substring(i+1)):i=o.substring(1),Qn(r+i,a)){n(53);break t}r&&(n(2),t.data.set(ln,r))}n(11),t.data.set(ze,i),(i=m("_gac",t.get(on)))&&("1"!=(i=i.split("."))[0]||4!=i.length?n(72):Qn(i[3],i[1])?n(71):(t.data.set(hn,i[3]),t.data.set(dn,i[2]),n(70)))}else n(21);e&&(n(9),t.data.set(ze,h(e))),t.get(ze)||((e=(e=C.gaGlobal&&C.gaGlobal.vid)&&-1!=e.search(It)?e:void 0)?(n(17),t.data.set(ze,e)):(n(8),t.data.set(ze,l()))),t.get(ln)||(n(3),t.data.set(ln,l())),In(t)},mi=function(t){var e=C.navigator,i=C.screen,a=I.location;if(t.set(Ft,function(t,e){var n=I.referrer;if(/^(https?|android-app):\/\//i.test(n)){if(t)return n;if(t="//"+I.location.hostname,!b(n,t))return e&&(e=t.replace(/\./g,"-")+".cdn.ampproject.org",b(n,e))?void 0:n}}(t.get(un),t.get(gn))),a){var r=a.pathname||"";"/"!=r.charAt(0)&&(n(31),r="/"+r),t.set(Vt,a.protocol+"//"+a.hostname+r+a.search)}i&&t.set(Kt,i.width+"x"+i.height),i&&t.set(Bt,i.colorDepth+"-bit"),i=I.documentElement;var o=(r=I.body)&&r.clientWidth&&r.clientHeight,s=[];if(i&&i.clientWidth&&i.clientHeight&&("CSS1Compat"===I.compatMode||!o)?s=[i.clientWidth,i.clientHeight]:o&&(s=[r.clientWidth,r.clientHeight]),i=0>=s[0]||0>=s[1]?"":s.join("x"),t.set(Xt,i),t.set(Yt,function(){var t,e;if((e=(e=C.navigator)?e.plugins:null)&&e.length)for(var n=0;n<e.length&&!t;n++){var i=e[n];-1<i.name.indexOf("Shockwave Flash")&&(t=i.description)}if(!t)try{var a=new ActiveXObject("ShockwaveFlash.ShockwaveFlash.7");t=a.GetVariable("$version")}catch(o){}if(!t)try{a=new ActiveXObject("ShockwaveFlash.ShockwaveFlash.6"),t="WIN 6,0,21,0",a.AllowScriptAccess="always",t=a.GetVariable("$version")}catch(o){}if(!t)try{t=(a=new ActiveXObject("ShockwaveFlash.ShockwaveFlash")).GetVariable("$version")}catch(o){}return t&&(a=t.match(/[\d]+/g))&&3<=a.length&&(t=a[0]+"."+a[1]+" r"+a[2]),t||void 0}()),t.set(zt,I.characterSet||I.charset),t.set(Wt,e&&"function"==typeof e.javaEnabled&&e.javaEnabled()||!1),t.set(qt,(e&&(e.language||e.browserLanguage)||"").toLowerCase()),t.data.set(hn,m("gclid",!0)),t.data.set(vn,m("gclsrc",!0)),t.data.set(dn,Math.round((new Date).getTime()/1e3)),a&&t.get(on)&&(e=I.location.hash)){for(e=e.split(/[?&#]+/),a=[],i=0;i<e.length;++i)(c(e[i],"utm_id")||c(e[i],"utm_campaign")||c(e[i],"utm_source")||c(e[i],"utm_medium")||c(e[i],"utm_term")||c(e[i],"utm_content")||c(e[i],"gclid")||c(e[i],"dclid")||c(e[i],"gclsrc"))&&a.push(e[i]);0<a.length&&(e="#"+a.join("&"),t.set(Vt,t.get(Vt)+e))}};pi.prototype.get=function(t){return this.b.get(t)},pi.prototype.set=function(t,e){this.b.set(t,e)};var wi={pageview:[Ht],event:[Jt,Zt,Qt,te],social:[ee,ne,ie],timing:[ge,he,pe,de]};pi.prototype.send=function(t){if(!(1>arguments.length)){if("string"==typeof arguments[0])var e=arguments[0],n=[].slice.call(arguments,1);else e=arguments[0]&&arguments[0][Dt],n=arguments;e&&((n=_(wi[e]||[],n))[Dt]=e,this.b.set(n,void 0,!0),this.filters.D(this.b),this.b.data.m={})}},pi.prototype.ma=function(t,e){var n=this;Ci(t,n,e)||(Ri(t,function(){Ci(t,n,e)}),Ii(String(n.get(qe)),t,void 0,e,!0))};var yi,bi,_i,ki,Oi=function(t){return"prerender"!=I.visibilityState&&(t(),!0)},xi=function(t){if(!Oi(t)){n(16);var e=!1;d(I,"visibilitychange",function n(){if(!e&&Oi(t)){e=!0;var i=n,a=I;a.removeEventListener?a.removeEventListener("visibilitychange",i,!1):a.detachEvent&&a.detachEvent("onvisibilitychange",i)}})}},Ti=/^(?:(\w+)\.)?(?:(\w+):)?(\w+)$/,Si=function(t){if(r(t[0]))this.u=t[0];else{var e=Ti.exec(t[0]);if(null!=e&&4==e.length&&(this.c=e[1]||"t0",this.K=e[2]||"",this.C=e[3],this.a=[].slice.call(t,1),this.K||(this.A="create"==this.C,this.i="require"==this.C,this.g="provide"==this.C,this.ba="remove"==this.C),this.i&&(3<=this.a.length?(this.X=this.a[1],this.W=this.a[2]):this.a[1]&&(s(this.a[1])?this.X=this.a[1]:this.W=this.a[1]))),e=t[1],t=t[2],!this.C)throw"abort";if(this.i&&(!s(e)||""==e))throw"abort";if(this.g&&(!s(e)||""==e||!r(t)))throw"abort";if(ji(this.c)||ji(this.K))throw"abort";if(this.g&&"t0"!=this.c)throw"abort"}};function ji(t){return 0<=t.indexOf(".")||0<=t.indexOf(":")}yi=new k,_i=new k,ki=new k,bi={ec:45,ecommerce:46,linkid:47};var Ci=function(t,e,n){e==Di||e.get(qe);var i=yi.get(t);return!!r(i)&&(e.plugins_=e.plugins_||new k,!!e.plugins_.get(t)||(e.plugins_.set(t,new i(e,n||{})),!0))},Ii=function(t,e,i,a,o){if(!r(yi.get(e))&&!_i.get(e)){if(bi.hasOwnProperty(e)&&n(bi[e]),ei.test(e)){if(n(52),!(t=Di.j(t)))return!0;a={id:e,B:(i=a||{}).dataLayer||"dataLayer",ia:!!t.get("anonymizeIp"),sync:o,G:!1},t.get("&gtm")==e&&(a.G=!0);var s=String(t.get("name"));"t0"!=s&&(a.target=s),N(String(t.get("trackingId")))||(a.clientId=String(t.get(ze)),a.ka=Number(t.get(Be)),i=i.palindrome?ii:ni,i=(i=I.cookie.replace(/^|(; +)/g,";").match(i))?i.sort().join("").substring(1):void 0,a.la=i,a.qa=w(t.b.get(Vt)||"","gclid")),t=a.B,i=(new Date).getTime(),C[t]=C[t]||[],i={"gtm.start":i},o||(i.event="gtm.js"),C[t].push(i),i=function(t){function e(t,e){e&&(n+="&"+t+"="+h(e))}var n="https://www.google-analytics.com/gtm/js?id="+h(t.id);return"dataLayer"!=t.B&&e("l",t.B),e("t",t.target),e("cid",t.clientId),e("cidt",t.ka),e("gac",t.la),e("aip",t.ia),t.sync&&e("m","sync"),e("cycle",t.G),t.qa&&e("gclid",t.qa),ai.test(I.referrer)&&e("cb",String(ht())),n}(a)}!i&&bi.hasOwnProperty(e)?(n(39),i=e+".js"):n(43),i&&(i&&0<=i.indexOf("/")||(i=(At||"https:"==I.location.protocol?"https:":"http:")+"//www.google-analytics.com/plugins/ua/"+i),t=(a=Ni(i)).protocol,i=I.location.protocol,("https:"==t||t==i||"http:"==t&&"http:"==i)&&Ei(a)&&(v(a.url,void 0,o),_i.set(e,!0)))}},Ri=function(t,e){var n=ki.get(t)||[];n.push(e),ki.set(t,n)},Ai=function(t,e){yi.set(t,e),e=ki.get(t)||[];for(var n=0;n<e.length;n++)e[n]();ki.set(t,[])},Ei=function(t){var e=Ni(I.location.href);return!!c(t.url,"https://www.google-analytics.com/gtm/js?id=")||!(t.query||0<=t.url.indexOf("?")||0<=t.path.indexOf("://"))&&(t.host==e.host&&t.port==e.port||(e="http:"==t.protocol?80:443,!("www.google-analytics.com"!=t.host||(t.port||e)!=e||!c(t.path,"/plugins/"))))},Ni=function(t){function e(t){var e=(t.hostname||"").split(":")[0].toLowerCase(),n=(t.protocol||"").toLowerCase();return n=1*t.port||("http:"==n?80:"https:"==n?443:""),t=t.pathname||"",c(t,"/")||(t="/"+t),[e,""+n,t]}var n=I.createElement("a");n.href=I.location.href;var i=(n.protocol||"").toLowerCase(),a=e(n),r=n.search||"",o=i+"//"+a[0]+(a[1]?":"+a[1]:"");return c(t,"//")?t=i+t:c(t,"/")?t=o+t:!t||c(t,"?")?t=o+a[2]+(t||r):0>t.split("/")[0].indexOf(":")&&(t=o+a[2].substring(0,a[2].lastIndexOf("/"))+"/"+t),n.href=t,i=e(n),{protocol:(n.protocol||"").toLowerCase(),host:i[0],port:i[1],path:i[2],query:n.search||"",url:t||""}},$i={ga:function(){$i.f=[]}};$i.ga(),$i.D=function(t){var e=$i.J.apply($i,arguments);for(e=$i.f.concat(e),$i.f=[];0<e.length&&!$i.v(e[0])&&(e.shift(),!(0<$i.f.length)););$i.f=$i.f.concat(e)},$i.J=function(t){for(var e=[],n=0;n<arguments.length;n++)try{var i=new Si(arguments[n]);i.g?Ai(i.a[0],i.a[1]):(i.i&&(i.ha=Ii(i.c,i.a[0],i.X,i.W)),e.push(i))}catch(a){}return e},$i.v=function(t){try{if(t.u)t.u.call(C,Di.j("t0"));else{var e=t.c==Ct?Di:Di.j(t.c);if(t.A){if("t0"==t.c&&null===(e=Di.create.apply(Di,t.a)))return!0}else if(t.ba)Di.remove(t.c);else if(e)if(t.i){if(t.ha&&(t.ha=Ii(t.c,t.a[0],t.X,t.W)),!Ci(t.a[0],e,t.W))return!0}else if(t.K){var n=t.C,i=t.a,a=e.plugins_.get(t.K);a[n].apply(a,i)}else e[t.C].apply(e,t.a)}}catch(r){}};var Di=function(t){n(1),$i.D.apply($i,[arguments])};Di.h={},Di.P=[],Di.L=0,Di.answer=42;var Mi=[We,Je,qe];Di.create=function(t){var e=_(Mi,[].slice.call(arguments));e[qe]||(e[qe]="t0");var i=""+e[qe];if(Di.h[i])return Di.h[i];t:{if(e[gn]){if(n(67),e[an]&&"cookie"!=e[an]){var a=!1;break t}if(void 0!==j)e[ze]||(e[ze]=j);else{e:{a=String(e[Je]||y());var r=String(e[Ze]||"/"),o=$(String(e[Ye]||"_ga"));if(!(a=Dn(o,a,r))||It.test(a))a=!0;else if(0==(a=$("AMP_TOKEN")).length)a=!0;else{if(1==a.length&&("$RETRIEVING"==(a=decodeURIComponent(a[0]))||"$OPT_OUT"==a||"$ERROR"==a||"$NOT_FOUND"==a)){a=!0;break e}a=!1}}if(a&&function t(e,i){var a=$("AMP_TOKEN");return 1<a.length?(n(55),!1):"$OPT_OUT"==(a=decodeURIComponent(a[0]||""))||"$ERROR"==a||N(i)?(n(62),!1):V.test(I.referrer)||"$NOT_FOUND"!=a?void 0!==j?(n(56),R(function(){e(j)},0),!0):x?(F.push(e),!0):"$RETRIEVING"==a?(n(57),R(function(){t(e,i)},1e4),!0):(x=!0,a&&"$"!=a[0]||(B("$RETRIEVING",3e4),setTimeout(z,3e4),a=""),!!q(a,i)&&(F.push(e),!0)):(n(68),!1)}(H,String(e[We]))){a=!0;break t}}}a=!1}return a?null:(e=new pi(e),Di.h[i]=e,Di.P.push(e),e)},Di.remove=function(t){for(var e=0;e<Di.P.length;e++)if(Di.P[e].get(qe)==t){Di.P.splice(e,1),Di.h[t]=null;break}},Di.j=function(t){return Di.h[t]},Di.getAll=function(){return Di.P.slice(0)},Di.N=function(){"ga"!=Ct&&n(49);var t=C[Ct];if(!t||42!=t.answer){Di.L=t&&t.l,Di.loaded=!0;var e=C[Ct]=Di;if(kn("create",e,e.create),kn("remove",e,e.remove),kn("getByName",e,e.j,5),kn("getAll",e,e.getAll,6),kn("get",e=pi.prototype,e.get,7),kn("set",e,e.set,4),kn("send",e,e.send),kn("requireSync",e,e.ma),kn("get",e=vt.prototype,e.get),kn("set",e,e.set),"https:"!=I.location.protocol&&!At){t:{e=I.getElementsByTagName("script");for(var i=0;i<e.length&&100>i;i++){var a=e[i].src;if(a&&0==a.indexOf("https://www.google-analytics.com/analytics")){e=!0;break t}}e=!1}e&&(At=!0)}(C.gaplugins=C.gaplugins||{}).Linker=Wn,e=Wn.prototype,Ai("linker",Wn),kn("decorate",e,e.ca,20),kn("autoLink",e,e.S,25),Ai("displayfeatures",gi),Ai("adfeatures",gi),t=t&&t.q,o(t)?$i.D.apply(Di,t):n(50)}},Di.da=function(){for(var t=Di.getAll(),e=0;e<t.length;e++)t[e].get(qe)};var Pi=Di.N,Li=C[Ct];function Ui(t){var e,n=1;if(t)for(n=0,e=t.length-1;0<=e;e--){var i=t.charCodeAt(e);n=0!=(i=266338304&(n=(n<<6&268435455)+i+(i<<14)))?n^i>>21:n}return n}Li&&Li.r?Pi():xi(Pi),xi(function(){$i.D(["provide","render",g])})}(window);
},{}],"tQzs":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0,require("../lib/analytics.js");var e=t(require("./log"));function t(e){return e&&e.__esModule?e:{default:e}}function r(e,t){return l(e)||i(e,t)||o(e,t)||n()}function n(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(e,t){if(e){if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):void 0}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,a=void 0;try{for(var i,l=e[Symbol.iterator]();!(n=(i=l.next()).done)&&(r.push(i.value),!t||r.length!==t);n=!0);}catch(c){o=!0,a=c}finally{try{n||null==l.return||l.return()}finally{if(o)throw a}}return r}}function l(e){if(Array.isArray(e))return e}const c="UA-122302708-1",u="__fpgadelayed";function s(){return window.ga.apply(window.ga,arguments)}const d=Object.assign(s,{pageview:(e,t)=>{const r=e||window.location.pathname;t=t||window.location.href,d("send",{hitType:"pageview",page:r,location:location})},event:(e,t,r,n,o)=>{d("send","event",e,t,r,n,o)},delayedEvent:(t,r,n,o,a)=>{const i=JSON.stringify({category:t,action:r,label:n,value:o,fieldsObject:a});try{window.localStorage.setItem(u,i)}catch(l){e.default.error(l)}},_runDelayedEvent:()=>{let t;try{(t=window.localStorage.getItem(u))&&window.localStorage.removeItem(u)}catch(r){e.default.error(r)}t&&d("send","event",t.category,t.action,t.label,t.value,t.fieldsObject)}});var w=d;exports.default=w,d._runDelayedEvent();const f=(e,t)=>{let n=t.filter(e=>!/^[A-Za-z0-9_-]+$/.test(e));if(n.length){let e=new Error(`removeParams bad args: ${n.join(", ")}`);throw e.name="InvalidKeysError",e}let o=r(e.split("?"),3),a=o[0],i=o[1],l=o[2];if(i){var c=r(i.split("#"),2);i=c[0],l=c[1],t.forEach(e=>{let t=new RegExp(`(^|&)${e}(=[^&]+)?`);(i=i.replace(t,"")).startsWith("&")&&(i=i.substring(1))}),e=a,i&&(e+=`?${i}`),l&&(e+=`#${l}`)}return e};window.GoogleAnalyticsObject="ga",window.ga||(window.ga=function(){window.ga.q=window.ga.q||[],window.ga.q.push(arguments)}),window.ga.l=1*new Date,d("create",c,"auto"),d("set","checkProtocolTask",null);
},{"../lib/analytics.js":"n94p","./log":"DrS6"}],"npls":[function(require,module,exports) {
"use strict";require("core-js/modules/es7.array.flat-map"),require("core-js/modules/es6.array.from"),require("core-js/modules/es6.array.sort"),require("core-js/modules/es6.array.species"),require("core-js/modules/es6.function.has-instance"),require("core-js/modules/es6.map"),require("core-js/modules/es7.object.define-getter"),require("core-js/modules/es7.object.define-setter"),require("core-js/modules/es7.object.entries"),require("core-js/modules/es7.object.get-own-property-descriptors"),require("core-js/modules/es7.object.lookup-getter"),require("core-js/modules/es7.object.lookup-setter"),require("core-js/modules/es6.object.to-string"),require("core-js/modules/es7.object.values"),require("core-js/modules/es6.promise"),require("core-js/modules/es7.promise.finally"),require("core-js/modules/es6.regexp.constructor"),require("core-js/modules/es6.regexp.match"),require("core-js/modules/es6.regexp.replace"),require("core-js/modules/es6.regexp.split"),require("core-js/modules/es6.regexp.search"),require("core-js/modules/es6.regexp.to-string"),require("core-js/modules/es6.set"),require("core-js/modules/es6.symbol"),require("core-js/modules/es7.symbol.async-iterator"),require("core-js/modules/es7.string.pad-start"),require("core-js/modules/es7.string.pad-end"),require("core-js/modules/es7.string.trim-left"),require("core-js/modules/es7.string.trim-right"),require("core-js/modules/es6.typed.array-buffer"),require("core-js/modules/es6.typed.int8-array"),require("core-js/modules/es6.typed.uint8-array"),require("core-js/modules/es6.typed.uint8-clamped-array"),require("core-js/modules/es6.typed.int16-array"),require("core-js/modules/es6.typed.uint16-array"),require("core-js/modules/es6.typed.int32-array"),require("core-js/modules/es6.typed.uint32-array"),require("core-js/modules/es6.typed.float32-array"),require("core-js/modules/es6.typed.float64-array"),require("core-js/modules/es6.weak-map"),require("core-js/modules/es6.weak-set"),require("core-js/modules/web.timers"),require("core-js/modules/web.immediate"),require("core-js/modules/web.dom.iterable"),require("regenerator-runtime/runtime");var e=require("luxon");require("nodelist-foreach-polyfill");var r=j(require("../api")),t=require("../db"),o=require("../e7n-util"),s=b(require("../errors")),n=j(require("../events")),i=b(require("../fixlinks")),a=require("../image-formats"),u=b(require("../invalidurls")),l=b(require("../log")),c=b(require("../stores/options-store")),d=j(require("../chrome.permissions")),m=require("../reportbutton"),f=require("../promise"),p=j(require("../chrome.tabs")),h=require("../util"),w=require("./domwait"),y=b(require("../ga")),g=require("~js/constants");function b(e){return e&&e.__esModule?e:{default:e}}function q(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return q=function(){return e},e}function j(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=q();if(r&&r.has(e))return r.get(e);var t={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var n=o?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(t,s,n):t[s]=e[s]}return t.default=e,r&&r.set(e,t),t}function v(e,r,t,o,s,n,i){try{var a=e[n](i),u=a.value}catch(l){return void t(l)}a.done?r(u):Promise.resolve(u).then(o,s)}function $(e){return function(){var r=this,t=arguments;return new Promise(function(o,s){var n=e.apply(r,t);function i(e){v(n,o,s,i,a,"next",e)}function a(e){v(n,o,s,i,a,"throw",e)}i(void 0)})}}function x(e,r){return O(e)||E(e,r)||k(e,r)||_()}function _(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function k(e,r){if(e){if("string"==typeof e)return R(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?R(e,r):void 0}}function R(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,o=new Array(r);t<r;t++)o[t]=e[t];return o}function E(e,r){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var t=[],o=!0,s=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(o=(i=a.next()).done)&&(t.push(i.value),!r||t.length!==r);o=!0);}catch(u){s=!0,n=u}finally{try{o||null==a.return||a.return()}finally{if(s)throw n}}return t}}function O(e){if(Array.isArray(e))return e}const C="production",S=parseInt("1621564927",10)||0,T="ExpiredBetaError";(0,i.default)(),(0,o.updateHtml)(),window.setTimeout(()=>{y.default.pageview()},0);let P,I=null,A=null;function U(){h.$.on((0,h.$)("invalid-desc-a"),"click",e=>{e.preventDefault(),H()}),h.$.on((0,h.$)("close"),"click",e=>{e.preventDefault(),window.close()}),h.$.findClass("fp-btn",(0,h.$)("frame-perms")).forEach(e=>{let r=e.dataset.action;h.$.on(e,"click",e=>{n.trigger("clicked",{action:r}),h.$.hide("frame-perms"),h.$.show("loading")})}),n.on("needFramePermsClick",e=>{let r=x("iframe"===e.tagName?["frame-perms-iframe","frame-perms-frame"]:["frame-perms-frame","frame-perms-iframe"],2),t=r[0],o=r[1];h.$.hide(o),h.$.show(t,"inline"),h.$.hide("loading"),h.$.show("frame-perms");let s=document.querySelector("#frame-perms .button-primary");s&&s.focus()});let o=`${g.CUR_BROWSER_INFO.browser_protocol}extensions/?id=${chrome.runtime.id}`;h.$.findClass("ext-link",(0,h.$)("file-perms")).forEach(e=>{h.$.on(e,"click",()=>{let e={url:o};P&&!P.incognito&&(e.index=P.index+1),chrome.tabs.create(e)})}),n.on("needFilePermsClick",()=>{h.$.hide("loading"),h.$.show("file-perms");let e=document.querySelector("#file-perms .button-primary");e&&e.focus()}),p.getCurrentTab().then(r=>{if(P=r,"beta"===C&&S){const r=e.DateTime.fromSeconds(S);if(e.DateTime.now().diff(r,"days").days>30){const e=new Error("This build is no longer valid.");throw e.name=T,e}}}).then($(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,p.getZoom(P.id);case 3:A=e.sent,e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),console.error("Error getting zoom",e.t0);case 9:return e.next=11,c.default.load();case 11:if(!(I=e.sent).auto_dl2){e.next=21;break}return e.next=15,d.contains(d.PERMISSIONS.DOWNLOADS);case 15:if(e.sent){e.next=21;break}return I.auto_dl2=!1,I.auto_dl_was_disabled=!0,e.next=21,c.default.remove("auto_dl2");case 21:case"end":return e.stop()}},e,null,[[0,6]])}))).then(()=>B()).then(()=>{I.auto_dl2?(0,w.wait)().then(()=>{h.$.show("auto-dl")}):I.auto_dl_was_disabled&&(0,w.wait)().then(()=>{h.$.show("auto-dl-was-disabled")}),function(e){let r=h.$.findClass("frame_persist-toggle",(0,h.$)("frame-perms"))[0],t=r.getElementsByClassName("frame_persist-input")[0],o=r.getElementsByClassName("frame_persist-text")[0],s=c.default.getField("frame_persist");t.checked=e.frame_persist,o.innerText=s.help,h.$.on(t,"change",function(e){let r=e.target.checked;c.default.set("frame_persist",r).then(()=>{I.frame_persist=r}).catch(t=>{e.target.checked=!r,N(t)})})}(I)}).then(()=>{let e=P.url,r="filesystem:"+chrome.extension.getURL("/persistent/");if(e.startsWith(r)&&[".jpg",".png"].some(r=>e.endsWith(r)))return t.Capture.findSrc(e.split("/").pop())}).then(e=>{if(e)return e;const t=P.url||"about:blank",o=P.title,s=(0,a.getImageFormat)(I.fmt);I.fmt_details=s;const n=(0,h.asFilename)(t,s.ext);return r.captureToFiles(P,n,D,e=>{h.$.show("split-image"),(0,h.$)("screenshot-count").innerText=e,e>10&&window.setTimeout(()=>{h.$.show("split-image-extra")},2e3)},I).then(({files:e,scaleMultiplier:s,metadata:n})=>{if(!e||!e.length){let r=new Error("No files passed to `displayCaptures` "+e);throw r.name="NoFiles",r}return n=n||{},1!==A&&(n.z=A),n.ww&&n.ww===P.width||(n.tw=P.width),n.wh&&n.wh===P.height||(n.th=P.height),r.filesToRecord(t,o,I.fmt,s,e,n)})}).then(e=>(0,f.sleep)(I.auto_dl_was_disabled?2e3:0).then(()=>e)).then(e=>I&&I.auto_dl2?F(e.fullPaths):p.openInTabs(P,[e.displayURL])).catch(e=>N(e))}function F(e){return M.apply(this,arguments)}function M(){return(M=$(regeneratorRuntime.mark(function e(r){var t,o,s,n,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,h.fullServiceDownload)(r,!0);case 2:if(t=e.sent,o=t.hasPerms,s=t.results,n=t.failCount,o){e.next=12;break}throw(i=new Error("Does not have permission.")).name="ChromePermissions",i;case 12:if(!n){e.next=16;break}throw s.find(e=>!1===e.success).error;case 16:return e.next=18,(0,f.sleep)(2e3);case 18:window.close();case 19:case"end":return e.stop()}},e)}))).apply(this,arguments)}function N(e){let t="[_errorHandler] ";if(e){let r=["name","via","message"].map(r=>e[r]?`${r} = ${e[r]}`:null).filter(e=>e).join("\n");r&&(t+="\n"+r+"\n")}l.default.error(t),e&&e.stack&&(0,l.default)(e.stack),(0,w.wait)(()=>h.$.hidden("loading")).then(()=>{h.$.show("close");const t=z(),n=e&&e.name===r.ERROR_TYPES.CHROME_TABS&&("executeScript"===e.via||"captureVisibleTab"===e.via&&u.default.isOwnUrl(t)),i=e&&e.name===T;if(n){let r=u.default.getMatch(t),s=(0,h.$)("invalid-desc-detail");if(!s.dataset.default){const e=(0,o.tr)("The browser does not allow access to some URLs for security reasons. This includes the web store gallery, other extensions, and $browser_protocol$ URLs. The current page appears to be a restricted URL.",null,[g.CUR_BROWSER_INFO.browser_protocol],{browser_protocol:{content:"$1",example:"chrome://"}});s.dataset.default=e}return s.textContent=r?r.explanation:s.dataset.default,L(e).then(()=>{r||H(),h.$.show("invalid")})}if(i){const e=(0,h.$)("expired-beta");e.textContent="This test version has expired. Please reach out to get an update or switch over to the proper extension in the";const r=document.createElement("a");Object.assign(r,{targt:"_blank",rel:"noopener noreferrer",href:g.CUR_BROWSER_INFO.store_url,textContent:g.CUR_BROWSER_INFO.store}),e.textContent.appendChild(r),h.$.show("expired-beta")}else{if(!e||!s.default[e.name])return B(e).then(()=>{h.$.show("uh-oh")});{let r=s.default[e.name];(0,h.$)("generic-error-title").innerText=r.title,(0,h.$)("generic-error-body").innerText=r.body;let t=(0,h.$)("generic-error-buttons");t.innerHTML="",r.buttons&&r.buttons().forEach(e=>{t.appendChild(e)}),h.$.show("generic-error")}}}).then(()=>h.$.hide("loading"))}function D(e){0===e&&h.$.show("loading");let r=Math.floor(100*e,10);(0,h.$)("bar").style.width=r+"%",(0,h.$)("dots").style.width=100-r+2+"%"}function B(e){return W("report-link",e)}function L(e){return W("invalid-link",e)}function W(e,r){return(0,m.getReportUrlAsync)({url:z(),error:r}).then(r=>((0,h.$)(e).setAttribute("href",r),r)).catch(e=>l.default.error(e))}function H(){(0,h.$)("invalid").classList.add("show-desc")}function z(){return P?P.url:window.location.href}switch(window.location.search){case"?test":window._progress=D,D(.18),(0,h.asArray)(document.querySelectorAll("#wrap > div")).forEach(e=>{e.style.display="block"}),[document.documentElement,document.body,document.getElementById("wrap")].forEach(e=>{e.style.overflow="auto"});break;default:window.addEventListener("load",U)}
},{"core-js/modules/es7.array.flat-map":"I8vV","core-js/modules/es6.array.from":"RRcs","core-js/modules/es6.array.sort":"nrVf","core-js/modules/es6.array.species":"smn3","core-js/modules/es6.function.has-instance":"a7bX","core-js/modules/es6.map":"ioKM","core-js/modules/es7.object.define-getter":"guoQ","core-js/modules/es7.object.define-setter":"HMp9","core-js/modules/es7.object.entries":"gxEP","core-js/modules/es7.object.get-own-property-descriptors":"BQD8","core-js/modules/es7.object.lookup-getter":"HB2g","core-js/modules/es7.object.lookup-setter":"QF5J","core-js/modules/es6.object.to-string":"zTK3","core-js/modules/es7.object.values":"Ltmz","core-js/modules/es6.promise":"Pjta","core-js/modules/es7.promise.finally":"l1j0","core-js/modules/es6.regexp.constructor":"BenF","core-js/modules/es6.regexp.match":"RTfC","core-js/modules/es6.regexp.replace":"KGao","core-js/modules/es6.regexp.split":"aOHf","core-js/modules/es6.regexp.search":"zOab","core-js/modules/es6.regexp.to-string":"iflU","core-js/modules/es6.set":"coyu","core-js/modules/es6.symbol":"uVn9","core-js/modules/es7.symbol.async-iterator":"DlMC","core-js/modules/es7.string.pad-start":"SWNE","core-js/modules/es7.string.pad-end":"n20m","core-js/modules/es7.string.trim-left":"ppxd","core-js/modules/es7.string.trim-right":"hxx1","core-js/modules/es6.typed.array-buffer":"NJ0a","core-js/modules/es6.typed.int8-array":"wqMZ","core-js/modules/es6.typed.uint8-array":"QTtY","core-js/modules/es6.typed.uint8-clamped-array":"Kqgs","core-js/modules/es6.typed.int16-array":"fEGw","core-js/modules/es6.typed.uint16-array":"xyd6","core-js/modules/es6.typed.int32-array":"hIko","core-js/modules/es6.typed.uint32-array":"tNPN","core-js/modules/es6.typed.float32-array":"wisA","core-js/modules/es6.typed.float64-array":"mbTX","core-js/modules/es6.weak-map":"D6DP","core-js/modules/es6.weak-set":"bRUR","core-js/modules/web.timers":"OTsy","core-js/modules/web.immediate":"hZLH","core-js/modules/web.dom.iterable":"v6Aj","regenerator-runtime/runtime":"QVnC","luxon":"eqBM","nodelist-foreach-polyfill":"nidE","../api":"cZF8","../db":"f9V3","../e7n-util":"f0tH","../errors":"CyAK","../events":"GZY7","../fixlinks":"P6tM","../image-formats":"UTVA","../invalidurls":"lIDf","../log":"DrS6","../stores/options-store":"m7pw","../chrome.permissions":"WlKW","../reportbutton":"eLqP","../promise":"pPWs","../chrome.tabs":"aDCn","../util":"wT1R","./domwait":"BoDy","../ga":"tQzs","~js/constants":"qwST"}]},{},["npls"], null)
//# sourceMappingURL=popup.fa1ad58d.js.map