export default {
  mounted(el: HTMLElement) {
    let startX: number, startY: number, initialOffsetX: number, initialOffsetY: number;
    let isDragging = false;

    const mouseDownHandler = (e: MouseEvent) => {
      startX = e.clientX;
      startY = e.clientY;
      initialOffsetX = el.offsetLeft;
      initialOffsetY = el.offsetTop;
      isDragging = false;

      document.addEventListener('mousemove', mouseMoveHandler);
      document.addEventListener('mouseup', mouseUpHandler);
    };

    const mouseMoveHandler = (e: MouseEvent) => {
      const dx = e.clientX - startX;
      const dy = e.clientY - startY;

      if (dx !== 0 || dy !== 0) {
        isDragging = true;
      }

      let newLeft = initialOffsetX + dx;
      let newTop = initialOffsetY + dy;

      const maxLeft = window.innerWidth - el.offsetWidth;
      const maxTop = window.innerHeight - el.offsetHeight;

      if (newLeft < 0) newLeft = 0;
      if (newTop < 0) newTop = 0;
      if (newLeft > maxLeft) newLeft = maxLeft;
      if (newTop > maxTop) newTop = maxTop;

      el.style.left = `${newLeft}px`;
      el.style.top = `${newTop}px`;
    };

    const mouseUpHandler = (e:MouseEvent) => {
      document.removeEventListener('mousemove', mouseMoveHandler);
      document.removeEventListener('mouseup', mouseUpHandler);
    };

    el.addEventListener('mousedown', mouseDownHandler);
  }
}