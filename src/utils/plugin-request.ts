
interface RequestConfig {
  data?: { [key: string]: any } | string,
  params?: { [key: string]: any }
  headers?: { [key: string]: any }
}

function sendMessage(message: any) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, (response) => {
      if (response) {
        resolve(response)
      } else {
        reject(chrome.runtime.lastError)
      }
    })
  })
}

async function sendRequest(method: 'post' | 'get', url: string, config: any): Promise<any> {
  const headerConfig = Object.assign({}, config.headers, {"Content-Type": "application/json"})
  const message = {
    callName: 'browse_plugin:request',
    data: {
      method: method,
      url: url,
      data: JSON.stringify(config.data),
      params: config.params,
      headers: headerConfig
    }
  }
  const result:any = await sendMessage(message)

  if (!result) {
    throw new Error('sendRequest error')
  } else if (result.status !== 'success') {
    const error = new Error(result.data.message)
    error.stack = result.data.stack
    throw error
  } else return result.data.data
}

async function get(url: string, config?: RequestConfig) {
  return await sendRequest('get', url, config || {})
}

async function post(url: string, config: RequestConfig) {
  return await sendRequest('post', url, config || {})
}

export default {
  get,
  post
}