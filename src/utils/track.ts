import request from "./plugin-request"
import config from '@/config'
import { v4 } from 'uuid'
import manifest from '@/manifest.json'
import { TrackOpTypes } from "vue"

const API_URL = `${config.api}/api/common/log`
const CLIENT_ID = 'clientId'

// // 这里是为了让Error对象也可以被json序列化
// if (!('toJSON' in Error.prototype)) {
//   Object.defineProperty(Error.prototype, 'toJSON', {
//     value: function () {
//       let alt: { [key: string]: any } = {}
//       Object.getOwnPropertyNames(this).forEach(function (key) {
//         alt[key] = this[key]
//       }, this)
//       return alt
//     },
//     configurable: true,
//     writable: true
//   })
// }
  

type TrackEvent = {
  clientTime: number
  app: string
  version: string
  // 这个用于客户端的唯一标
  uuid: string
  os: string
  osVersion: string
  // 网络类型，这个留
  networkType: string
  // 事件id，这个自己定
  eventId: string
  // 事件类
  eventType: string
  // 事件内容，必须以JSO
  eventContent: string
}

const EVENT_BASE = {
  clientTime: 0,
  app: manifest.name,
  version: manifest.version,
  os: '',
  osVersion: '',
  networkType: '',
}

function asyncGetCache(key: string) {
  return new Promise((resolve, reject) => {
    chrome.storage.sync.get(key, function (result) {
      resolve(result[key])
    })
  })
}

function asyncSetCache(key: string, value: any) {
  return new Promise((resolve, reject) => {
    chrome.storage.sync.set({ [key]: value }, function () {
      console.log('set cache', key, value)
      resolve(null)
    })
  })
}

export class EventTracker {
  private static _instance: EventTracker
  static createInstance({ uuid }: { uuid: string }) {
    this._instance = new this({ uuid })
  }

  static get instance() {
    return this._instance
  }

  private _uuid: string
  constructor({ uuid }: { uuid: string }) {
    this._uuid = uuid
  }

  private tack(event: TrackEvent) {
    request.post(API_URL, { data: [event] })
  }

  event(eventId: string, data: any) {
    const trackData = Object.assign({}, data, {url: location.href})
    const event: TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: eventId,
      eventType: 'page_event',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }

  exception(eventId: string, data: any) {
    data['url'] = location.href
    const trackData = Object.assign({}, data, {
      url: location.href,
      message: data.message,
    })
    const event: TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: eventId,
      eventType: 'page_exception',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }

  click(eventId: string, data: any) {
    const trackData = Object.assign({}, data, {url: location.href})
    const event: TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: eventId,
      eventType: 'page_click',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }
}

export async function useTracker() {
  // 请注意，instance需要初始化。否则会为空
  let clientId = await asyncGetCache(CLIENT_ID) as string
  if (!clientId) {
    clientId = v4()
    await asyncSetCache(CLIENT_ID, clientId)
  }
  EventTracker.createInstance({ uuid: clientId })
  return EventTracker.instance
}