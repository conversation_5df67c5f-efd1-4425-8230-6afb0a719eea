class MaimaiInjector {

  private _onShowResume: Function | null = null
  private _onCloseResume: Function | null = null

  private checkResumeOpenTimer: NodeJS.Timeout | null = null
  private checkResumeCloseTimer: NodeJS.Timeout | null = null


  init() {
    clearInterval(this.checkResumeOpenTimer!)
    clearInterval(this.checkResumeCloseTimer!)
    this.checkResumeOpen()
    window.addEventListener('popstate', () => {
      this._onCloseResume && this._onCloseResume()
      clearInterval(this.checkResumeCloseTimer!)
      this.checkResumeCloseTimer = null
      console.log('人才详情弹窗关闭...')
      this.checkResumeOpen()
    })
  }

  private checkResumeOpen() {
    this.checkResumeOpenTimer = setInterval(() => {
      const resume = document.querySelector('.left___1IRRn')
      if (resume) {
        this._onShowResume && this._onShowResume(resume)
        clearInterval(this.checkResumeOpenTimer!)
        this.checkResumeOpenTimer = null
        console.log('简历详情弹窗打开...')
        this.checkResumeClose()
      }
    })
  }

  private checkResumeClose() {
    this.checkResumeCloseTimer = setInterval(() => {
      const resume = document.querySelector('.left___1IRRn')
      if (!resume) {
        this._onCloseResume && this._onCloseResume()
        clearInterval(this.checkResumeCloseTimer!)
        this.checkResumeCloseTimer = null
        console.log('简历详情关闭...')
        this.checkResumeOpen()
      }
    })
  }

  onResumeShow(cb: Function) {
    this._onShowResume = cb
  }

  onResumeClose(cb: Function) {
    this._onCloseResume = cb
  }
}

export default MaimaiInjector
