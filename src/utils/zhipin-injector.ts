


/**
 * 从插件cache中获取参数，如果没有参数，则不执行
 * 从插件cache中获取参数，然后通过hidden input注入到页面中。
 * 然后再在页面中注入执行脚本。
 * 执行脚本将会从hidden input中获取参数，并通过调用zhipin在window下的iBossRoot对象来执行简历显示。
 */

class ZhipinInjector {

  private _onShowResume: (resume:Element) => void = () => { }
  private _onCloseResume: () => void = () => { }

  private checkResumeOpenTimer: NodeJS.Timeout | null = null
  private checkResumeCloseTimer: NodeJS.Timeout | null = null

  private getHiddenInput(id: string, value: string) {
    const hiddenInput = document.createElement('input')
    hiddenInput.id = id
    hiddenInput.value = value
    hiddenInput.type = 'hidden'
    return hiddenInput
  }

  private injectScript(src: string) {
    const scriptEle = document.createElement('script')
    scriptEle.setAttribute('type', 'text/javascript')
    scriptEle.setAttribute('src', src)
    document.body.appendChild(scriptEle)
  }

  constructor() {
    this.init()
  }

  init() {
    clearInterval(this.checkResumeOpenTimer!)
    clearInterval(this.checkResumeCloseTimer!)
    this.checkResumeOpen()
    window.addEventListener('popstate', () => {
      this._onCloseResume()
      clearInterval(this.checkResumeCloseTimer!)
      this.checkResumeOpen()
    })
  }

  private checkResumeOpen() {
    if (this.checkResumeCloseTimer) return

    this.checkResumeOpenTimer = setInterval(() => {

      const page = location.pathname

      // 推荐--人才详情弹窗
      if (page == '/web/chat/recommend') {
        const recommendFrame = document.getElementsByName('recommendFrame') as NodeListOf<HTMLIFrameElement>
        if (!recommendFrame.length) return

        const frameDocument = recommendFrame[0].contentDocument
        const resume = frameDocument?.getElementsByClassName('resume-dialog')[0] as HTMLDivElement
        if (!resume) return

        resume.addEventListener('click', (e: Event) => {
          const target = e.target as HTMLDivElement
          const className = target.className
          if ( className.includes('resume-turn-btn-bg') ||
            className.includes('resume-next') || className.includes('resume-pre')
          ) { setTimeout(() => this._onShowResume(resume), 200) }
        })

        clearInterval(this.checkResumeOpenTimer!)
        this.checkResumeClose()
        this._onShowResume(resume)
      }

      // 互动--人才详情弹窗
      if (page == '/web/chat/interaction') {
        const interactionFrame = document.getElementsByName('interactionFrame') as NodeListOf<HTMLIFrameElement>
        if (!interactionFrame.length) return

        const frameDocument = interactionFrame[0].contentDocument
        const resume = frameDocument?.getElementsByClassName('resume-dialog')[0] as HTMLDivElement
        if (!resume) return

        resume.addEventListener('click', (e: Event) => {
          const target = e.target as HTMLDivElement
          const className = target.className
          if (
            className.includes('resume-turn-btn-bg') ||
            className.includes('resume-next') || className.includes('resume-pre')
          ) {
            setTimeout(() => this._onShowResume(resume) , 200)
          }
        })
        clearInterval(this.checkResumeOpenTimer!)
        this.checkResumeClose()
        this._onShowResume(resume)

      }

      // 搜索--人才详情弹窗
      if (page == '/web/chat/search') {
        const resume = document.querySelector('.anonymous-resume-wrapper')
        if (!resume) return
        
        resume.parentElement!.addEventListener('click', (e: Event) => {
          const target = e.target as HTMLDivElement
          const className = target.className
          if (className.includes('page-turner')) {
            setTimeout(() => {
              const resume = document.querySelector('.anonymous-resume-wrapper')
              if (resume) this._onShowResume(resume)
            }, 200)
          }
        })
        clearInterval(this.checkResumeOpenTimer!)
        this.checkResumeClose()
        this._onShowResume(resume)
      }

      // 聊天--人才详情弹窗
      if (page == '/web/chat/index') {
        const resume = document.querySelector('.new-resume-online-main-ui')
        if (!resume) return
        this._onShowResume(resume)
        clearInterval(this.checkResumeOpenTimer!)
        this.checkResumeClose()
      }

      // 意向--人才详情弹窗
      if (page == '/web/chat/intention') {
        const intentionFrame = document.querySelector('iframe[src="/web/frame/senior-search/intention"]') as HTMLIFrameElement
        if (!intentionFrame) return

        const frameDocument = intentionFrame.contentDocument
        if (!frameDocument) return

        const resume = frameDocument.querySelector('.geek-detail-container')
        if (!resume) return

        resume.addEventListener('click', (e: Event) => {
          const target = e.target as HTMLDivElement
          const className = target.className
          if (
            className.includes('resume-next') || className.includes('resume-pre') ||
            className.includes('iboss-right') || className.includes('iboss-left')
          ) { setTimeout(() => this._onShowResume(resume), 200) }
        })
        clearInterval(this.checkResumeOpenTimer!)
        this.checkResumeClose()
        this._onShowResume(resume)
      }
    }, 1000)
  }

  private checkResumeClose() {
    if (this.checkResumeCloseTimer) return
    this.checkResumeCloseTimer = setInterval(() => {

      if (location.href.indexOf('chat/recommend') !== -1) {
        const recommendFrame = document.getElementsByName('recommendFrame') as NodeListOf<HTMLIFrameElement>
        if (recommendFrame.length) {
          const frameDocument = recommendFrame[0].contentDocument
          const resume = frameDocument?.getElementsByClassName('resume-dialog')[0] as HTMLDivElement
          if (!resume) {
            this._onCloseResume()
            clearInterval(this.checkResumeCloseTimer!)
            this.checkResumeCloseTimer = null
            this.checkResumeOpen()
            return
          }
        }
      }

      if (location.href.indexOf('chat/interaction') !== -1) {
        const interactionFrame = document.getElementsByName('interactionFrame') as NodeListOf<HTMLIFrameElement>
        if (interactionFrame.length) {
          const frameDocument = interactionFrame[0].contentDocument
          const resume = frameDocument?.getElementsByClassName('resume-dialog')[0] as HTMLDivElement
          if (!resume) {
            this._onCloseResume()
            clearInterval(this.checkResumeCloseTimer!)
            this.checkResumeCloseTimer = null
            this.checkResumeOpen()
            return
          }
        }
      }

      if (location.href.indexOf('chat/search') !== -1) {
        const searchResumes = document.getElementsByClassName('anonymous-resume-wrapper')
        if (!searchResumes.length) {
          this._onCloseResume()
          clearInterval(this.checkResumeCloseTimer!)
          this.checkResumeCloseTimer = null
          this.checkResumeOpen()
          return
        }
      }

      if (location.href.indexOf('chat/index') !== -1) {
        const chatResumes = document.getElementsByClassName('new-resume-online-main-ui')
        if (!chatResumes.length) {
          this._onCloseResume()
          clearInterval(this.checkResumeCloseTimer!)
          this.checkResumeCloseTimer = null
          this.checkResumeOpen()
          return
        }
      }

      if (location.href.indexOf('chat/intention') !== -1) {
        const intentionFrame = document.querySelector('iframe[src="/web/frame/senior-search/intention"]') as HTMLIFrameElement

        if (intentionFrame) {
          const frameDocument = intentionFrame.contentDocument
          const resumes = frameDocument!.getElementsByClassName('geek-detail-container')
          if (!resumes.length) {
            this._onCloseResume()
            clearInterval(this.checkResumeCloseTimer!)
            this.checkResumeCloseTimer = null
            this.checkResumeOpen()
            return
          }
        }
      }
    }, 1000)
  }

  showResume() {
    let keys = ["currentLid", "currentExpectId", "currentSecurityId"];
    chrome.runtime.sendMessage({
      callName: "browse_plugin:getLocalCache",
      data: keys
    }, (res) => {
      const bossLid = res.data['currentLid'] ? res.data['currentLid'] : ''
      const bossExpectId = res.data['currentExpectId'] ? res.data['currentExpectId'] : ''
      const bossSecurityId = res.data['currentSecurityId'] ? res.data['currentSecurityId'] : ''

      const inputLid = this.getHiddenInput('inject-input-lid', bossLid)
      const inputExpectId = this.getHiddenInput('inject-input-expectId', bossExpectId)
      const inputSecurityId = this.getHiddenInput('inject-input-securityId', bossSecurityId)

      document.body.append(inputLid)
      document.body.append(inputExpectId)
      document.body.append(inputSecurityId)

      this.injectScript(chrome.runtime.getURL('inject.js'))

      chrome.runtime.sendMessage({
        callName: "browse_plugin:clearLocalCache",
        data: null
      }, (res) => { })
    })
  }

  onResumeShow(cb: (resume: Element)=>void) {
    this._onShowResume = cb
  }

  onResumeClose(cb: ()=>void) {
    this._onCloseResume = cb
  }
}

export default ZhipinInjector