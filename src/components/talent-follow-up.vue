<template lang="pug">
.follow-ups
  .follow-up-list(v-if="pagination.total")
    .follow-up-item(v-for="(item, index) in followups")
      .comment {{ item.comment }}
      .info {{ item.assigneeName }} · {{ formateDate(item.createTime) }}
      Divider(style="margin: 12px 0")
  .empty(v-else)
    Empty
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef, watch } from 'vue'
import { Empty, Divider } from 'ant-design-vue'
import request from '@/utils/plugin-request'
import config from '@/config'
import dayjs from 'dayjs'
import { useUserStore } from '@/content/user-store'

const props = defineProps<{ talentId: number }>()
const talentId = toRef(props, 'talentId')
const followups = ref<any[]>([])
const pagination = reactive({
  current: 0,
  total: 0,
  pageSize: 20
})
const userStore = useUserStore()

const emit = defineEmits(['count'])

async function getFollowUps(talentId:number) {
  const res = await request.get(
    config.api + `/api/company/${userStore.companyId}/talent/${talentId}/followups`,
    { params: pagination }
  )
  pagination.total = res.data.total
  followups.value = res.data.followups
  emit('count', pagination.total)
}

function formateDate(timestamp:number) {
  return dayjs(timestamp).format('YYYY-MM-DD')
}

defineExpose({
  getFollowUps
})

onMounted(() => {
  getFollowUps(talentId.value)
})

</script>

<style lang="sass" scoped>
.follow-ups
  .follow-up-item
    .comment
      margin-bottom: 8px
    .info
      color: #999
</style>