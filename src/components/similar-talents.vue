<template lang="pug">
mixin similar-talent-item
  .similar-talent(@click="() => handleTalentClick(talent.talentId)")
    .info
      .avatar
        Avatar(:size="36") {{ talent.name }}
      .info-detail
        .name {{ talent.name }} <em>{{ getGender(talent.gender) }} · {{ talent.age }}岁 · {{ degreeDictMap.get(talent.degree) }}</em>
        .position {{ talent.currentPosition }} · {{ talent.currentCompany }}

        .reason
          template(v-for="item in talent.recReason")
            Tag() {{ item }}

.similar-talent-list
  template(v-if="!talents || talents.length == 0")
    Empty(title="暂无相似人才")
  template(v-for="talent in talents")
    +similar-talent-item
  
</template>

<script lang="ts" setup>
import { onMounted, ref, toRef } from 'vue'
import { Avatar, Tag, Divider, Empty } from 'ant-design-vue'
import config from '@/config'
import { EventTracker, useTracker } from '@/utils/track'
import { degreeDictMap } from '@/utils/degree-dict'

const tracker = ref<EventTracker>()
const props = defineProps<{ talents: any[], candidateId: number, talentId: number }>()
const talents = toRef(props, 'talents')
const talentId = toRef(props, 'talentId')
const candidateId = toRef(props, 'candidateId')

function getGender(gender: number) {
  switch (gender) {
    case 1: return '男'
    case 2: return '女'
    default: return '性别未知'
  }
}

function handleTalentClick(similarTalentId: number) {
  tracker.value?.click('similar_talent_click', {
    similar_talent_id: similarTalentId,
    talent_id: talentId.value,
    candidate_id: candidateId.value
  })
  window.open(`${config.itp}/talent/${similarTalentId}/detail`)
}

onMounted(async () => {
  tracker.value = await useTracker()
})

</script>

<style lang="sass" scoped>
.similar-talent-list

  .similar-talent
    border-bottom: 1px solid #f0f0f0
    padding: 12px 0
    cursor: pointer
  .info
    display: flex
    align-items: flex-start
    .avatar
      flex: 0 0 44px
    .info-detail
      overflow: hidden
      .name
        font-size: 14px
        font-weight: bold

        em
          color: #999
          font-size: 12px
          font-style: normal
          font-weight: normal
          margin-left: 12px
      .position
        color: #999
        white-space: nowrap
        margin-bottom: 4px
        font-size: 12px

</style>