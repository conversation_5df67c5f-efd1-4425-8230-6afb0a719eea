<template lang="pug">
.job-search
  .search-input
    InputSearch(placeholder="请输入项目关键字" enter-button v-model:value="keywords" @search="handleJobSearch")
  .search-list
    template(v-for="item in jobList")
      .job-item
        .job-priority
          JobPriority(:priority="item.priority")
        .job-info
          .job-name {{ item.processName }}
          .customer-name {{ item.customerName }}

      .job-team
        Space(:size="[12, 0]" wrap)
          .team 
            span.team-name BD: 
            span.team-member {{ getUsers(item, 'bd')  }}
          .team 
            span.team-name PM:
            span.team-member {{ getUsers(item, 'pm')  }}
          .team             
            span.team-name CA:
            span.team-member {{ getUsers(item, 'ca')  }}

      Row.job-item-action(:gutter="[12, 12]")
        Col(:span="12")
          But<PERSON>(type="primary" block size="small" :style="{fontSize: 12}"  @click="() => handlAddTalentToJob(item)") 加入项目
        Col(:span="12")
          But<PERSON>(type="primary" ghost block size="small" :style="{fontSize: 12}" @click="() => handleShowJobDetail(item)") 查看详情

      Divider(style="margin: 12px 0")
</template>

<script lang="ts" setup>
import config from '@/config';
import request from '@/utils/plugin-request';
import { InputSearch, message, Button, Divider, Space, Row, Col, Modal } from 'ant-design-vue'
import JobPriority from '@/components/job-priority.vue'
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/content/user-store'

const emit = defineEmits(['select'])
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const keywords = ref('')
const jobList = ref([])

function getUsers(job:any, key:string) {
  const users = job.properties.filter((item:any) => item.key === key).map((item:any) => item.valueName)
  return users.join(', ')
}

async function handleJobSearch() {
  status.loading = true
  try {
    const res = await request.post(`${config.api}/api/company/${userStore.companyId}/pipeline/job/require/search`, {
      data: { name: keywords.value, size: 10, status: 1 }
    })
    console.log(res)
    jobList.value = res.data.jobRequirements
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handlAddTalentToJob(job:any) {
  emit('select', job)
}

async function handleShowJobDetail(job:any) {
  window.open(`${config.itp}/job/${job.id}/detail`)
}

</script>

<style lang="sass" scoped>
.job-search
  padding: 12px 12px

  .search-input
    margin-bottom: 24px

  .job-team
    color: #999
    margin-bottom: 8px
    .team
      font-size: 12px
      .team-name
        color: #999
      .team-member
        color: #333

.job-item
  color: #999
  display: flex
  font-size: 12px
  align-items: center

  .job-priority
    flex: 0 0 48px
  .job-name
    font-weight: bold
    font-size: 14px
    color: #333


</style>