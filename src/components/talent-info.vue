<template lang="pug">
.talent-search-item(v-if="talent")
  .talent-head
    Avatar(:size="36" shape="round" :src="talent.photoUrl") {{ talent.realName ? talent.realName[0] : '' }}
  .talent-info 
    .talent-basic-name 
      span.name 
        .name-text {{ talent.realName }}
        Tag(v-if="!talent.id" color="orange") 未入库
        Tag(v-else color="blue") 已入库
    .talent-basic-other
      span(v-if="talent.gender") {{ talent.gender === 1 ? '男' : '女' }}
      span(v-else) 性别未知
      Divider(type="vertical")
      span(v-if="talent.age") {{ talent.age }}岁
      span(v-else) {{ getFromTimeBirthday(talent.birthday) }}
      Divider(type="vertical")
      span {{ getFromTimeBeginWorkDate(talent.beginWorkDate) }}

    .talent-mobile
      span(v-if="talent.mobileNumber")
        MobileOutlined(style="margin-right: 4px; color: #ccc;")
      span {{ talent.mobileNumber }}
    .talent-email
      span(v-if="talent.email")
        MailOutlined(style="margin-right: 6px; color: #ccc;")
      span {{ talent.email }}

</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import {Avatar, Divider, Tag} from 'ant-design-vue'
import { computed, onMounted } from 'vue'
import { MailOutlined, MobileOutlined } from '@ant-design/icons-vue'
import { toRef } from 'vue'

const props = defineProps<{talent: any}>()
const talent = toRef(props, 'talent')
const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''
  return dayjs(val).format('YYYY.MM')
}

function getFromTimeBeginWorkDate(strDate: string) {
  if (!strDate || strDate === "0") {
    return "未知工作年限";
  } else {
    const year = dayjs(strDate).year()
    const now = dayjs()
    const diff = now.diff(strDate, 'year')
    return `${diff}年工作`
  }
}

function getFromTimeBirthday(strDate: string) {
  if (!strDate || strDate === "0") {
    return "年龄未知"
  } else {
    const year = dayjs(strDate).year()
    const now = dayjs()
    const diff = now.diff(strDate, 'year')
    return `${diff}岁`
  }
}

</script>

<style lang="sass" scoped>
.talent-search-item
  width: 100%
  color: #444
  cursor: pointer
  position: relative
  padding: 0 12px
  margin: 12px 0
  padding-left: 64px
  .talent-head
    position: absolute
    top: 0
    left: 12px

  .talent-info
    line-height: 24px
    font-size: 13px

    .talent-basic-other
      color: #999
    .talent-basic-name
      .name
        font-weight: 700
        font-size: 16px
        display: flex
        align-items: center

        .name-text
          margin-right: 8px
      .status
        font-weight: 400
        font-size: 13px
        color: #999

    .talent-mobile, .talent-email
      width: 100%
      overflow: hidden
      text-overflow: ellipsis
      white-space: nowrap

    .talent-intro
      color: #999
      width: 100%
      display: -webkit-box
      -webkit-line-clamp: 3
      -webkit-box-orient: vertical
      text-overflow: ellipsis
      overflow: hidden
</style>