<template lang="pug">
Tag(v-if="priority === 10" color="#F9470D") P0
Tag(v-else-if="priority === 5" color="#03E3B0") P1
Tag(v-else-if="priority === 1") P2
</template>

<script lang="ts" setup>
import { computed, toRef } from 'vue'
import { Tag } from 'ant-design-vue'

const props = defineProps<{ priority: number }>()
const priority = toRef(props, 'priority')

</script>

<style lang="sass" scoped>
</style>