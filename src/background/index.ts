//获取cookies某一项
async function getCookies(domain: string, name: string) {
  const cookie = await chrome.cookies.get({ url: domain, name })
  return cookie
}

//获取cookies所有值
function getAllCookies(domain: string) {
  const allCookies = chrome.cookies.getAll({ domain })
  return allCookies
}

// 增加rules
async function addRequestRules(rules: chrome.declarativeNetRequest.Rule[]) {
  const oldRules = await chrome.declarativeNetRequest.getDynamicRules()

  const result = await chrome.declarativeNetRequest.updateDynamicRules({
    addRules: rules,
    removeRuleIds: oldRules.map(rule => rule.id),
  })
  return result
}

type ReqeustPrarms = {
  url: string,
  method: string,
  headers: any,
  data: any,
  params: any,
}

function fetchJson(request: ReqeustPrarms) {
  const requestUrl = `${request.url}${request.params ? `?${new URLSearchParams(request.params).toString()}` : ''}`
  console.log(request)
  return fetch(requestUrl, {
    method: request.method,
    headers: request.headers,
    credentials: 'include',
    body: request.data,
  })
}

async function handleCommonReqest(request: ReqeustPrarms, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) {

  if (request.url.indexOf("www.linkedin.com") !== -1) {
    const cookie = await getCookies(request.url, "JSESSIONID")
    request.headers['csrf-token'] = cookie?.value.replaceAll("\"", "")
  }

  const res = await fetchJson(request)
  const response = await res.json()
  sendResponse({ status: 'success', data: { data: response } })
}

async function handleCheckPluginActive(request: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) {
  sendResponse({ status: 'success' })
}

async function handleSetLocalCache(request: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) {
  const data = await chrome.storage.local.set(request)
  sendResponse({ status: 'success', data })
}

async function handleGetLocalCache(request: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) {
  const data = await chrome.storage.local.get(request)
  sendResponse({ status: 'success', data })
}

async function handleClearLocalCache(request: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) {
  const data = await chrome.storage.local.clear();
  sendResponse({ status: 'success', data })
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message && message.callName) {

    const callName = message.callName.split(':')
    const callType = callName[0]
    const callMethod = callName[1]
    const request = message.data

    if (callType === 'browse_plugin') {
      if (callMethod === 'request') {
        handleCommonReqest(request as ReqeustPrarms, sender, sendResponse)
      } else if (callMethod === 'checkPluginActive') {
        handleCheckPluginActive(request as any, sender, sendResponse)
      } else if (callMethod === "setLocalCache") {
        handleSetLocalCache(request, sender, sendResponse)
      } else if (callMethod === "getLocalCache") {
        console.log(request)
        handleGetLocalCache(request, sender, sendResponse)
      } else if (callMethod === "clearLocalCache") {
        handleClearLocalCache(request, sender, sendResponse)
      } else {
        sendResponse({ status: 'error', data: '不支持的类型' + callMethod })
      }
    }
  }
  return true
})

addRequestRules([
  {
    id: 2,
    priority: 1,
    action: {
      type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
      requestHeaders: [
        {
          header: 'Origin',
          value: 'https://h.liepin.com',
          operation: chrome.declarativeNetRequest.HeaderOperation.SET
        }
      ],
    },
    condition: {
      urlFilter: '*.liepin.*',
    }
  }
])

// 阻止 zhipin security 相关脚本加载
async function blockZhipinSecurityScripts() {
  await chrome.declarativeNetRequest.updateDynamicRules({
    addRules: [
      {
        id: 1001,
        priority: 1,
        action: { type: chrome.declarativeNetRequest.RuleActionType.BLOCK },
        condition: {
          urlFilter: "https://static.zhipin.com/zhipin-boss/security",
          resourceTypes: [chrome.declarativeNetRequest.ResourceType.SCRIPT]
        }
      },
      {
        id: 1002,
        priority: 1,
        action: { type: chrome.declarativeNetRequest.RuleActionType.BLOCK },
        condition: {
          urlFilter: "risk-detection.js",
          resourceTypes: [chrome.declarativeNetRequest.ResourceType.SCRIPT]
        }
      }
    ],
    removeRuleIds: [1001, 1002]
  });
}

blockZhipinSecurityScripts();

// 定时执行的方法
function logCurrentTime() {
  const now = new Date();
  console.log('Current time:', now.toLocaleString());
}

// 每10秒执行一次
setInterval(logCurrentTime, 10000);